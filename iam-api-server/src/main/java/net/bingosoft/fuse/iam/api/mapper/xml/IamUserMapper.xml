<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.bingosoft.fuse.iam.api.mapper.IamUserMapper">
    <resultMap id="userListApiVo" type="net.bingosoft.fuse.iam.api.vo.UsersApiVo">
        <result property="id" column="id_"></result>
        <result property="name" column="name_"></result>
        <result property="code" column="code_"></result>
        <result property="salesCode" column="sales_code"></result>
        <result property="staffType" column="staff_type"></result>
        <result property="username" column="username_"></result>
        <result property="oaName" column="oa_name"></result>
        <result property="adShowName" column="ad_show_name"></result>
        <result property="email" column="email_"></result>
        <result property="emailInternet" column="email_internet"></result>
        <result property="otherEmail" column="other_email"></result>
        <result property="py" column="py_"></result>
        <result property="pinyin" column="pinyin_"></result>
        <result property="orgId" column="org_id"></result>
        <result property="email" column="email_"></result>
        <result property="mobile" column="mobile_"></result>
        <result property="mobile2" column="mobile2_"></result>
        <result property="mobile3" column="mobile3_"></result>
        <result property="phoneNum" column="phone_number"></result>
        <result property="phone" column="phone"></result>
        <result property="directPhone" column="direct_phone"></result>
        <result property="erpId" column="erp_id"></result>
        <result property="sex" column="sex_"></result>
        <result property="workPlace" column="work_place"></result>
        <result property="manager" column="manager"></result>
        <result property="admin" column="admin"></result>
        <result property="adminId" column="admin_id"></result>
        <result property="company" column="company"></result>
        <result property="postalCode" column="postal_code"></result>
        <result property="orgName" column="org_name"></result>
        <result property="oaOrgId" column="oa_org_id"></result>
        <result property="adOrgId" column="ad_org_id"></result>
        <result property="acmUserId" column="acm_user_id"></result>
        <result property="oaUserId" column="oa_user_id"></result>
        <result property="adUserId" column="external_id"></result>
        <result property="updateAt" column="updated_at"></result>
        <result property="createAt" column="created_at"></result>
        <result property="enableDate" column="enable_date"></result>
        <result property="enabled" column="enabled_"></result>
        <result property="position" column="position"></result>
        <result property="description" column="description"></result>
        <result property="employeeRoleName" column="employee_role_name"></result>
        <result property="emailUserServiceLevel" column="email_user_service_level"></result>
        <result property="oaHide" column="oa_hide"></result>
    </resultMap>
    <resultMap id="userOneApiVo" type="net.bingosoft.fuse.iam.api.vo.UserApiVo">
        <result property="id" column="id_"></result>
        <result property="name" column="name_"></result>
        <result property="code" column="code_"></result>
        <result property="salesCode" column="sales_code"></result>
        <result property="staffType" column="staff_type"></result>
        <result property="username" column="username_"></result>
        <result property="py" column="py_"></result>
        <result property="email" column="email_"></result>
        <result property="mobile" column="mobile_"></result>
        <result property="phoneNum" column="phone_number"></result>
        <result property="phone" column="phone"></result>
        <result property="directPhone" column="direct_phone"></result>
        <result property="erpId" column="erp_id"></result>
        <result property="workPlace" column="workPlace"></result>
        <result property="manager" column="manager"></result>
        <result property="postalCode" column="postal_code"></result>
        <result property="orgName" column="org_name"></result>
        <result property="oaOrgId" column="oa_org_id"></result>
        <result property="adOrgId" column="ad_org_id"></result>
        <result property="oaUserId" column="oa_user_id"></result>
        <result property="adUserId" column="external_id"></result>
        <result property="updateAt" column="updated_at"></result>
        <result property="createAt" column="created_at"></result>
        <collection property="otherOrgs" ofType="net.bingosoft.fuse.iam.api.vo.UserOtherOrgsVo">
            <result property="orgId" column="other_org_id"></result>
            <result property="oaOrgId" column="other_oa_org_id"></result>
            <result property="adOrgId" column="other_ad_org_id"></result>
        </collection>
    </resultMap>
    <insert id="createUser">

    </insert>
    <insert id="createOaUser">
        INSERT INTO iam_user (id_, name_, code_, sort_order, sort_order2, avatar_, username_, password_,
                                      password_status, type_, user_kind, org_id, ref_id, restricted_, job_title, deleted_,
                                      gender_, phone_number, mobile_, mobile2_, mobile3_, email_, inbound_org_id, enabled_,
                                      external_id, expired_at, privileged_, alias_, hidden_, pinyin_, py_, id_num, secret_level,
                                      extended_, desc_, p_sort_order, contact_id, tenant_id, created_at, created_by, updated_at,
                                      updated_by, staff_type, oa_name, mailbox_capacity, email_internet, other_email,
                                      ad_show_name, enable_date, company, interface_by, email_user_service_level,
                                      disable_access_to_oa, employee_role_name, phone, description, dp_id, dp_name, erp_id,
                                      position, work_place, manager, postal_code, direct_phone, acm_user_id, oa_user_id,
                                      deleted_date, admin,admin_id, bind_dn)
        VALUES (UUID(), #{name}, null, 100, 100, null, #{username},
                '{MD5-BASE64}0jeH9M/7fsXFDObey/EMtA==', 'INI', 'N', 'P',
                (select a.id_ from(select id_ from  iam_org where name_ = #{orgName} limit 1) as a), null, false, null, false, 'U', null, null, null, null, concat(#{username},'@gfdev.com'), null, true,
                null, null, false, null, false, #{username}, null, null, 0, null, null, null, null,
                'J9364cyRHGCmCfBXn5jc83', now(), 'ioa', now(), 'ioa', 'OA|AD|mailbox|hk', concat(#{name},'/GFZQ'),
                10, concat(#{username},'@oa.gf.com.cn'), null, #{name}, now(), '广发证券股份有限公司', null, '10', null, '正式员工', null, null, null,
                null, #{erpId}, null, null, null, null, null, null, null, null, 'qinwj','qinwj',
                null);
    </insert>
    <insert id="createUserOrg">
        insert into iam_user_org(org_id, user_id, sort_order, created_at, created_by, `primary`, oa_order, ad_order)
        values ((select a.id_ from(select id_ from  iam_org where name_ = #{orgName} limit 1) as a),
                (select a.id_ from(select id_ from  iam_user where username_ = #{username} limit 1) as a),100,
                now(),'ioa',true,100000,100000);
    </insert>
    <insert id="createUserStaffType">
        insert into iam_user_staff_type(user_id, type) VALUES
        <foreach collection="staffTypes" item="staffType" separator=",">
        (#{userId},#{staffType})
        </foreach>
    </insert>
    <update id="updateUserOAId">
        update iam_user set oa_user_id = id_  where username_ = #{username};
    </update>
    <update id="updateBindDnByUsername">
        update iam_user iu
            left join iam_org io on iu.org_id = io.id_
            set iu.bind_dn = concat('CN=', iu.name_, ',', io.bind_dn)
        where username_ = #{username}
    </update>
    <delete id="deleteStaffType">
        delete from iam_user_staff_type where user_id = #{userId}
    </delete>
    <select id="select" resultType="Object">
        select * from iam_user;
    </select>
    <select id="getUsersListByStaffTypeAndEnable" resultMap="userListApiVo">
        select u.id_,u.sex_, u.name_, u.code_, u.sales_code,u.sort_order, u.sort_order2, u.avatar_, u.username_, u.password_, u.nt_password, u.password_status,
               u.type_, u.user_kind, u.org_id, u.ref_id, u.restricted_, u.job_title, u.deleted_, u.gender_, u.phone_number, u.mobile_, u.mobile2_,
               u.mobile3_, u.email_, u.inbound_org_id, u.enabled_, u.external_id, u.expired_at, u.privileged_, u.alias_, u.hidden_, u.pinyin_, u.py_,
               u.id_num, u.secret_level, u.extended_, u.desc_, u.p_sort_order, u.contact_id, u.tenant_id,  u.created_by,u.oa_hide,
               u.updated_by, u.staff_type, u.oa_name, u.mailbox_capacity, u.email_internet, u.other_email, u.ad_show_name, u.enable_date, u.company,
               u.interface_by, u.email_user_service_level, u.disable_access_to_oa, u.employee_role_name, u.phone, u.description, u.dp_id, u.dp_name,
               u.erp_id, u.position, u.work_place, u.manager, u.postal_code, u.direct_phone, u.acm_user_id, u.oa_user_id, u.deleted_date, u.admin,u.admin_id, u.bind_dn,
               u.extend_object_class,  u.created_at,u.updated_at,o.name_ org_name,
               o.external_id org_ad_id,o.oa_org_id oa_org_id
        from iam_user u
                 left join iam_org o on u.org_id = o.id_
        <if test="!isAdmin">
                 inner join (select *
                            from iam_user_staff_type iust
                            where iust.type in
                            <foreach collection="userType" item="item" open="(" separator="," close=")">
                                 #{item}
                            </foreach> )
                             iust on iust.user_id = u.id_
        </if>
        where 1=1
        <if test="!isAdmin">
           <if test="isOA">
           and u.oa_user_id is not null
           </if>
           <if test="isAD">
               and u.external_id is not null
           </if>
            <if test="authOrg != null ">
                and(
                <foreach collection="authOrg" item="item" separator="or">
                    (
                    (o.code_ like concat('${item}',',%')) and 1 = 1

                   )
                </foreach>
                )
            </if>
        </if>
       <if test="isEnable !=null ">
           and u.enabled_ = #{isEnable}
       </if>
        <if test="username !=null and username !='' ">
           and u.username_ = #{username}
       </if>
          <if test="createDate != null and createDate !=''">
              and DATE_FORMAT(u.created_at, '%Y-%m-%d') = #{createDate}
          </if>
        and u.deleted_  = false
        group by u.username_
        order by u.username_
            limit #{start},#{limit}
    </select>
    <select id="getUserDetailsByUserName" resultMap="userOneApiVo">
        select u.id_, u.name_, u.code_,  u.sales_code,u.sort_order, u.sort_order2, u.avatar_, u.username_, u.password_, u.nt_password, u.password_status, u.type_, u.user_kind, u.org_id,
               u.ref_id, u.restricted_, u.job_title, u.deleted_, u.gender_, u.phone_number, u.mobile_, u.mobile2_, u.mobile3_, u.email_, u.inbound_org_id, u.enabled_,
               u.external_id, u.expired_at, u.privileged_, u.alias_, u.hidden_, u.pinyin_, u.py_, u.id_num, u.secret_level, u.extended_, u.desc_, u.p_sort_order,
               u.contact_id, u.tenant_id, u.created_by, u.updated_by, u.staff_type, u.oa_name, u.mailbox_capacity, u.email_internet,
               u.other_email, u.ad_show_name, u.enable_date, u.company, u.interface_by, u.email_user_service_level, u.disable_access_to_oa, u.employee_role_name,
               u.phone, u.description, u.dp_id, u.dp_name, u.erp_id, u.position, u.work_place, u.manager, u.postal_code,
               u.direct_phone, u.acm_user_id, u.oa_user_id, u.deleted_date, u.admin,u.admin_id, u.bind_dn, u.extend_object_class, IFNULL(u.acm_created_at,u.created_at) created_at,
               IFNULL(u.acm_updated_at,u.updated_at) updated_at,
               o.name_ org_name,o.external_id org_ad_id,o.oa_org_id oa_org_id,o1.id_ other_org_id,o1.external_id other_ad_org_id,o1.oa_org_id other_oa_org_id
        from iam_user u
                 left join iam_org o on u.org_id = o.id_
                 left join iam_user_org iuo on iuo.user_id = u.id_
                 left join iam_org o1 on iuo.org_id = o1.id_ and iuo.`primary` = false
        where u.deleted_ = false
          and (
                <foreach collection="authOrgRootList" item="authOrgCode" separator="or">
                    (o.code_ like concat(#{authOrgCode},',%'))
                </foreach>
            )
          and username_ = #{userName};
    </select>
    <select id="getAdOrderAfterAdOrder" >
        select adOrder from iam_user_org where `primary` = true
        and org_id = #{orgId}
        and ad_order &lt; #{adOrder}
        order by  ad_order desc limit 1
    </select>
    <select id="exists" resultType="java.lang.Integer">
        select count(*) from iam_user
                                 left join iam_org on iam_user.org_id = iam_org.id_
        where iam_org.code_ like concat(#{code},'%')
              and iam_user.deleted_ = false
    </select>
    <select id="checkUserDuplicateInOrg" resultType="java.lang.Integer">
        select count(*) from iam_user where org_id = #{orgId} and name_ = #{name}
    </select>
</mapper>
