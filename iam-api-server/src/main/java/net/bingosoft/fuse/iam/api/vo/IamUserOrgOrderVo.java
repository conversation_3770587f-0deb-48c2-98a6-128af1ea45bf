package net.bingosoft.fuse.iam.api.vo;

import fly.core.meta.annotation.Summary;
import lombok.Data;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;

import java.util.List;

@Data
public class IamUserOrgOrderVo {
    @Summary("组织id")
    String orgId;
    /*
    * 用于更新acm
    * */
    @Summary("组织acmId")
    String acmOrgId;

    @Summary("组织名称")
    String orgName;

    @Summary("排序选项 0：排在最上  1：排在最下  2：排在谁之后")
    protected Integer sortOptions;

    @Summary("排序选择2的时候，参照的用户")
    protected String sortUserId;

    @Summary("组织内的排序")
    protected List<String> userId;

    @Summary("组织内的排序（更改后的用户名称）")
    protected List<IamUserEntity> userNames;
}
