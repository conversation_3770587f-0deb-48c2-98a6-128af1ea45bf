package net.bingosoft.fuse.iam.api.client.model;

import fly.core.meta.annotation.Summary;
import net.bingosoft.fuse.commons.core.data.entities.AbstractCreatedEntity;

import java.util.List;

public class IamClientDataAuthzDetailVo extends AbstractCreatedEntity {
    @Summary("客户端数据授权id")
    String clientDataId;

    @Summary("组织id")
    String orgId;

    @Summary("授权类型Id")
    List<String> typeIds;

    @Summary("用户授权类型id")
    List<String> userTypeIds;

    @Summary("用户授权类型id")
    List<String> orgTypeIds;

    @Summary("用户类型")
    List<String> type;

    public String getClientDataId() {
        return clientDataId;
    }

    public void setClientDataId(String clientDataId) {
        this.clientDataId = clientDataId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public List<String> getTypeIds() {
        return typeIds;
    }

    public void setTypeIds(List<String> typeIds) {
        this.typeIds = typeIds;
    }

    public List<String> getType() {
        return type;
    }

    public void setType(List<String> type) {
        this.type = type;
    }

    public List<String> getUserTypeIds() {
        return userTypeIds;
    }

    public void setUserTypeIds(List<String> userTypeIds) {
        this.userTypeIds = userTypeIds;
    }

    public List<String> getOrgTypeIds() {
        return orgTypeIds;
    }

    public void setOrgTypeIds(List<String> orgTypeIds) {
        this.orgTypeIds = orgTypeIds;
    }
}
