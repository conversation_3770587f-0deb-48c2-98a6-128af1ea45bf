package net.bingosoft.fuse.iam.api.acm.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户emp 新增修改传参对象
 * 创建OA、AD、邮箱、香港用户
 * <AUTHOR>
 * @Date 2022/6/7
 */
@Data
public class AcmUserEmpDto {
    /**
     * 唯一标识ID
     */
    private String id;
    /**
     * 中文名
     */
    private String name;
    /**
     * 创建OA、AD、邮箱、香港用户
     * 创建类型(AD|OA|mailbox|hk)
     */
    /**
     * OA用户名
     * 张三/GFZQ
     * 新建OA用户必填
     */
    private String oaName;
    /**
     * 登录名
     */
    private String account;
    /**
     * 显示名
     * 例如：张三（信息技术部运营服务支持组）
     */
    private String displayName;
    /**
     * 简拼
     */
    private String simpleSpell;
    /**
     * 全拼
     */
    private String fullSpell;
    /**
     * 密码
     */
    private String password;
    /**
     * 主部门
     */
    private AcmUserOrgDto parentOrg;
    /**
     * 所属其他群组
     */
    private List<AcmUserOrgDto> otherOrgs;
    /**
     * 邮箱容量(5|10|15|20|25|30)
     * 新建邮箱用户必填
     */
    private String mailBoxCapacity;
    /**
     * 其他邮件地址
     * 多个邮件地址半角逗号“,”分隔
     */
    private String otherEmail;
    /**
     * 角色
     * 合作方员工|公共账号|正式员工|营销员工|劳务派遣|供应商驻场|实习生
     */
    private String employeeRoleName;
    /**
     * 失效时间
     * (AD|AD+邮箱)时必填
     */
    private String whenFailure;
    /**
     * 禁止访问OA门户
     * 默认是不禁用
     * 禁用请输入http://oa.gf.com.cn
     */
    private String disableAccessToOA;
    /**
     * 公司
     * 默认是广发证券股份有限公司
     */
    private String company;
    /**
     * 备注
     */
    private String description;
    /**
     * ERP号
     */
    private String number;
    /**
     * 管理者字段
     */
    private String superintendent;
    /**
     * 岗位
     */
    private String workPostion;
    /**
     * 办公电话
     */
    private String telephoneNumber;
    /**
     * 移动电话
     */
    private String phoneNum;
    /**
     * 工作地址
     */
    private String workPlace;
    /**
     * 上级经理
     */
    private String manager;
    /**
     * 邮政编码
     */
    private String postCode;
    /**
     * 分机号
     */
    private String otherPhoneNum;

    private String sysType;
}
