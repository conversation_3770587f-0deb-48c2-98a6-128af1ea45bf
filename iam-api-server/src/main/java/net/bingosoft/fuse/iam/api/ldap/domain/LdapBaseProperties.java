package net.bingosoft.fuse.iam.api.ldap.domain;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Data
@Configuration
@PropertySource("classpath:gfprotal.uum.properties")
@ConfigurationProperties(prefix = "ldap-base")
public class LdapBaseProperties {
	private String ldapUrl;
	private String ldapPort;
	private String basicDn;
	private String binder;
	private String binderPassword;
//	private String adBasicDn;
//	private String adLdapUrl;
//	private String adBinder;
//	private String adBinderPassword;
}
