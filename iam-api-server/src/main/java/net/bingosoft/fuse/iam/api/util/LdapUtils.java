package net.bingosoft.fuse.iam.api.util;

import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.constant.Constants;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.InputStream;
import java.util.Properties;


/**
 * <AUTHOR>
 * 
 */
@Slf4j
public class LdapUtils {
	private static LdapUtils _ldapUtils = null;

	private LdapUtils() {
	}
	
	public static synchronized LdapUtils Instance() {
		if (null == _ldapUtils) {
			_ldapUtils = new LdapUtils();
		}
		return _ldapUtils;
	}
	
	private static Properties properties = null;
	private static Properties usermappingproperties = null;
	private static Properties deptmappingproperties = null;
	private static Properties filterproperties = null;

	// private static LDAPConnection ldapConn=null;



	//获取与Domino Ldap 用户字段映射配置文件
	public static Properties GetUserMappingProperties() throws Exception {
		try {
			if (usermappingproperties == null) {
				/*InputStream inputStream = new LdapUtils().getClass()
						.getClassLoader().getResourceAsStream(
								Constants.GF_UUM_USER_MAPPING_FILEPATH);*/
				File file = new File("../portal-syn-web/config/"+ Constants.GF_UUM_USER_MAPPING_FILEPATH);
				//File file = new File("../"+ldap_file_path+"/"+Constants.GF_UUM_USER_MAPPING_FILEPATH);
				Resource fileRource = new FileSystemResource(file);
				InputStream inputStream = fileRource.getInputStream();
				usermappingproperties = new Properties();
				usermappingproperties.load(inputStream);
			}
		} catch (Exception ex) {
			throw new Exception("Can not load uum config file. ex msg:"
					+ ex.getMessage());
		}
		return usermappingproperties;
	}// end

	//获取与Domino Ldap 部门字段映射配置文件
	public Properties GetDeptMappingProperties() throws Exception {
		try {
			if (deptmappingproperties == null) {
				/*InputStream inputStream = new LdapUtils().getClass()
						.getClassLoader().getResourceAsStream(
								Constants.GF_UUM_DEPT_MAPPING_FILEPATH);*/
				File file = new File("../portal-syn-web/config/"+Constants.GF_UUM_DEPT_MAPPING_FILEPATH);
				//File file = new File("../"+ldap_file_path+"/"+Constants.GF_UUM_DEPT_MAPPING_FILEPATH);
				Resource fileRource = new FileSystemResource(file);
				InputStream inputStream = fileRource.getInputStream();
				deptmappingproperties = new Properties();
				deptmappingproperties.load(inputStream);
			}
		} catch (Exception ex) {
			throw new Exception("Can not load uum config file. ex msg:"
					+ ex.getMessage());
		}
		return deptmappingproperties;
	}// end

	//获取Domino Ldap 查询条件配置文件
	public Properties GetLDAPFilterProperties() throws Exception {
		try {
			if (filterproperties == null) {
				/*InputStream inputStream = new LdapUtils().getClass()
						.getClassLoader().getResourceAsStream(
								Constants.GF_UUM_LDAP_FILTER_FILEPATH);*/
				File file = new File("../portal-syn-web/config/"+Constants.GF_UUM_LDAP_FILTER_FILEPATH);
				//File file = new File("../"+ldap_file_path+"/"+Constants.GF_UUM_LDAP_FILTER_FILEPATH);
				Resource fileRource = new FileSystemResource(file);
				InputStream inputStream = fileRource.getInputStream();
				filterproperties = new Properties();
				filterproperties.load(inputStream);
			}
		} catch (Exception ex) {
			throw new Exception("Can not load uum config file. ex msg:"
					+ ex.getMessage());
		}
		return filterproperties;
	}// end

	//根据key 获取Domino Ldap用户查询条件

	
	
	//根据key 获取Domino Ldap部门查询条件
















	
}
