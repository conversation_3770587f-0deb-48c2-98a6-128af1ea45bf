package net.bingosoft.fuse.iam.api.client.vo;

import fly.core.meta.annotation.Summary;

import javax.validation.Valid;
import javax.validation.constraints.Size;

@Valid
public class IamClientDataAuthzVo {


    @Summary("客户端Id")
    protected String clientId;
    @Summary("客户端名称")
    protected String clientName;
    @Summary("每日请求数量")
    protected Integer requestNum;
    @Summary("每秒请求频率")
    protected Integer frequency;
    @Summary("联系人")
    protected String contacts;
    @Summary("联系人")
    protected String contactsBy;

    @Summary("client-id的结尾部分")
    @Size(
            max = 13
    )
    protected String idSuffix;
    @Summary("是否在前端注销时传递sid参数")
    protected boolean frontChannelLogoutSessionRequired;
    @Summary("是否支持check_session")
    protected boolean sessionStateRequired;
    @Summary("启用IAM内置权限")
    protected boolean enableIamScope = true;
    @Summary("是否启用")
    protected boolean enabled = true;
    @Summary("状态")
    protected boolean status;

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public Integer getRequestNum() {
        return requestNum;
    }

    public void setRequestNum(int requestNum) {
        this.requestNum = requestNum;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactsBy() {
        return contactsBy;
    }

    public void setContactsBy(String contactsBy) {
        this.contactsBy = contactsBy;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public void setRequestNum(Integer requestNum) {
        this.requestNum = requestNum;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    public String getIdSuffix() {
        return idSuffix;
    }

    public void setIdSuffix(String idSuffix) {
        this.idSuffix = idSuffix;
    }

    public boolean isFrontChannelLogoutSessionRequired() {
        return frontChannelLogoutSessionRequired;
    }

    public void setFrontChannelLogoutSessionRequired(boolean frontChannelLogoutSessionRequired) {
        this.frontChannelLogoutSessionRequired = frontChannelLogoutSessionRequired;
    }

    public boolean isSessionStateRequired() {
        return sessionStateRequired;
    }

    public void setSessionStateRequired(boolean sessionStateRequired) {
        this.sessionStateRequired = sessionStateRequired;
    }

    public boolean isEnableIamScope() {
        return enableIamScope;
    }

    public void setEnableIamScope(boolean enableIamScope) {
        this.enableIamScope = enableIamScope;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
