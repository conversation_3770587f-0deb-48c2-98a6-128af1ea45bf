package net.bingosoft.fuse.iam.api.constant;

public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String USER_PASSWORD_LOCK_PREFIX = "password.lock:";

    /**
     * GBK 字符集
     */
    public static final String USER_PASSWORD_LOCK_TIME = "iam.password.delay";
    //LDAP Connect Attribute Key
    public static final String GF_UMM_CONFIG_FILEPATH="gfprotal.uum.properties";
    public static final String GF_UUM_USER_MAPPING_FILEPATH="gfprotal.uum.user.mapping.properties";
    public static final String GF_UUM_DEPT_MAPPING_FILEPATH="gfprotal.uum.dept.mapping.properties";
    public static final String GF_UUM_LDAP_FILTER_FILEPATH="gfprotal.uum.filter.properties";
    public static final String GF_DBCONN_CONFIG="db.conf";
    public static final String GF_UUM_LDAP_URL_KEY="gfprotal.uum.ldapurl";

    public static final String GF_UUM_LDAP_PORT_KEY="gfportal.uum.ldapport";

    public static final String GF_UUM_LDAP_BASIC_DN_KEY="gfportal.uum.basic.dn";

    public static final String GF_UMM_LDAP_BINDER_KEY="gfportal.uum.binder";

    public static final String GF_UUM_LDAP_BINDER_PASSWORD_KEY="gfportal.uum.binderpassword";

    //LDAP User Attribute Key
    public static final String GF_LADP_USER_ATTR_GUID="fdu-personunid";

    public static final String GF_LADP_USER_ATTR_UID="uid";

    public static final String GF_LADP_USER_ATTR_NAME="cn";

    public static final String GF_LADP_USER_ATTR_DISPLAYNAME="displayname";

    public static final String GF_LADP_USER_ATTR_DEPTID="department";

    public static final String GF_LADP_USER_ATTR_DEPTID2="department2";

    public static final String GF_LADP_USER_ATTR_DEPTTYPE="depttype";

    public static final String GF_LADP_USER_ATTR_DEPTNAME="fdu-deptname";

    public static final String GF_LADP_USER_ATTR_EMAIL="mail";

    public static final String GF_LADP_USER_ATTR_MOBILE="mobile";

    public static final String GF_LADP_USER_ATTR_TELEPHONE="telephonenumber";

    public static final String GF_LADP_USER_ATTR_PARENTNO="parentno";

    public static final String GF_LADP_USER_ATTR_NEWNO="newno";

    public static final String GF_LADP_USER_ATTR_PINGYING="fdu-py";

    public static final String GF_LADP_USER_ATTR_TEMPUSUER="tempuser";
    //mail server
    public static final String GF_LDAP_USER_ATTR_MAILSERVER="mailserver";

    public static final String GF_LDAP_USER_ATTR_MAILFILE="mailfile";

    public static final String GF_LDAP_USER_ATTR_OU="ou";

    public static final String GF_LDAP_USER_ATTR_COMMENT="Comment";

    public static final String GF_LDAP_USER_ATTR_LEVEL0="Level0";

    //LDAP Dept Attribute Key
    public static final String GF_LADP_DEPT_ATTR_GUID="fdu-personunid";

    public static final String GF_LADP_DEPT_ATTR_DEPTID="no";

    public static final String GF_LADP_DEPT_ATTR_DEPTPARTENTID="fdu-parentid";

    public static final String GF_LADP_DEPT_ATTR_DESCRIPTION="description";

    public static final String GF_LADP_DEPT_ATTR_DEPT_DISPLAYNAME="displayname";

    public static final String GF_LADP_DEPT_ATTR_PARENETDEPT_DISPLAYNAME="fdu-parentname";

    public static final String GF_LADP_DEPT_ATTR_DEPTTYPE="depttype";

    public static final String GF_LADP_DEPT_ATTR_FDU_DEPTTYPE="fdu-type";

    public static final String GF_LADP_DEPT_ATTR_DEPT_GRADE="fdu-grade";

    public static final String GF_LADP_DEPT_ATTR_PINGYING="fdu-py";

    public static final String GF_LADP_DEPT_ATTR_PARENTNO="parentno";

    public static final String GF_LADP_DEPT_ATTR_FDUYXPTNO="fdu-yxptno";

    public static final String GF_LADP_DEPT_ATTR_NEWNO="NewNo";


    //available for sync
    public static final String GF_LADP_AVAILABLE_SYNC="availablefordirsync";

    //User filter
    public static final String GF_LDAP_USER_FILTER_FINDALL_KEY="findall";

    //Dept filter

    public static final String GF_LDAP_DEPT_FILTER_FINDALL_KEY="findall";

    public static final String GF_LDAP_DEPT_FILTER_FINDALL_WITH_GROUP_KEY="findallwithgourp";

    public static final String GF_LDAP_DEPT_FILTER_FIND_TEMP_OU_KEY="findtmpou";

    public static final String GF_AD_FIND_ALL_WITHOUT_GROUP = "AD_findallWithoutGourp";

    public static final String GF_AD_FIND_ALL_GROUP = "AD_findAllGroup";

    //others
    public static final String GF_DEPT_ROOT_KEY="dproot";

    public static String SYNCHRCONFIGKEY = "erp.sync";

    public static String DOMINOSYNCCONFIGKEY = "domino.sync";
    public static String HRSENDMAIL = "hr.send.mail";

    public static String HREDIT = "hr.edit.username";

}
