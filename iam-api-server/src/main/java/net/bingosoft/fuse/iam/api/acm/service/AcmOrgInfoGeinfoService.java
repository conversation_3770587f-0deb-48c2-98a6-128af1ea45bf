package net.bingosoft.fuse.iam.api.acm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import fly.core.data.model.RowMap;
import fly.orm.dao.entity.AbstractEntityOperations;
import net.bingosoft.fuse.iam.api.acm.dto.ResultMsg;
import net.bingosoft.fuse.iam.api.acm.vo.GeninfoErpOaDept;
import net.bingosoft.fuse.iam.api.acm.vo.GeninfoOrgInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class AcmOrgInfoGeinfoService extends AbstractEntityOperations<GeninfoOrgInfo,GeninfoOrgInfo> {
    @Value("${geninfo.server-url}")
    private String acmHostUrl;
    @Value("${geninfo.appId}")
    private String appId;

    private static final Logger log = LoggerFactory.getLogger(AcmOrgInfoGeinfoService.class);

    /*
     * acm-geninfo 获取组织信息
     * */
    public List<GeninfoOrgInfo> getOrgInfo(String acmId, String simpleSpell){
        String url = acmHostUrl + "/secinfo/api/acm/queryOrganization?appId="+appId;
        if( StrUtil.isNotEmpty(acmId) )
            url += "&acmId="+acmId;
        if( StrUtil.isNotEmpty(simpleSpell) )
            url += "&simpleSpell="+simpleSpell;
        //调用修改用户接口地址
        String resultStr = HttpUtil.get(url);
        ResultMsg result = null;
        try {
            result = JSONUtil.toBean(resultStr, ResultMsg.class);
            if(result.getCode()==1){
                throw new RuntimeException("获取ldap用户信息失败");
            }
        }catch (Exception e){
            log.error("acm-geninfo接口调用失败");
        }
        return JSONUtil.toList(result.getData(),GeninfoOrgInfo.class);
    }

    public List<RowMap> getGenInfoOrgList(){
        return dao.createSQLQuery("select * from acm_geninfo_org").list();
    }

    public String getGenInfoParentId(){
        String acmId = dao.createSQLQuery("select acm_id from acm_geninfo_org where name = ? ", GeninfoOrgInfo.class).args("广发证券股份有限公司").first().getAcmId();
        return acmId;
    }

    public String getGenInfoErpOaDeptByOrgId(String orgId){
        Assert.isTrue(StrUtil.isNotEmpty(orgId),"orgId不能为空");
        String url = acmHostUrl + "/secinfo/api/ioa/selectErpOaDeptByOrgId?appId="+appId+"&orgId="+orgId;
        //调用获取
        String resultStr = HttpUtil.get(url);
        ResultMsg result = null;
        String resp = "";
        try {
            result = JSONUtil.toBean(resultStr, ResultMsg.class);
            if(result.getCode()==1){
                throw new RuntimeException("获取selectErpOaDeptByOrgId信息失败");
            }
            List<GeninfoErpOaDept> erpOaDeptList = Optional.ofNullable(result.getData())
                    .map(array -> array.toList(GeninfoErpOaDept.class))
                    .orElse(Collections.emptyList());
            if (CollectionUtil.isNotEmpty(erpOaDeptList)){
                return Optional.ofNullable(erpOaDeptList.get(0)).map(GeninfoErpOaDept::getDp_id).orElse("");
            }
        }catch (Exception e){
            log.error("GenInfo：selectErpOaDeptByOrgId接口调用失败");
        }
        return resp;
    }
}
