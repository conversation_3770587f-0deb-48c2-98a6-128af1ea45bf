package net.bingosoft.fuse.iam.api.acm.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import fly.core.data.model.RowMap;
import fly.orm.dao.entity.AbstractEntityOperations;
import net.bingosoft.fuse.iam.api.acm.dto.ResultMsg;
import net.bingosoft.fuse.iam.api.acm.vo.GeninfoEmpInfo;
import net.bingosoft.fuse.iam.api.acm.vo.GeninfoEmpInfoEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AcmUserInfoGeninfoService extends AbstractEntityOperations<GeninfoEmpInfoEntity, GeninfoEmpInfo> {
    @Value("${geninfo.server-url}")
    private String acmHostUrl;
    @Value("${geninfo.appId}")
    private String appId;
    private static final Logger log = LoggerFactory.getLogger(AcmUserInfoGeninfoService.class);

    /*
     * 获取ldap用户
     * */
    public List<GeninfoEmpInfo> getEmpInfo(String loginId){
        String url = acmHostUrl + "/secinfo/api/acm/queryEmployee?appId="+appId;
        if( StrUtil.isNotEmpty(loginId) )
            url += "&loginId="+loginId;
        //调用修改用户接口地址
        String resultStr = HttpUtil.get(url);
        ResultMsg result = null;
        try {
            result = JSONUtil.toBean(resultStr, ResultMsg.class);
            if(result.getCode()==1){
                throw new RuntimeException("获取geninfo用户信息失败");
            }
        }catch (Exception e){
            log.error("acm-geninfo接口调用失败");
        }
        return JSONUtil.toList(result.getData(),GeninfoEmpInfo.class);
    }

    public List<RowMap> getGenInfoUserList(){
        return dao.createSQLQuery("select u.*,ou.organization_id,ou.is_primary from acm_geninfo_user u LEFT JOIN acm_geninfo_user_org ou ON u.acm_id = ou.employee_id").list();
    }
}
