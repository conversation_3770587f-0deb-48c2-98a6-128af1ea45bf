package net.bingosoft.fuse.iam.api.acm.dto;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class GeninfoEmpInfoVo {
    String acmId;
    String oaId;
    String adId;
    String displayName;
    String name;
    String eduBackground;
    String email;
    String phoneNum;
    String company;
    String manager;
    String workPosition;
    String account;
    boolean isAllowance;
    Timestamp whenCreated;
    Timestamp whenModified;
    String fullSpell;
    String simpleSpell;
    String postCode;
    String otherPhoneNum;
    String telePhoneNumber;
    String bankAccountNum;
    String otherEmail;
    String internetEmail;
    Timestamp effiectTime;
    String erpNum;
    String organizationId;
    String isPrimary;
    String createdBy;
    String updatedBy;
    String extendedFour;
    String extendedTwo;
    String emailUserServiceLevel;
    String employeeRoleName;
    String photo;
    String description;
    String workPlace;
    Timestamp whenFailure;
    String whenEffective;
}
