package net.bingosoft.fuse.iam.api.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import fly.core.data.model.RowMap;
import fly.core.meta.annotation.Summary;
import fly.core.security.annotation.ClientOnly;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.client.manager.IamClientDataLogManager;
import net.bingosoft.fuse.iam.api.constant.Constants;
import net.bingosoft.fuse.iam.api.log.annotation.ApiAccess;
import net.bingosoft.fuse.iam.api.mapper.IamUserMapper;
import net.bingosoft.fuse.iam.api.model.IamUser;
import net.bingosoft.fuse.iam.api.service.GfCommTenantConfigServiceImpl;
import net.bingosoft.fuse.iam.api.service.IamUserService;
import net.bingosoft.fuse.iam.api.service.MailSendService;
import net.bingosoft.fuse.iam.api.service.impl.IamRequestOperationManager;
import net.bingosoft.fuse.iam.api.service.impl.SyncUserDataService;
import net.bingosoft.fuse.iam.api.vo.*;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.common.password.PasswordManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 用户
 *
 * <AUTHOR>
 * @Date 2023/11/20 13:42
 */
@Tag(name = "user", description = "用户接口")
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {
    @Autowired
    SyncUserDataService syncUserService;
    @Autowired
    PasswordManager passwordManager;
    @Autowired
    IamUserManager iamUserManager;
    @Autowired
    IamUserMapper userMapper;
    @Value("${ioa.enable}")
    Boolean ioaEnable;
    @Autowired
    IamUserService iamUserService;
    @Resource
    RedisTemplate<String,String> redisTemplate;
    String lockPrefix = "user.password.lock.";
    @Autowired
    GfCommTenantConfigServiceImpl gfCommTenantConfigService;
    @Autowired
    IamClientDataLogManager iamClientDataLogManager;
    @Autowired
    MailSendService mailSendService;
    @Autowired
    IamRequestOperationManager iamRequestOperationManager;



    @Value("${ioa.publicKeyBase64}")
    public String publicKeyBase64;

    @ClientOnly
    @GetMapping("")
    @Summary("查询列表")
    @ApiAccess(name = "获取用户列表信息", code = "user::info")
    SyncDataResultVo list( @Summary("开始页 默认1") Integer start,
                           @Summary("每页数量 默认10") Integer limit,
                           @Summary("是否启用 true | false 默认查询全部") Boolean isEnable,
                           @Summary("用户类型 OA AD 默认查询全部") String type,
                           @Summary("用户账号")String username,
                           @Summary("创建日期 格式：YYYY-MM-DD") String createDate
    ) {
        if (ObjectUtil.isNull(start)) {
            start = 1;
        }
        if (ObjectUtil.isNull(limit)) {
            limit = 10;
        }
        return syncUserService.users("user::info", start, limit, isEnable, type, username,createDate);
    }

    @ClientOnly
    @GetMapping("/ad/{username}")
    @Summary("获取AD用户")
    SyncDataResultVo getAdUser(@Summary("用户账号") @PathVariable("username") String username) {
        if(StrUtil.isEmpty(username)) {
            return SyncDataResultVo.error("请传输用户名");
        }
        return syncUserService.adUsers(username);
    }

    @ClientOnly
    @GetMapping("/oa/{username}")
    @Summary("获取OA用户")
    SyncDataResultVo getOaUser(@Summary("用户账号") @PathVariable("username") String username) {
        if(StrUtil.isEmpty(username)) {
            return SyncDataResultVo.error("请传输用户名");
        }
        return syncUserService.oaUsers(username);
    }

    @ClientOnly
    @GetMapping("/{username}")
    @Summary("查询用户信息")
    @ApiAccess(name = "获取用户信息", code = "user::info")
    SyncDataResultVo get( @Summary("用户账号") @PathVariable("username") String userName,  @Summary("用户类型 OA AD 默认查询全部") String type) {
        if (StrUtil.isEmpty(userName)) {
            return SyncDataResultVo.error("userName不允许为空！");
        }
        return syncUserService.user("user::info", userName, type);
    }


    @ClientOnly
    @PostMapping("")
    @Summary("创建用户")
    @ApiAccess(name = "添加用户信息", code = "user::create")
    SyncDataResultVo create(@Summary("用户实体类") @RequestBody IamUserCreateVo iamUserCreateVo) {
        try {
           return syncUserService.create("user::create", iamUserCreateVo);
        }catch (Exception e){
            log.info("【创建用户】失败，失败原因"+e.getMessage(),e);
            return SyncDataResultVo.error("创建失败，失败信息为" + e.getMessage());
        }
    }

    @ClientOnly
    @PostMapping("createAccount")
    @Summary("智慧人力创建用户")
    @ApiAccess(name = "添加用户信息", code = "user::create")
    SyncDataResultVo hrCreate(@Summary("用户实体类") @RequestBody IamHrUserCreateDto iamUserCreateVo) {
        try {
            log.info("traceid:{}",iamUserCreateVo.getTraceId());
            List<RowMap> traceIds = iamClientDataLogManager.dao().createSQLQuery("select * from iam_client_data_log where trace_id = ?", iamUserCreateVo.getTraceId()).execute().list();
            if(ObjectUtil.isNotEmpty(traceIds)){
                RowMap rowMap = traceIds.get(0);
                if(rowMap.getInteger("status")==1||StrUtil.isEmpty(rowMap.getString("username"))) {
                    String string = rowMap.getString("transferReport");
                    log.info("上一次推送失败,失败信息{}",string);
                    return SyncDataResultVo.error(string);
                }else {
                    log.info("上一次推送成功");
                    String username = rowMap.getString("username");
                    String email = rowMap.getString("email");
                    IamHrUserVo data = new IamHrUserVo();
                    data.setUsername(username);
                    data.setEmail(email);
                    return SyncDataResultVo.success(data,"创建成功");
                }
            }else {
                IamUserCreateVo iamUserCreateVo1 = syncUserService.initUserDto(iamUserCreateVo);
                if(iamUserCreateVo1.getIsRequest()){
                    syncUserService.makeRequest(iamUserCreateVo1);
                    return SyncDataResultVo.success(null,"已提交it服务台草稿箱");
                }else {
                    return syncUserService.create("user::create", iamUserCreateVo1);
                }

            }
        }catch (Exception e){
            log.info("【创建用户】失败，失败原因"+e.getMessage(),e);
            return SyncDataResultVo.error("创建失败，失败信息为" + e.getMessage());
        }
    }

    @ClientOnly
    @PatchMapping({"/{username}"})
    @Summary("更新用户信息")
    @ApiAccess(name = "更新用户信息", code = "user::update")
    SyncDataResultVo update(@Summary("用户账号") @PathVariable("username")String username,@Summary("用户更新信息") @RequestBody IamUserUpdateVo iamUserUpdateVo) {
        try {
            return syncUserService.update("user::update", iamUserUpdateVo, username);
        }catch (Exception e){
            log.info("【更新用户】失败，失败原因"+e.getMessage(),e);
            return SyncDataResultVo.error("更新失败，失败信息为" + e.getMessage());
        }

    }

    @ClientOnly
    @PostMapping("/disable/{username}")
    @Summary("注销用户")
    @ApiAccess(name = "注销用户", code = "user::disable")
    SyncDataResultVo disable(@PathVariable("username") @Summary("用户账号名") String username) {
        if (StrUtil.isEmpty(username)) {
            return SyncDataResultVo.error("username不允许为空！");
        }
        try {
            return syncUserService.disable("user::disable", username);
        }catch (Exception e){
            log.info("【注销用户】失败，失败原因"+e.getMessage(),e);
            return SyncDataResultVo.error("注销失败，失败信息为" + e.getMessage());
        }
    }

    @ClientOnly
    @PatchMapping("/enable/{username}")
    @Summary("启用用户")
    @ApiAccess(name = "启用用户", code = "user::enable")
    SyncDataResultVo enable(@PathVariable("username") @Summary("用户账号名") String username) {
        if (StrUtil.isEmpty(username)) {
            return SyncDataResultVo.error("username不允许为空！");
        }
        try {
            return syncUserService.enable("user::enable", username);
        }catch (Exception e){
            log.info("【启用用户】失败，失败原因"+e.getMessage(),e);
            return SyncDataResultVo.error("启用失败，失败信息为" + e.getMessage());
        }
    }

    @ClientOnly
    @PostMapping("/resetPwd/{username}")
    @Summary("修改密码")
    @ApiAccess(name = "修改用户密码", code = "user::updatePsw")
    SyncDataResultVo resetPwd(@PathVariable("username") @Summary("用户账号名") String username,
                              @RequestBody(required = true) UserUpdatePasswordVo userUpdatePasswordVo) {
        IamUserEntity byUsername = iamUserManager.findByUsername(null, username);
        if(ObjectUtil.isEmpty(byUsername)){
            return SyncDataResultVo.error("查询不到username为"+username+"的用户");
        }
        if(StrUtil.isEmpty(userUpdatePasswordVo.getPassword())){
            SyncDataResultVo.error("密码不能为空");
        }
        log.info("[重置密码] {}",username);
        RSA rsa = new RSA(null, publicKeyBase64);
        byte[] aByte = HexUtil.decodeHex(userUpdatePasswordVo.getPassword());
        byte[] decrypt = rsa.decrypt(aByte, KeyType.PublicKey);
        String password = StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);
        boolean b = passwordManager.updatePassword(byUsername.getId(), password);
        if(b){
            log.info("[重置密码]更新用户状态");
            iamUserManager.dao().executeUpdate("update iam_user_status set locked_ = false where login_name = ?",username);
        }
        // 加锁，登录后先不回写
        long lockTime = 900;
        String lockTimeStr = gfCommTenantConfigService.findCommTenantConfig(Constants.USER_PASSWORD_LOCK_TIME);
        if(StrUtil.isNotEmpty(lockTimeStr)){
            lockTime = Long.parseLong(lockTimeStr);
        }
        String key = Constants.USER_PASSWORD_LOCK_PREFIX + username;
        log.info("密码回写锁定{}s,key为{}",lockTime,key);
        redisTemplate.opsForValue().set(key, "true",lockTime, TimeUnit.SECONDS);
        return b?SyncDataResultVo.success(null,"重置密码成功"):SyncDataResultVo.error("重置失败");
    }

    @PostMapping("/ioa")
    @Summary("ioa创建用户")
    @ClientOnly
    SyncDataResultVo create(@Summary("用户实体类") @RequestBody IoaUserCreateVo iamUserCreateVo) {
        if(ioaEnable) {
            try {
                IamUser userByUsername = iamUserService.getUserByUsername(iamUserCreateVo.getUsername());
                if(ObjectUtil.isNotEmpty(userByUsername)){
                    return SyncDataResultVo.error("创建用户失败，用户账号"+iamUserCreateVo.getUsername()+"已存在");
                }
                return syncUserService.create(iamUserCreateVo);
            } catch (Exception e) {
                log.info("【创建用户】失败，失败原因" + e.getMessage(), e);
                return SyncDataResultVo.error("创建用户失败，失败信息为" + e.getMessage());
            }
        }else{
            return SyncDataResultVo.error("创建用户失败，iam未启用创建用户按钮");
        }
    }

    @ClientOnly
    @PostMapping("/checkUsernames")
    @Summary("检查账号是否重复(智慧人力)")
    @ApiAccess(name = "检查账号是否重复", code = "user::check")
    SyncDataResultVo checkUsernames(@Summary("用户账号列表") @RequestBody List<String> usernames) {
        try {
            Set<String> availableUsernames = syncUserService.checkUsernames(usernames);
            return SyncDataResultVo.success(availableUsernames, "获取可用账号成功");
        } catch (Exception e) {
            log.error("【检查账号】失败，失败原因" + e.getMessage(), e);
            return SyncDataResultVo.error("检查账号失败，失败信息为" + e.getMessage());
        }
    }
}
