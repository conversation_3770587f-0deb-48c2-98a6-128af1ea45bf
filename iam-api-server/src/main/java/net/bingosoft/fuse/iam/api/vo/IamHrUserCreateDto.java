package net.bingosoft.fuse.iam.api.vo;

import fly.core.meta.annotation.Summary;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class IamHrUserCreateDto {
    @Summary("itUrl")
    protected String itUrl;
    @Summary("员工类型 OA , AD , mailbox , hk")
    protected List<String> staffType;
    @Summary("用户名称")
    protected String name;
    @Summary("OA用户名")
    protected String oaName;
    @Summary("登录账号")
    protected List<String> username;
    @Summary("用户名首字母拼音")
    protected String py;
    @Summary("用户名拼音")
    protected String pinyin;
    @Summary("登录密码")
    protected String password;
    @Summary("所属组织ID AD-ID OA-ID")
    protected IamUserOrgVo org;
    @Summary("组织id")
    protected String orgId;
    @Summary("所属组织名称")
    protected String orgName;
    @Summary("所属组织路径")
    protected String orgPath;
    @Summary("手机号码 分隔符, ")
    protected String mobile;
    @Summary("邮箱")
    protected String email;
    @Summary("其他群组ID AD-ID OA-ID")
    protected List<String> otherOrgIds;
    @Summary("角色 合作方员工 | 公共账号 | 正式员工 | 营销员工 | 劳务派遣 | 供应商驻场 | 实习生 ")
    protected String employeeRoleName;
    @Summary("过期时间 (AD|AD+EMAIL)时必填 不传默认为长期有效")
    protected LocalDate expiredAt;
    @Summary("邮箱用户服务级别 0 10 11")
    protected String emailUserServiceLevel;
    @Summary("因特网地址")
    protected String emailInternet;
    @Summary("其他邮箱地址")
    protected String otherEmail;
    @Summary("管理者")
    protected String admin;
    @Summary("生效时间 不传默认为当前时间")
    protected LocalDate enableDate;
    @Summary("备注")
    protected String description;
    @Summary("erp号")
    protected String erpId;
    @Summary("岗位")
    protected String position;
    @Summary("工作地址")
    protected String workPlace;
    @Summary("上级经理")
    protected String manager;
    @Summary("邮政编码")
    protected String postalCode;
    @Summary("分机号")
    protected String directPhone;
    @Summary("座机")
    protected String phone;
    @Summary("工作电话")
    protected String phoneNumber;
    @Summary("员工编号")
    protected String code;
    @Summary("业务id，智慧人力专用")
    protected String traceId;

}
