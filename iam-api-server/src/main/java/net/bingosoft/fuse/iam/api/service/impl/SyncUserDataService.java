package net.bingosoft.fuse.iam.api.service.impl;


import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import fly.core.data.model.RowMap;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.acm.dto.AcmUserEmpDto;
import net.bingosoft.fuse.iam.api.acm.service.AcmApiService;
import net.bingosoft.fuse.iam.api.acm.service.AcmOrgInfoGeinfoService;
import net.bingosoft.fuse.iam.api.acm.service.AcmUserInfoGeninfoService;
import net.bingosoft.fuse.iam.api.acm.vo.GeninfoEmpInfo;
import net.bingosoft.fuse.iam.api.client.entity.IamClientDataAcmLog;
import net.bingosoft.fuse.iam.api.client.manager.IamClientDataAcmLogManager;
import net.bingosoft.fuse.iam.api.client.manager.IamClientDataAuthzDetailManager;
import net.bingosoft.fuse.iam.api.ldap.service.impl.LdapUserServiceImpl;
import net.bingosoft.fuse.iam.api.model.IamOrg;
import net.bingosoft.fuse.iam.api.model.IamUser;
import net.bingosoft.fuse.iam.api.service.IamOrgService;
import net.bingosoft.fuse.iam.api.service.IamUserService;
import net.bingosoft.fuse.iam.api.service.MailSendService;
import net.bingosoft.fuse.iam.api.util.ClientSecurity;
import net.bingosoft.fuse.iam.api.util.HanZiToPinYin;
import net.bingosoft.fuse.iam.api.vo.*;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.managers.users.IamOrgManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserOrgManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SyncUserDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SyncUserDataService.class);
    @Autowired
    IamUserService iamUserService;
    @Autowired
    IamOrgService iamOrgService;
    @Autowired
    IamUserManager iamUserManager;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    IamUserOrgManager iamUserOrgManager;
    @Autowired
    IamOrgManager iamOrgManager;
    @Autowired
    AcmApiService acmApiService;
    @Autowired
    SyncApiService syncApiService;
    @Autowired
    AcmUserInfoGeninfoService acmUserInfoGeninfoService;
    @Autowired
    IamClientDataAuthzDetailManager iamClientDataAuthzDetailManager;
    @Autowired
    IamClientDataAcmLogManager iamClientDataAcmLogManager;
    @Value("${iam-console.host.url}")
    String IAM_CONSOLE_URL;
    @Autowired
    IamRequestOperationManager iamRequestOperationManager;
    @Autowired
    LdapUserServiceImpl ldapUserService;
    @Autowired
    MailSendService mailSendService;

    private final AcmOrgInfoGeinfoService acmOrgInfoGeinfoService;

    public SyncUserDataService(AcmOrgInfoGeinfoService acmOrgInfoGeinfoService) {
        this.acmOrgInfoGeinfoService = acmOrgInfoGeinfoService;
    }

    public SyncDataResultVo users(String code, int start, int limit, Boolean isEnable, String staffType, String username, String createDate){
        LOGGER.info("判断权限。。。");
        String clientId = ClientSecurity.getClientId();
        List<RowMap> authOrgRootList = new ArrayList<>();
        List<RowMap> authUserType = new ArrayList<>();
        boolean equals = "ioa".equals(clientId);
        if(!equals) {
            authOrgRootList = iamClientDataAuthzDetailManager.queryAuthOrgsByClientId(clientId, code);
            authUserType = iamClientDataAuthzDetailManager.queryAuthUserTypeByClientId(clientId, code);
            if (ObjectUtil.isEmpty(authOrgRootList)) {
                return SyncDataResultVo.error("该客户端没有该接口权限");
            }
        }
        List<UsersApiVo> page = iamUserService.getUsersListByStaffTypeAndEnable((start-1)*limit,limit,isEnable,staffType,authOrgRootList.stream().map(e->e.getString("code")).collect(Collectors.toList()),authUserType,equals,username,createDate);
        return SyncDataResultVo.success(0,page,"获取成功");
    }

    public SyncDataResultVo user(String code,String userName,String type){
        LOGGER.info("判断权限。。。");
        String clientId = ClientSecurity.getClientId();
        List<RowMap> authOrgRootList = iamClientDataAuthzDetailManager.queryAuthOrgsByClientId(clientId, code);
        if(ObjectUtil.isEmpty(authOrgRootList)) {
            return SyncDataResultVo.error("该客户端没有该接口权限");
        }
        UserApiVo userApiVo =  iamUserService.getUserDetailsByUserName(userName,authOrgRootList.stream().map(e->e.getString("code")).collect(Collectors.toList()));
        return SyncDataResultVo.success(userApiVo,"获取成功");
        //        String sql = "select u.id_          as id,\n" +
//                "       u.name_        as name,\n" +
//                "       u.staff_type   as staff_type,\n" +
//                "       u.username_    as username,\n" +
//                "       u.pinyin_      as pinyin,\n" +
//                "       u.py_          as py,\n" +
//                "       u.email_       as email,\n" +
//                "       u.mobile_      as mobile,\n" +
//                "       u.phone_number as phone_number,\n" +
//                "       u.phone        as phone,\n" +
//                "       u.direct_phone as direct_phone,\n" +
//                "       u.erp_id       as erp_id,\n" +
//                "       u.work_place   as work_place,\n" +
//                "       u.manager      as manger,\n" +
//                "       u.postal_code  as post_code,\n" +
//                "       o.name_        as org_name,\n" +
//                "       o.oa_org_id    as oa_org_id,\n" +
//                "       o.external_id  as ad_org_id,\n" +
//                "       o.code_         as code,\n"+
//                "       u.oa_user_id   as oa_user_id,\n" +
//                "       u.external_id  as ad_user_id,\n" +
//                "       DATE_FORMAT(u.updated_at,'%Y-%m-%d %T') as updated_at,\n" +
//                "       DATE_FORMAT(u.created_at,'%Y-%m-%d %T') as created_at\n" +
//                "from iam_user u\n" +
//                "         left join iam_org o on u.org_id = o.id_\n" +
//                "where u.deleted_ = false\n" +
//                "and u.username_ = '"+userName+"'\n";
//        if ("OA".equals(type))
//            sql += "and u.oa_user_id is not null\n";
//        else if ("AD".equals(type))
//            sql += "and u.external_id is not null\n";
//        List<RowMap> result = iamUserManager.dao().createSQLQuery(sql).list();
//        if(result==null||result.size()==0) return SyncDataResultVo.error("不存在该用户");
//        RowMap user = result.get(0);
//        boolean hasAuth = false;
//        for (RowMap rootOrg : authOrgRootList) {
//            if((user.getString("code")+",").startsWith(rootOrg.getString("code"))) {
//                user.remove("code");
//                hasAuth = true;
//            }
//        }
//        if(!hasAuth)
//            return SyncDataResultVo.error("该客户端没有该权限");
//        String sql2 = "select id_ as org_id,external_id as ad_org_id,oa_org_id as oa_org_id from iam_org \n" +
//                "where id_ in (\n" +
//                "    select org_id from iam_user_org\n" +
//                "    where user_id = ? \n" +
//                ")\n";
//        if("OA".equals(type))
//            sql2+=" and oa_org_id is not null \n";
//        else if ("AD".equals(type))
//            sql2 += " and external_id is not null \n";
//        List<RowMap> list = iamOrgManager.dao().createSQLQuery(sql2)
//                .args(user.getString("id"))
//                .list();
//        user.put("otherOrgs",list);
//        return SyncDataResultVo.success(user);
    }

    @Transactional(rollbackFor = Exception.class)
    public SyncDataResultVo disable(String code,String username) {
        IamUserEntity user = iamUserManager.findByUsername(null, username);
        if (ObjectUtil.isEmpty(user)) {
            return SyncDataResultVo.error("查询不到用户['"+username+"']");
        }
        log.info("【注销用户】查询接口权限。。");
        String clientId = ClientSecurity.getClientId();
        boolean accessible = syncApiService.checkOrgAccessible(user.getOrgId(), clientId, code);
        if(!accessible) {
            return SyncDataResultVo.error("该客户端没有该接口权限");
        }
        log.info("【注销用户】注销用户。。");
        iamUserService.disableUser(username);
//        ThreadUtil.execAsync(()-> {
            log.info("【注销用户】发送acm。。");
        AcmResultMsgVO acmResultMsgVO = AcmResultMsgVO.success(null);
            IamClientDataAcmLog iamClientDataAcmLog = new IamClientDataAcmLog();
            try {
                AcmUserEmpDto acmUserEmpDto = iamUserService.initAcmDisAbleUser(username);
                acmResultMsgVO = acmApiService.removeEmp(acmUserEmpDto);
                iamClientDataAcmLog.setInterfaceName("注销用户");
                iamClientDataAcmLog.setRequestObj(JSONUtil.toJsonStr(acmUserEmpDto));
                iamClientDataAcmLog.setResponseObj(JSONUtil.toJsonStr(acmResultMsgVO));
                if (acmResultMsgVO.getCode() == 1) {
                    iamClientDataAcmLog.setStatus(0);
                    log.info("【iam】iam注销成功。【acm】acm注销用户失败，失败原因" + acmResultMsgVO.getMessage());
                } else {
                    iamClientDataAcmLog.setStatus(1);
                    log.info("注销用户成功");
                }
            } catch (Exception e) {
                String s = "【iam】iam注销成功。【acm】acm注销用户失败，失败原因" + e.getMessage();
                iamClientDataAcmLog.setStatus(0);
                iamClientDataAcmLog.setResponseObj(s);
                log.info(s);
            } finally {
                ThreadUtil.execAsync(()->{
                    iamClientDataAcmLogManager.create(iamClientDataAcmLog);
                });
            }
//        });
        if(acmResultMsgVO.getCode()==1){
            throw new RuntimeException("发送acm失败：失败原因："+acmResultMsgVO.getMessage());
        }
        return SyncDataResultVo.success(null, "注销用户成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public SyncDataResultVo enable(String code,String username) {
        IamUser userByUsername = iamUserService.getUserByUsername(username);
        if (ObjectUtil.isEmpty(userByUsername)) {
            return SyncDataResultVo.error("查询不到用户['"+username+"']");
        }
        log.info("【启用用户】查询接口权限。。");
        String clientId = ClientSecurity.getClientId();
        boolean accessible = syncApiService.checkOrgAccessible(userByUsername.getOrgId(), clientId, code);
        if(!accessible) {
            return SyncDataResultVo.error("该客户端没有该接口权限");
        }
        log.info("【启用用户】启用用户。。");
        iamUserService.enableUser(username);
//        ThreadUtil.execAsync(()->{
        AcmResultMsgVO acmResultMsgVO = AcmResultMsgVO.success(null);
            log.info("【启用用户】发送acm。。");
            IamClientDataAcmLog iamClientDataAcmLog = new IamClientDataAcmLog();
            iamClientDataAcmLog.setInterfaceName("启用用户");
            try {
                AcmUserEmpDto acmUserEmpDto = iamUserService.initAcmEnableUser(username);
                iamClientDataAcmLog.setRequestObj(JSONUtil.toJsonStr(acmUserEmpDto));
                acmResultMsgVO = acmApiService.enableEmp(acmUserEmpDto);

                iamClientDataAcmLog.setResponseObj(JSONUtil.toJsonStr(acmResultMsgVO));
                if (acmResultMsgVO.getCode() == 1) {
                    iamClientDataAcmLog.setStatus(0);
                    log.info("【iam】iam启用用户成功。【acm】acm启用用户失败，失败原因" + acmResultMsgVO.getMessage());
                } else {
                    iamClientDataAcmLog.setStatus(1);
                    log.info("启用用户成功");
                }
            }catch (Exception e){
                String message = "【iam】启用用户成功;【acm】发送acm失败，失败原因为：" + e.getMessage();
                iamClientDataAcmLog.setResponseObj(message);
                log.info(message,e);
            }finally {
                ThreadUtil.execAsync(()->{
                    iamClientDataAcmLogManager.create(iamClientDataAcmLog);
                });
            }
//        });
        if(acmResultMsgVO.getCode()==1){
            throw new RuntimeException("发送acm失败：失败原因："+acmResultMsgVO.getMessage());
        }
        return SyncDataResultVo.success(null,"启用用户成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public SyncDataResultVo create(String code,IamUserCreateVo iamUserCreateVo){
        String clientId = ClientSecurity.getClientId();
        transOAADToIam(iamUserCreateVo);
        checkCreateParams(iamUserCreateVo);
        // 判断是否有权限
        LOGGER.info("【创建用户】判断权限。。。");
        boolean accessible = syncApiService.checkOrgAccessible(iamUserCreateVo.getOrg().getOrgId(), clientId, code);
        if (!accessible) {
            LOGGER.info("【创建用户】该客户端没有该接口权限。。。");
            return SyncDataResultVo.error("该客户端没有该接口权限");
        }
        LOGGER.info("【创建用户】创建用户。。。");
        IamUser user = iamUserService.createUser(iamUserCreateVo);
        iamUserService.changeUserStaffType(iamUserCreateVo.getStaffType(),user.getId());
        // 发送acm


        AcmResultMsgVO emp = AcmResultMsgVO.success(null);
        LOGGER.info("【创建用户】发送acm。。。");
        IamClientDataAcmLog iamClientDataAcmLog = new IamClientDataAcmLog();
        try {
            // 1.密码恢复为未加密
            user.setPassword(iamUserCreateVo.getPassword());
            // 2.排序
            user.setSort(iamUserCreateVo.getOrg().getOrder());
            // 3.其余所属组织id
            user.setOtherOrgIds(iamUserCreateVo.getOtherOrgIds());
            AcmUserEmpDto acmUserEmpDto = iamUserService.initAcmCreateUser(user);
            emp = acmApiService.createEmp(acmUserEmpDto);
            if(ObjectUtil.isNotEmpty(acmUserEmpDto.getPassword())) {
                acmUserEmpDto.setPassword("******");
            }
            iamClientDataAcmLog.setInterfaceName("添加用户信息");
            iamClientDataAcmLog.setRequestObj(JSONUtil.toJsonStr(acmUserEmpDto));
            iamClientDataAcmLog.setResponseObj(JSONUtil.toJsonStr(emp));
            LOGGER.info("【创建用户】发送acm结束");
            if (emp.getCode() == 1) {
                iamClientDataAcmLog.setStatus(0);
                LOGGER.info("【iam】创建用户成功 【acm】发送acm失败，失败信息为" + emp.getMessage());
            } else {
                iamClientDataAcmLog.setStatus(1);
                LOGGER.info("【创建用户】发送acm成功。。。");
                LOGGER.info("【创建用户】更新用户acmId、adId，oaId 。。。");
                String username = user.getUsername();
                List<GeninfoEmpInfo> geninfoEmpInfos = acmUserInfoGeninfoService.getEmpInfo(username);
                if (ObjectUtil.isEmpty(geninfoEmpInfos)) {
                    LOGGER.info("【创建用户】【geninfo】获取用户账号为{}的信息失败", username);
                    LOGGER.info("【创建用户】【iam】创建用户成功 【geninfo】获取用户账号为" + username + "的信息失败。");
                } else {
                    GeninfoEmpInfo geninfoEmpInfo = geninfoEmpInfos.get(0);
                    UpdateWrapper<IamUser> update = new UpdateWrapper<IamUser>()
                            .eq("id_", user.getId())
                            .set(StrUtil.isNotEmpty(geninfoEmpInfo.getAdId()), "external_id", geninfoEmpInfo.getAdId())
                            .set(StrUtil.isNotEmpty(geninfoEmpInfo.getAcmId()), "acm_user_id", geninfoEmpInfo.getAcmId());
                    iamUserService.update(update);
                }
                LOGGER.info("【创建用户】更新用户acmId、adId成功");
                log.info("【oa】获取oaid");
                ldapUserService.updateUserOaId(username);
            }
        }catch (Exception e){
            iamClientDataAcmLog.setStatus(0);
            String s = "【iam】创建用户成功，发送acm失败，失败原因为：" + e.getMessage();
            iamClientDataAcmLog.setResponseObj(s);
            log.info(s);
        }finally {
            ThreadUtil.execAsync(()->{
                iamClientDataAcmLogManager.create(iamClientDataAcmLog);
            });
        }
        if(emp.getCode()==1){
            throw new RuntimeException("发送acm失败：失败原因："+emp.getMessage());
        }

        String traceId = iamUserCreateVo.getTraceId();
        iamClientDataAcmLogManager.dao().executeUpdate("update iam_client_data_log set username_ = ?,email_=? where trace_id = ?",iamUserCreateVo.getEmail(),iamUserCreateVo.getUsername(),traceId);
        IamHrUserVo iamHrUserVo = new IamHrUserVo();
        iamHrUserVo.setEmail(user.getEmail());
        iamHrUserVo.setUsername(user.getUsername());
        return SyncDataResultVo.success(iamHrUserVo,"创建用户成功");
    }

    private void checkCreateParams(IamUserCreateVo iamUserCreateVo) {
        Assert.notNull(iamUserCreateVo.getName(),"用户姓名不能为空");
        if(StrUtil.isEmpty(iamUserCreateVo.getOrgName())&&(ObjectUtil.isEmpty(iamUserCreateVo.getOrg())|| StrUtil.isEmpty(iamUserCreateVo.getOrg().getOrgId()))){
            throw  new RuntimeException("用户组织信息不能为空");
        }else {
            log.info("检查同一组织下的用户是否重复");
            boolean duplicate = iamUserService.checkUserDuplicateInOrg(iamUserCreateVo.getOrg().getOrgId(),iamUserCreateVo.getName());
            if(duplicate){
                throw  new RuntimeException("同一组织下的用户名称不能重复");
            }
        }
        Assert.notNull(iamUserCreateVo.getUsername(),"用户账号不能为空");
        log.info("检查用户账号是否重复");
        IamUser userByUsername = iamUserService.getUserByUsername(iamUserCreateVo.getUsername());
        if(ObjectUtil.isNotEmpty(userByUsername)){
           throw new RuntimeException("用户账号重复");
        }
        Assert.notNull(iamUserCreateVo.getPassword(),"用户密码不能为空");
        Assert.notNull(iamUserCreateVo.getStaffType(),"用户类型不能为空");
        boolean isOA = iamUserCreateVo.getStaffType().contains("OA");
        boolean isAD = iamUserCreateVo.getStaffType().contains("AD");
        boolean isCoremail = iamUserCreateVo.getStaffType().contains("mailbox");
        boolean isHk = iamUserCreateVo.getStaffType().contains("hk");
        if(isOA){
            Assert.notNull(iamUserCreateVo.getOaName(),"OA用户的oa用户名不能为空");
            Assert.state(
                    Pattern.compile(".*/GFZQ$").matcher(iamUserCreateVo.getOaName())
                            .matches(),"OA用户的oa账户名应该以'/GFZQ'结尾");
            if(isHk||isCoremail||isAD) {
                Assert.notNull(iamUserCreateVo.getPinyin(),"用户的全拼不能为空");
            }

        }
        if(isOA||isCoremail){
            Assert.notNull(iamUserCreateVo.getEmail(),"用户的邮箱不能为空");
        }
        if(isCoremail){
            Assert.notNull(iamUserCreateVo.getEmailInternet(),"用户的因特网地址不能为空");
        }
        Assert.notNull(iamUserCreateVo.getPy(),"用户的简拼不能为空");
        Assert.notNull(iamUserCreateVo.getEmployeeRoleName(),"用户角色不能为空");
        if(isAD&&!isOA){
            Assert.notNull(iamUserCreateVo.getExpiredAt(),"AD用户的过期时间不能为空");
        }
    }

    private void transOAADToIam(IamUserCreateVo iamUserCreateVo) {
        // 翻译组织id
        if(StrUtil.isNotEmpty(iamUserCreateVo.getOrgName())){
            IamOrg iamOrg = iamOrgService.getOrgByName(iamUserCreateVo.getOrgName());
            if(ObjectUtil.isEmpty(iamOrg)){
                throw new RuntimeException("组织名为"+iamUserCreateVo.getOrgName()+"的组织不存在");
            }
            IamUserOrgVo org = new IamUserOrgVo();
            org.setOrgId(iamOrg.getId());
            org.setOrder("last");
            iamUserCreateVo.setOrg(org);
        }
        if(ObjectUtil.isEmpty(iamUserCreateVo.getOrg())||StrUtil.isEmpty(iamUserCreateVo.getOrg().getOrgId())){
            throw new RuntimeException("组织名必填");
        }
        String orgId = iamUserCreateVo.getOrg().getOrgId();
        if(StrUtil.isNotEmpty(orgId)){
            QueryWrapper<IamOrg> eq = new QueryWrapper<IamOrg>()
                    .eq("oa_org_id", orgId)
                    .or()
                    .eq("external_id", orgId)
                    .or()
                    .eq("id_",orgId);
            IamOrg one = iamOrgService.getOne(eq);
            iamUserCreateVo.getOrg().setOrgId(one.getId());
            if(ObjectUtil.isEmpty(one)) {
                throw new RuntimeException("找不到oa或者ad的id为"+orgId+"的组织");
            }
        }

        // 翻译其余所属组织id
        List<String> otherOrgIds = iamUserCreateVo.getOtherOrgIds();
        if(ObjectUtil.isNotEmpty(otherOrgIds)){
            QueryWrapper<IamOrg> in = new QueryWrapper<IamOrg>()
                    .in("oa_org_id", otherOrgIds)
                    .or()
                    .in("external_id", otherOrgIds)
                    .or()
                    .in("id_", otherOrgIds);
            List<IamOrg> list = iamOrgService.list(in);
            if(list.size()!=otherOrgIds.size()){
                String noFoundIds = otherOrgIds.stream().filter(e -> {
                    for (IamOrg iamOrgEntity : list) {
                        if (e.equals(iamOrgEntity.getId()) || e.equals(iamOrgEntity.getExternalId()) || e.equals(iamOrgEntity.getOaOrgId())) {
                            return false;
                        }
                    }
                    return true;
                }).collect(Collectors.joining("','"));
                if(StrUtil.isNotEmpty(noFoundIds)){
                    throw  new RuntimeException("找不到其余所属组织为['"+noFoundIds+"']的组织");
                }
            }
            List<String> otherIds = list.stream().map(e -> e.getId()).collect(Collectors.toList());
            iamUserCreateVo.setOtherOrgIds(otherIds);
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public SyncDataResultVo update(String code, IamUserUpdateVo iamUserVo, String username) {
        transOAADToIam(iamUserVo);
        String clientId = ClientSecurity.getClientId();
        // 判断是否有权限
        LOGGER.info("【更新用户】判断权限。。。");
        IamUser old = iamUserService.getUserByUsername(username);
        if(ObjectUtil.isEmpty(old)) {
            return SyncDataResultVo.error("用户账号为"+username+"的用户不存在");
        }
        boolean accessible = syncApiService.checkOrgAccessible(old.getOrgId(), clientId, code);
        if (!accessible) {
            return SyncDataResultVo.error("该客户端没有该接口权限");
        }
        // 更新用户
        LOGGER.info("【更新用户】更新用户基本信息。。。");
        iamUserService.update(iamUserVo, username);
        AcmResultMsgVO acmResultMsgVO = AcmResultMsgVO.success(null);
//        ThreadUtil.execAsync(()->{
            LOGGER.info("【更新用户】发送acm。。。");
            IamClientDataAcmLog iamClientDataAcmLog = new IamClientDataAcmLog();
            try {
                AcmUserEmpDto acmUserEmpDto = iamUserService.initAcmUpdateUser(iamUserVo, old);
                acmResultMsgVO = acmApiService.updateEmp(acmUserEmpDto);
                if(ObjectUtil.isNotEmpty(acmUserEmpDto.getPassword())) {
                    acmUserEmpDto.setPassword("******");
                }
                iamClientDataAcmLog.setInterfaceName("更新用户信息");
                iamClientDataAcmLog.setRequestObj(JSONUtil.toJsonStr(acmUserEmpDto));
                iamClientDataAcmLog.setResponseObj(JSONUtil.toJsonStr(acmResultMsgVO));
                if (acmResultMsgVO.getCode() == 1) {
                    iamClientDataAcmLog.setStatus(0);
                    log.info("【更新用户】更新用户失败 【acm】发送acm失败，失败信息为" + acmResultMsgVO.getMessage());
                } else {
                    iamClientDataAcmLog.setStatus(1);
                    log.info("【更新用户】更新用户成功");
                }
            }catch (Exception e){
                String s = "【iam】更新用户成功,发送acm失败,失败信息为" + e.getMessage();
                log.info(s);
                iamClientDataAcmLog.setResponseObj(s);
            }finally {
                ThreadUtil.execAsync(()->{
                    iamClientDataAcmLogManager.create(iamClientDataAcmLog);
                });
            }
//        });
        if(acmResultMsgVO.getCode()==1){
            throw new RuntimeException("发送acm失败：失败原因："+acmResultMsgVO.getMessage());
        }
        return SyncDataResultVo.success(null, "更新用户成功");
    }

    private void transOAADToIam(IamUserUpdateVo iamUserUpdateVo) {
        String orgName = iamUserUpdateVo.getOrgName();
        if(StrUtil.isNotEmpty(orgName)){
            IamOrg orgByName = iamOrgService.getOrgByName(orgName);
            IamUserOrgVo org = new IamUserOrgVo();
            org.setOrgId(orgByName.getId());
            iamUserUpdateVo.setOrg(org);
        }
        // 翻译组织id
        if(ObjectUtil.isNotEmpty(iamUserUpdateVo.getOrg())&&ObjectUtil.isNotEmpty(iamUserUpdateVo.getOrg().getOrgId())) {
            String orgId = iamUserUpdateVo.getOrg().getOrgId();
            if (StrUtil.isNotEmpty(orgId)) {
                QueryWrapper<IamOrg> eq = new QueryWrapper<IamOrg>()
                        .eq("oa_org_id", orgId)
                        .or()
                        .eq("external_id", orgId)
                        .or()
                        .eq("id_", orgId);
                IamOrg one = iamOrgService.getOne(eq);
                iamUserUpdateVo.getOrg().setOrgId(one.getId());
                if (ObjectUtil.isEmpty(one)) {
                    throw new RuntimeException("找不到oa或者ad的id为" + orgId + "的组织");
                }
            }
        }
        // 翻译其余所属组织id
        List<String> otherOrgIds = iamUserUpdateVo.getOtherOrgIds();
        if(ObjectUtil.isNotEmpty(otherOrgIds)){
            QueryWrapper<IamOrg> in = new QueryWrapper<IamOrg>()
                    .in("oa_org_id", otherOrgIds)
                    .or()
                    .in("external_id", otherOrgIds)
                    .or()
                    .in("id_", otherOrgIds);
            List<IamOrg> list = iamOrgService.list(in);
            if(list.size()!=otherOrgIds.size()){
                String noFoundIds = otherOrgIds.stream().filter(e -> {
                    for (IamOrg iamOrgEntity : list) {
                        if (e.equals(iamOrgEntity.getId()) || e.equals(iamOrgEntity.getExternalId()) || e.equals(iamOrgEntity.getOaOrgId())) {
                            return false;
                        }
                    }
                    return true;
                }).collect(Collectors.joining("','"));
                if(StrUtil.isNotEmpty(noFoundIds)){
                    throw  new RuntimeException("找不到其余所属组织为['"+noFoundIds+"']的组织");
                }
            }
            List<String> otherIds = list.stream().map(e -> e.getId()).collect(Collectors.toList());
            iamUserUpdateVo.setOtherOrgIds(otherIds);
        }
    }

    public SyncDataResultVo oaUsers(String username){
        String sql = "select iu.erp_id                                                                          as \"employeeid\",\n" +
                "       iu.name_                                                                           as \"sn\",\n" +
                "       io.name_                                                                             as \"fdu-deptname\",\n" +
                "       iu.email_                                                                          as \"mailaddress\",\n" +
                "       null                                                                                 as \"encryptincomingmail\",\n" +
                "       iu.email_internet                                                                  as \"mail\",\n" +
                "       iu.phone_number                                                                    as \"telephonenumber\",\n" +
                "       iu.mobile_                                                                         as \"homephone\",\n" +
                "       null                                                                                 as \"roamcleanper\",\n" +
                "       gul.oa_mail_server                                                                 as \"mailserver\",\n" +
                "       iu.job_title                                                                       as \"title\",\n" +
                "       null                                                                                 as \"objectclass\",\n" +
                "       CASE WHEN iu.external_id is not null and iu.oa_user_id is not null THEN 'AD接口' END AS \"markFrom\",\n" +
                "       iu.username_                                                                       as \"uid\",\n" +
                "       null                                                                                 as \"copytopersonflag\",\n" +
                "       DATE_FORMAT(ius.pwd_updated_at ,'%Y-%m-%d %T')                                                                  as \"httppasswordchangedate\",\n" +
                "       'GFZQ'                                                                             as \"maildomain\",\n" +
                "       null                                                                                 as \"messagestorage\",\n" +
                "       gul.fdu_py                                                                         as \"fdu-py\",\n" +
                "       gul.department2                                                                    as \"department2\",\n" +
                "       gul.new_no                                                                         as \"newno\",\n" +
                "       gul.parent_no                                                                      as \"parentno\",\n" +
                "       DATE_FORMAT(iu.created_at ,'%Y-%m-%d %T')                                                                     as \"createtime\",\n" +
                "       null                                                                                 as \"passwordchangeinterval\",\n" +
                "       null                                                                                 as \"roaminguser\",\n" +
                "       iu.work_place                                                                      as \"postaladdress\",\n" +
                "       iu.mobile_                                                                         as \"mobile\",\n" +
                "       null                                                                                 as \"mailsystem\",\n" +
                "       iu.name_                                                                           as \"cn\",\n" +
                "       gul.fdu_person_unid                                                                as \"fdu-personunid\",\n" +
                "       CASE WHEN iu.oa_user_id is null THEN TRUE ELSE FALSE END                           AS \"coemployee\",\n" +
                "       null                                                                                 as \"roamcleansetting\",\n" +
                "       agu.oa_id                                                                                 as \"docuniqueid\",\n" +
                "       null                                                                                 as \"availablefordirsync\",\n" +
                "       iu.ad_show_name                                                                         as \"displayname\",\n" +
                "       iu.work_place                                                                      as \"officestreetaddress\",\n" +
                "       gul.dept_type                                                                      as \"depttype\",\n" +
                "       null                                                                                 as \"passwordgraceperiod\",\n" +
                "       null                                                                                 as \"checkpassword\",\n" +
                "       iu.phone_number                                                                    as \"officeresnumber\"\n"+

                "from iam_user iu\n" +
                "         left join iam_org io on iu.org_id = io.id_\n" +
                "         left join gf_user_ldap gul on gul.fdu_person_unid = iu.oa_user_id\n" +
                "         left join iam_user_status ius on ius.login_name = iu.username_\n" +
                "         left join acm_geninfo_user agu on agu.account = iu.username_\n" +
                "where iu.deleted_ = false \n";
        sql += " and  iu.oa_user_id is not null \n";
        sql += " and (iu.username_ = '"+username+"' ) \n";
        List<RowMap> list = iamUserManager.dao().createSQLQuery(sql)
                .args().execute().list();
        if(ObjectUtil.isEmpty(list)) {
            return SyncDataResultVo.error("查询不到该用户");
        }
        RowMap e = list.get(0);
//        Object expiredDate = e.get("expiredDate");
//        if(ObjectUtil.isNotEmpty(expiredDate)&& DateUtil.compare((Date) expiredDate, new Date())==-1)
//            return SyncDataResultVo.error("该用户已过期,过期时间"+expiredDate);
//        if(false == e.getBoolean("enabled")) return SyncDataResultVo.error("该用户已禁用");
//        e.remove("enabled");
//        e.remove("expiredDate");
        return  SyncDataResultVo.success(e);
    }


    public SyncDataResultVo adUsers(String username){
//        "uid":"gzchenwenmin", "fdu-deptname":"综合用户","cn":"陈文敏","email":"<EMAIL>","displayname":"陈文敏（par0102）","coemployee":true
        String sql = "select " +
                "       iu.erp_id                                                                          as \"employeID\",\n" +
                "       iu.name_                                                                           as \"sn\",\n" +
                "       io.name_                                                                             as \"fdu-deptname\",\n"+
//                "       iu.email_                                                                          as \"mailaddress\",\n" +
//                "       null                                                                                 as \"encryptincomingmail\",\n" +
                "       iu.email_                                                                          as \"email_\",\n" +
                "       iu.phone_number                                                                    as \"telephonenumber\",\n" +
                "       iu.mobile_                                                                         as \"mobile\",\n" +
//                "       null                                                                                 as \"roamcleanper\",\n" +
//                "       gul.oa_mail_server                                                                 as \"mailserver\",\n" +
//                "       iu.job_title                                                                       as \"title\",\n" +
//                "       null                                                                                 as \"objectclass\",\n" +
//                "       CASE WHEN iu.external_id is not null and iu.oa_user_id is not null THEN 'AD接口' END AS \"markFrom\",\n" +
                "       iu.username_                                                                       as \"uid\",\n" +
                "       iu.username_                                                                       as \"sAMAccountName\",\n" +
//                "       null                                                                                 as \"copytopersonflag\",\n" +
//                "       DATE_FORMAT(ius.pwd_updated_at ,'%Y-%m-%d %T')                                                                  as \"httppasswordchangedate\",\n" +
//                "       'GFZQ'                                                                             as \"maildomain\",\n" +
//                "       null                                                                                 as \"messagestorage\",\n" +
//                "       gul.fdu_py                                                                         as \"fdu-py\",\n" +
//                "       gul.department2                                                                    as \"department2\",\n" +
//                "       gul.new_no                                                                         as \"newno\",\n" +
//                "       gul.parent_no                                                                      as \"parentno\",\n" +
//                "       DATE_FORMAT(iu.created_at ,'%Y-%m-%d %T')                                                                     as \"createtime\",\n" +
//                "       null                                                                                 as \"passwordchangeinterval\",\n" +
//                "       null                                                                                 as \"roaminguser\",\n" +
//                "       iu.work_place                                                                      as \"postaladdress\",\n" +
//                "       iu.mobile_                                                                         as \"mobile\",\n" +
//                "       null                                                                                 as \"mailsystem\",\n" +
                "       iu.name_                                                                           as \"cn\",\n" +
//                "       gul.fdu_person_unid                                                                as \"fdu-personunid\",\n" +
                "       CASE WHEN iu.oa_user_id is null THEN TRUE ELSE FALSE END                           AS \"coemployee\",\n" +
//                "       null                                                                                 as \"roamcleansetting\",\n" +
//                "       null                                                                                 as \"docuniqueid\",\n" +
//                "       null                                                                                 as \"availablefordirsync\",\n" +
                "       iu.ad_show_name                                                                        as \"displayname\"\n" +
//                "       iu.work_place                                                                      as \"officestreetaddress\",\n" +
//                "       gul.dept_type                                                                      as \"depttype\",\n" +
//                "       null                                                                                 as \"passwordgraceperiod\",\n" +
//                "       null                                                                                 as \"checkpassword\",\n" +
//                "       iu.phone_number                                                                    as \"officeresnumber\",\n" +
//                "       iu.enabled_                                                                        as \"enabled\",\n" +
//                "       io.desc_                                                                             as \"full_org_name\",\n" +
//                "       iu.expired_at                                                                      as \"expired_date\"\n" +

                "from iam_user iu\n" +
                "         left join iam_org io on iu.org_id = io.id_\n" +
                "         left join gf_user_ldap gul on gul.fdu_person_unid = iu.oa_user_id\n" +
                "         left join iam_user_status ius on ius.login_name = iu.username_\n" +
                "where iu.deleted_ = false \n";
        sql += " and  iu.external_id is not null \n";
        sql += " and (iu.username_ = '"+username+"' ) \n";
        List<RowMap> list = iamUserManager.dao().createSQLQuery(sql)
                .args().execute().list();
        if(ObjectUtil.isEmpty(list)) {
            return SyncDataResultVo.error("查询不到该用户");
        }
        RowMap e = list.get(0);
        //转换实体
        AdUserVo adUserVo = convertToAdUser(e);

//        Object expiredDate = e.get("expiredDate");
//        if(ObjectUtil.isNotEmpty(expiredDate)&& DateUtil.compare((Date) expiredDate, new Date())==-1)
//            return SyncDataResultVo.error("该用户已过期,过期时间"+expiredDate);
//        if(false == e.getBoolean("enabled")) return SyncDataResultVo.error("该用户已禁用");
//        if(StrUtil.isEmpty(e.getString("fdu-deptname"))) {
//            String fullOrgName = e.getString("fullOrgName");
//            if (StrUtil.isNotEmpty(fullOrgName)&&fullOrgName.contains("综合用户"))
//                e.put("fdu-deptname","综合用户");
//        }
//        e.remove("enabled");
//        e.remove("fullOrgName");
//        e.remove("expiredDate");
        return  SyncDataResultVo.success(adUserVo);
    }

    private  AdUserVo convertToAdUser(RowMap e) {
        AdUserVo adUserVo = new AdUserVo();
        adUserVo.setFduDeptname((String) e.get("fdu-deptname"));
        adUserVo.setEmail((String) e.get("email"));
        adUserVo.setMail((String) e.get("email"));
        adUserVo.setUserPrincipalName((String) e.get("email"));
        adUserVo.setUid((String) e.get("uid"));
        adUserVo.setCn((String) e.get("cn"));
        Optional.ofNullable(e.get("coemployee")).ifPresent(v -> adUserVo.setCoemployee(Integer.parseInt(v.toString())));
        adUserVo.setDisplayname((String) e.get("displayname"));
        // 处理新增属性
        adUserVo.setEmployeeid((String) e.get("employeid")); // 注意RowMap字段名是employeid
        adUserVo.setSn((String) e.get("sn"));
        adUserVo.setTelephoneNumber((String) e.get("telephonenumber")); // 注意RowMap字段名全小写
        adUserVo.setSAMAccountName((String) e.get("samaccountname")); // 注意RowMap字段名全小写
        adUserVo.setMobile((String) e.get("mobile"));
        return adUserVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public SyncDataResultVo create(IoaUserCreateVo iamUserCreateVo) {
        log.info("【iam】创建用户信息{} {} {} {}",iamUserCreateVo.getName(),iamUserCreateVo.getUsername(),iamUserCreateVo.getOrgName(),iamUserCreateVo.getErpId());
        iamUserService.createOAUser(iamUserCreateVo);
        log.info("【iam】创建用户-主组织信息");
        iamUserService.createUserOrg(iamUserCreateVo.getUsername(),iamUserCreateVo.getOrgName());
        iamUserService.updateUserOAId(iamUserCreateVo.getUsername());
        return SyncDataResultVo.success(null,"【iam】创建用户成功");
    }

    public IamUserCreateVo initUserDto(IamHrUserCreateDto iamHrUserCreateDto) {
        Assert.notNull(iamHrUserCreateDto.getName(),"用户姓名不能为空");
        Assert.notEmpty(iamHrUserCreateDto.getUsername(),"用户账号不能为空");
        log.info("检查用户账号是否重复");
        String username = null;
        for (String s : iamHrUserCreateDto.getUsername()) {
            IamUser userByUsername = iamUserService.getUserByUsername(s);
            if(ObjectUtil.isNotEmpty(userByUsername)){
                log.info("账号{}重复",iamHrUserCreateDto.getUsername());
            }else {
                log.info("检查与草稿箱内的账号是否重复");

                List<RowMap> list = iamRequestOperationManager.selectUserName(s);
                if(ObjectUtil.isEmpty(list)) {
                    username = s;
                    break;
                }
            }
        }
        if(StrUtil.isEmpty(username)){
            throw new RuntimeException("账号重复");
        }
        Assert.notNull(iamHrUserCreateDto.getPassword(),"用户密码不能为空");
        Assert.notNull(iamHrUserCreateDto.getStaffType(),"用户类型不能为空");
        IamUserCreateVo iamUserCreateVo = new IamUserCreateVo();
        String oaName = makeOaName(iamHrUserCreateDto.getName(), iamHrUserCreateDto.getStaffType(), iamHrUserCreateDto.getEmployeeRoleName());
        log.info("智慧人力生成的oaName:{}",oaName);
        iamUserCreateVo.setOaName(oaName);
        iamUserCreateVo.setName(iamHrUserCreateDto.getName());
        iamUserCreateVo.setUsername(username);
        iamUserCreateVo.setCode(iamHrUserCreateDto.getCode());
        //如果传了HK,转成小写 不界面勾选有问题
        List<String> staffType = iamHrUserCreateDto.getStaffType();
        if(staffType.contains("HK")){
            staffType = staffType.stream()
                    .map(type -> "HK".equals(type) ? "hk" : type)
                    .collect(Collectors.toList());
        }
        iamUserCreateVo.setStaffType(staffType);
        iamUserCreateVo.setCode(iamHrUserCreateDto.getCode());
        //获取拼音字段
        String change = HanZiToPinYin.change(iamHrUserCreateDto.getName());
        //分割拼音
        String[] split = change.split("\\|");
        // 只允许纯字母（大小写不限）的正则
        Pattern alphaOnly = Pattern.compile("^[A-Za-z]+$");
        Optional.ofNullable(split[0]).filter(alphaOnly.asPredicate()).map(String::toUpperCase).ifPresent(iamUserCreateVo::setPy);
        Optional.ofNullable(split[1]).filter(alphaOnly.asPredicate()).ifPresent(iamUserCreateVo::setPinyin);
        //判断是否正确添加拼音
        if (StrUtil.isEmpty(iamUserCreateVo.getPinyin()) || StrUtil.isEmpty(iamUserCreateVo.getPy())){
            iamUserCreateVo.setIsRequest(true);
        }

        iamUserCreateVo.setPassword(iamHrUserCreateDto.getPassword());
        iamUserCreateVo.setItUrl(iamHrUserCreateDto.getItUrl());
        iamUserCreateVo.setEmail(makeEmail(username,iamHrUserCreateDto.getStaffType()));
        iamUserCreateVo.setEmailInternet(makeEmailInternet(username));
        iamUserCreateVo.setEmployeeRoleName(iamHrUserCreateDto.getEmployeeRoleName());
        //处理组织逻辑
        orgProcess(iamHrUserCreateDto, iamUserCreateVo);
        iamUserCreateVo.setTraceId(iamHrUserCreateDto.getTraceId());
        if(iamUserCreateVo.getIsRequest()){
            log.info("发送邮件");
            String finalUsername = username;
            ThreadUtil.execAsync(()-> {
                try {
                    mailSendService.sendRequestMail(iamHrUserCreateDto, finalUsername);
                }catch (Exception e){
                    log.info("发送邮箱失败，失败信息为{}",e.getMessage(),e);
                }
            });
        }
        return iamUserCreateVo;
    }

    /**
     * 处理组织
     * 1.尝试从geninfo通过orgId获取 dpId，进而获取组织信息
     * 2.没有获取到组织信息 通过 orgName,orgPath来获取
     * 3.将组织信息设置到 iamUserCreateVo 中
     * @param iamHrUserCreateDto 请求体
     * @param iamUserCreateVo 封装后的信息
     */
    private void orgProcess(IamHrUserCreateDto iamHrUserCreateDto, IamUserCreateVo iamUserCreateVo) {
        IamOrg result = null;
        //先按orgId 调用 geninfo 查dpID
        String dpIdByGenInfoErpOaDept = null;
        if (StrUtil.isNotEmpty(iamHrUserCreateDto.getOrgId())) {
             dpIdByGenInfoErpOaDept = Optional.ofNullable(iamHrUserCreateDto.getOrgId())
                    .map(acmOrgInfoGeinfoService::getGenInfoErpOaDeptByOrgId)
                    .orElse(null);
        }
        //可以通过geninfo查询的dpid获取组织  oa_org_id
        if (StrUtil.isNotEmpty(dpIdByGenInfoErpOaDept)){
            result = iamOrgService.getOne(new LambdaQueryWrapper<IamOrg>()
                    .eq(IamOrg::getOaOrgId, dpIdByGenInfoErpOaDept));
        }
        //获取组织为空按之前的逻辑
        if(ObjectUtil.isEmpty(result)){
            Assert.notNull(iamHrUserCreateDto.getOrgName(), "组织不能为空");
            IamOrg orgByName = iamOrgService.getOrgByName(iamHrUserCreateDto.getOrgName());
            if (ObjectUtil.isEmpty(orgByName)) {
                log.info("找不到组织名为{}的组织", iamHrUserCreateDto.getOrgName());
                String orgPath = iamHrUserCreateDto.getOrgPath();
                if (StrUtil.isEmpty(orgPath)) {
                    throw new RuntimeException("组织路径为空");
                } else {
                    String[] split = orgPath.split(",");
                    for (String orgName : split) {
                        IamOrg orgByName1 = iamOrgService.getOrgByName(orgName);
                        if (ObjectUtil.isNotEmpty(orgByName1)) {
                            orgByName = orgByName1;
                            iamUserCreateVo.setIsRequest(true);
                            break;
                        }
                    }
                }
            }
            result = orgByName;
        }
        //将组织信息设置到 iamUserCreateVo
        if (ObjectUtil.isNotEmpty(result)) {
            IamUserOrgVo org = new IamUserOrgVo();
            org.setOrder("last");
            org.setOrgId(result.getId());
            iamUserCreateVo.setOrgName(result.getName());
            iamUserCreateVo.setOrg(org);
        }
    }

    private String makeEmailInternet(String username) {
        return username+"@oa.gf.com.cn";
    }

    private String makeEmail(String username, List<String> staffType) {
        boolean isHK = staffType.stream()
                .anyMatch("hk"::equalsIgnoreCase);
        if(isHK){
            return username+"@gfgroup.com.hk";
        }else {
            return username+"@gf.com.cn";
        }
    }
    /**
     * 逻辑是  角色是营销是先判断oa用户名  xxx/scyx01/GFZQ 是否存在，不存在直接生成，存在则递增到02
     * 其他角色生成oa用户名 xxx/GFZQ 如果存在 则 xxx/staff01/GFZQ，如果还存在就查 xxx/staff02/GFZQ 一直到一个可以的)
     */
    private String makeOaName(String name, List<String> staffType, String employeeRoleName) {
        // 存在OA类型才生成
        if (staffType.contains("OA")) {
            //先判断是否hk用户 不是再判断角色类型
            boolean isHK = staffType.stream()
                    .anyMatch("hk"::equalsIgnoreCase);
            if (isHK) {
                // hk用户使用hk 如果有重复再使用hk01
                LambdaQueryWrapper<IamUser> baseQueryWrapper = new LambdaQueryWrapper<>();
                baseQueryWrapper.eq(IamUser::getOaName, name + "/hk/GFZQ");
                int count = iamUserService.count(baseQueryWrapper);
                if (count == 0) {
                    // 基本格式不存在，直接返回
                    return name + "/hk/GFZQ";
                } else {
                    // 基本格式存在，使用hk前缀
                    return generateUniqueOaName(name, "hk");
                }
            }else{
            if ("营销员工".equals(employeeRoleName)) {
                // 营销员工使用scyx前缀
                return generateUniqueOaName(name, "scyx");
            } else {
                // 其他角色先尝试基本格式
                LambdaQueryWrapper<IamUser> baseQueryWrapper = new LambdaQueryWrapper<>();
                baseQueryWrapper.eq(IamUser::getOaName, name + "/GFZQ");
                int count = iamUserService.count(baseQueryWrapper);

                if (count == 0) {
                    // 基本格式不存在，直接返回
                    return name + "/GFZQ";
                } else {
                    // 基本格式存在，使用staff前缀
                    return generateUniqueOaName(name, "staff");
                }
            }
        }}
        // 不包含OA类型，返回空
        return "";
    }

    /**
     * 生成唯一的OA用户名
     *
     * @param name 用户名基础部分
     * @param prefix 前缀（如"scyx"或"staff"）
     * @return 唯一的OA用户名
     */
    private String generateUniqueOaName(String name, String prefix) {
        int index = 1;
        boolean nameExists = true;
        String oaName = "";

        while (nameExists) {
                // 格式化编号为两位数字
                String indexStr = String.format("%02d", index);
                oaName = name + "/" + prefix + indexStr + "/GFZQ";

            // 查询是否存在
            LambdaQueryWrapper<IamUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IamUser::getOaName, oaName);
            int count = iamUserService.count(queryWrapper);

            if (count == 0) {
                // 不存在，可以使用
                nameExists = false;
            } else {
                // 存在，尝试下一个编号
                index++;
            }
        }
        log.info("智慧人力生成的OA用户名为:{}",oaName);
        return oaName;
    }

    public void makeRequest(IamUserCreateVo iamUserCreateVo) {
        IamUserRequestDto iamUserVo = new IamUserRequestDto();
        iamUserVo.setCompany("广发证券股份有限公司");
        iamUserVo.setEmail(iamUserCreateVo.getEmail());
        iamUserVo.setEmailInternet(iamUserCreateVo.getEmailInternet());
        iamUserVo.setEmployeeRoleName(iamUserCreateVo.getEmployeeRoleName());
        iamUserVo.setEnableDate(LocalDate.now());
        iamUserVo.setOaName(iamUserCreateVo.getOaName());
        iamUserVo.setName(iamUserCreateVo.getName());
        iamUserVo.setUsername(iamUserCreateVo.getUsername());
        iamUserVo.setOrgId(iamUserCreateVo.getOrg().getOrgId());
        IamUserOrgOrderVo iamUserOrg = new IamUserOrgOrderVo();
        iamUserOrg.setOrgId(iamUserCreateVo.getOrg().getOrgId());
        iamUserOrg.setSortOptions(1);
        iamUserOrg.setOrgName(iamUserCreateVo.getOrgName());
        iamUserVo.setIamUserOrg(iamUserOrg);
        iamUserVo.setCode(iamUserCreateVo.getCode());
        iamUserVo.setPassword(iamUserCreateVo.getPassword());
        iamUserVo.setPinyin(iamUserCreateVo.getPinyin());
        iamUserVo.setPy(iamUserCreateVo.getPy());
        iamUserVo.setStaffType(iamUserCreateVo.getStaffType());
        iamUserVo.setAcmFlag(1);
        iamUserVo.setStatus(0);
        iamUserVo.setOperationType("ADD");
        iamUserVo.setItType("0");
        iamRequestOperationManager.insert("user",0,"add",iamUserVo,null,"0", iamUserCreateVo.getItUrl(), 1,null);
    }


    /**
     * 检查账号是否重复，返回不重复的账号列表
     * @param usernames 用户账号列表
     * @return 不重复的账号列表
     */
    public Set<String> checkUsernames(List<String> usernames) {
        if (ObjectUtil.isEmpty(usernames)) {
            return new HashSet<>();
        }

        Set<String> availableUsernames = new HashSet<>();
        //先查出所有数据，避免多次查库
        LambdaQueryWrapper<IamUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(IamUser::getUsername,IamUser::getId);
        queryWrapper.in(IamUser::getUsername, usernames);
        List<IamUser> usernamesList = iamUserService.list(queryWrapper);
        for (String username : usernames) {
            if (StrUtil.isEmpty(username)) {
                continue;
            }

            IamUser userByUsername = usernamesList.stream().filter(e->e.getUsername().equals(username)).findFirst().orElse(null);
            if (ObjectUtil.isNotEmpty(userByUsername)) {
                LOGGER.info("账号{}重复", username);
            } else {
                LOGGER.info("检查与草稿箱内的账号是否重复");
                List<RowMap> list = iamRequestOperationManager.selectUserName(username);
                if (ObjectUtil.isEmpty(list)) {
                    availableUsernames.add(username);
                }
            }
        }

        return availableUsernames;
    }

}
