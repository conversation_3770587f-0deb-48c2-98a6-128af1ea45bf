#AUTHOR TIMO.LEE BY FEATURE SOLUTION

#fdu-personunid
ldap-user.fduPersonUnid=${userFduPersonUnid:fdu-personunid}

#uid
ldap-user.uid=${userUid:uid}

#cn
ldap-user.cn=${userCn:cn}

#displayname
ldap-user.displayname=${userDisplayname:displayname}


#department
ldap-user.department=${userDepartment:department}

#department
ldap-user.department2=${userDepartment2:department2}


#depttype
ldap-user.deptType=${userDeptType:depttype}

#fdu-deptname
ldap-user.fduDeptName=${userFduDeptName:fdu-deptname}


#mail
ldap-user.mail=${userMail:mail}

#mobile
ldap-user.mobile=${userMobile:mobile}

#telephonenumber
ldap-user.telephoneNumber=${userTelephoneNumber:telephonenumber}

#parentno
ldap-user.parentNo=${userParentNo:parentno}

#newno
ldap-user.newNo=${userNewNo:newno}

#fdu-py
ldap-user.fduPy=${userFduPy:fdu-py}

#tempuser
ldap-user.tempUser=${userTempUser:tempuser}

#mailfile
ldap-user.mailFile=${userMailFile:mailfile}

#mailserver
ldap-user.mailServer=${userMailServer:mailserver}

#ou
ldap-user.ou=${userOu:ou}

#Comment
ldap-user.Comment=${userComment:Description}

#Level0
ldap-user.Level0=${userLevel0:Level0}


