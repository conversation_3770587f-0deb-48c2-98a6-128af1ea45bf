{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "userInfo", "title": "获取当前用户信息", "multiple": false, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "sortOrder": {"type": "integer", "title": "排序"}, "username": {"type": "string", "title": "登陆账户"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用"}, "externalId": {"type": "string", "title": "外部Id"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "org": {"type": "object", "properties": {"id": {"type": "string"}, "sortOrder": {"type": "integer", "title": "排序"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类"}, "externalId": {"type": "string", "title": "外部Id"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "sortOrder": {"type": "integer", "title": "排序"}, "username": {"type": "string", "title": "登陆账户"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用"}, "externalId": {"type": "string", "title": "外部Id"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "ID"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/me/userinfo", "processor": "ServicePathProcessor"}, "method": "GET"}], "variables": {"selectedMenu": {"type": "string", "default": "userinfo", "title": "选中的菜单", "orderNo": 0}}, "body": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px", "children": [{"type": "FuseDoubleColumnsView", "props": {"fixedPart": "left", "fixedPartWidth": 250, "showScroll": false, "showTrigger": false}, "slots": {"left": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "margin-bottom:0px;height:auto;", "props": {"padding": 16, "shadow": false, "bordered": true, "disHover": true, "replace": false, "target": "_self", "append": false}, "slots": {"default": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "display:block;text-align:center;", "children": [{"type": "IvImage", "props": {"width": "160", "height": "160", "fit": "contain", "round": "50%", "autoWidth": false, "src": "${userInfo.data.avatar && userInfo.data.avatar.id ? utils.config.find('apiBaseUrl') + utils.config.find('file.download') + '/' + userInfo.data.avatar.id : 'raw/material/default/avatar1.png'}", "alt": "用户头像"}, "style": "display:inline-block;margin:12px;"}, {"type": "IvText", "props": {"text": "${userInfo.data.name+'('+userInfo.data.username+')'}", "maxLine": 0}, "designer": {"demo": {"props.text": "管理员(admin)"}}}]}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "right": {"children": [{"type": "SlotContainer", "style": "height:100%;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}], "auth": {"turnOn": false, "requiredPermission": [], "license": null}, "meta": {"title": "母版页面"}}