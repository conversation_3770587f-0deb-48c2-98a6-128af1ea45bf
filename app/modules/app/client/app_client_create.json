{"type": "AdaptivePage", "version": "2.0", "dataSources": [], "variables": {"formData": {"type": "object", "default": {"authorizationCode": false, "idSuffix": ""}, "title": "formData", "orderNo": 0}, "createSuccess": {"type": "boolean", "default": false, "title": "createSuccess", "orderNo": 1}, "submitResult": {"type": "object", "default": {}, "title": "submitResult", "orderNo": 2}}, "functions": {"transformed": false, "script": "export function buildFormData(formData) {\r\n  // let grantTypes = [\"refresh_token\"];\r\n  // if (formData.authorizationCode) {\r\n  //   grantTypes.push(\"authorization_code\");\r\n  //   grantTypes.push(\"implicit\");\r\n  //   delete formData.authorizationCode;\r\n  // }\r\n\r\n  // if (formData.password) {\r\n  //   grantTypes.push(\"password\");\r\n  //   delete formData.password;\r\n  // }\r\n\r\n  // if (formData.clientCredentials) {\r\n  //   grantTypes.push(\"client_credentials\");\r\n  //   delete formData.clientCredentials;\r\n  // }\r\n  formData.appId = request.params.appId;\r\n  //formData.grantTypes = grantTypes;\r\n  return formData;\r\n}\r\n\r\nexport function copy(str) {\r\n  return utils.copy(str)\r\n}\r\n\r\nexport function validateList(rule, value, callback) {\r\n  if (!value || !value.length) {\r\n    callback(new Error('请输入'));\r\n  }\r\n  if (value.some(item => !item)) {\r\n    callback(new Error('请补充完整'));\r\n  }\r\n  callback()\r\n}\r\n\r\nexport function validateAccountOrId(rule, value, callback) {\r\n  const code = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ@#$_'\r\n  for (let i = 0; i < value.length; i++) {\r\n    if (!code.includes(value[i])) {\r\n      callback(new Error('仅包含数字字母@#$_'));\r\n      return\r\n    }\r\n  }\r\n  callback()\r\n}\r\n\r\nexport function validatePassword(rule, value, callback) {\r\n  const code = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ@#$^&*_-'\r\n  for (let i = 0; i < value.length; i++) {\r\n    if (!code.includes(value[i])) {\r\n      callback(new Error('仅包含数字字母@#$^&*_-'));\r\n      return\r\n    }\r\n  }\r\n  callback()\r\n}"}, "body": [{"type": "IvForm", "props": {"model": "${formData}", "labelPosition": "right", "labelWidth": "150", "showMessage": true, "hideRequiredMark": false, "labelColon": false, "disabled": false, "prevent": true}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "客户端名称", "span": "24", "width": "100%", "value": "${formData.name}", "type": "text", "size": "default", "placeholder": "请输入客户端名称", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "客户端名称不能为空"}]}, "clearable": true, "requiredName": "ivu-form-item-required", "prop": "name"}}, {"type": "IvFormInput", "props": {"labelName": "客户端账号", "span": "24", "width": "100%", "value": "${formData.idSuffix}", "type": "text", "size": "default", "placeholder": "请输入客户端账号", "border": true, "required": false, "prop": "idSuffix", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "客户端账号不能为空"}, {"type": "method", "value": "method", "label": "自定义函数", "pattern": "", "method": "${validateAccountOrId}", "message": ""}]}, "requiredName": "ivu-form-item-required", "clearable": false, "maxlength": 12, "autocomplete": "off", "showWordLimit": false}, "slots": {"append": {"children": [{"type": "IvText", "props": {"text": "自动生成", "showTitle": false, "maxline": 0}, "style": "cursor:pointer;", "events": {"click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "28", "name": "formData.idSuffix", "value": "${utils.random(12,'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ@#$_-')}"}], "description": null}, "id": "action-27", "activeOn": null}]}}}, {"type": "Icon", "props": {"type": "md-copy", "size": 16}, "style": "cursor:pointer;", "events": {"click.stop": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "copy", "params": [{"name": "str", "value": "${formData.idSuffix}"}], "description": null}, "id": "action-26", "activeOn": null}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvText", "props": {"text": "文本", "showTitle": false, "maxline": 0}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": true}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "提示信息", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}, "label": {"children": [{"type": "IvText", "props": {"text": "客户端账号", "showTitle": false, "maxline": 0}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": true}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "客户端账号由【22位应用ID+“.”+6位客户端账号后缀】组成", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "collapse": false}}}}, {"type": "IvFormInput", "props": {"labelName": "客户端账号", "span": "24", "width": "100%", "value": "${formData.secret}", "type": "password", "size": "default", "placeholder": "请输入客户端密钥", "border": true, "required": false, "prop": "secret", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "客户端秘钥不能为空"}]}, "requiredName": "ivu-form-item-required", "clearable": false, "password": true, "autocomplete": "new-password"}, "slots": {"append": {"children": [{"type": "IvText", "props": {"text": "自动生成", "showTitle": false, "maxline": 0}, "style": "cursor:pointer;", "events": {"click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "28", "name": "formData.secret", "value": "${utils.random(18,'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ@#$^&*_-')}"}], "description": null}, "id": "action-27", "activeOn": null}]}}}, {"type": "Icon", "props": {"type": "md-copy", "size": 16}, "style": "cursor:pointer;", "events": {"click.stop": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "copy", "params": [{"name": "str", "value": "${formData.secret}"}], "description": null}, "id": "action-26", "activeOn": null}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvText", "props": {"text": "文本", "showTitle": false, "maxline": 0}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": true}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "提示信息", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}, "label": {"children": [{"type": "IvText", "props": {"text": "客户端密钥", "showTitle": false, "maxline": 0}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "collapse": false}}}}], "style": "", "class": "", "id": "form1111", "visible": "${!createSuccess}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "designer": {"lock": false}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px", "children": [{"type": "Card", "designer": {"movein": false, "lock": false}, "style": "text-align:left;", "props": {"padding": "16", "bordered": true, "disHover": true, "shadow": false, "replace": false, "append": false}, "class": "", "id": "", "visible": "${createSuccess}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "text-align:center;", "children": [{"type": "Icon", "props": {"type": "md-checkmark-circle", "size": "76", "color": "rgba(25,190,107,1)"}}], "designer": {"lock": true}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:0px;text-align:center;", "children": [{"type": "IvText", "props": {"text": "客户端账号创建成功", "showTitle": false, "maxline": 0}, "style": "font-size:20px;font-weight:bold;margin:16px;"}], "designer": {"lock": false}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "border-style:solid;border-width:1px;border-color:#dcdee2;border-radius:5px;display:inline-block;text-align:center;margin-bottom:24px;padding:20px;width:100%;", "children": [{"type": "IvText", "props": {"text": "为确保客户端账号的安全，创建完成后客户端密钥不可见，请自行记住客户端密钥", "showTitle": false, "maxline": 0}, "style": "color:gray;display:inline;"}, {"type": "IvLink", "props": {"tag": "a", "text": "一键复制", "type": "page", "linkColor": true}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "utils", "funcExp": "utils.copy('客户端账号：' + submitResult.clientId + '\\n' + '客户端密钥：' + submitResult.clientSecret)", "params": [], "description": null}, "description": null, "id": "action-37", "activeOn": null}]}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designer": {"lock": false}, "style": "margin-bottom:16px;margin-top:16px;text-align:center;", "children": [{"type": "IvText", "props": {"text": "客户端账号: ", "showTitle": false, "maxline": 0}}, {"type": "IvText", "props": {"text": "${submitResult.clientId}", "showTitle": false, "maxline": 0}, "style": "color:gray;"}]}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px;text-align:center;", "children": [{"type": "IvText", "props": {"text": "客户端密钥:", "showTitle": false, "maxline": 0}}, {"type": "IvText", "props": {"text": "${submitResult.clientSecret}", "showTitle": false, "maxline": 0}, "style": "color:gray;"}]}]}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px;text-align:center;", "children": [{"type": "IvButton", "props": {"text": "返回客户端账号管理", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-32"}]}}}]}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "class": "", "id": "", "visible": "${createSuccess}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "header": [{"type": "IvText", "props": {"text": "创建客户端账号", "showTitle": false, "maxline": 0}}], "footer": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-75", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "${!createSuccess}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvButton", "props": {"text": "创建", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1111", "description": null}, "id": "action-58", "activeOn": null}, {"type": "Action.Http", "inputs": {"resultName": "createResult", "schema": {"tenantId": {"type": "string"}, "clientId": {"type": "string", "title": "客户端账号"}, "clientSecret": {"type": "string", "title": "客户端密钥"}, "name": {"type": "string", "title": "客户端名称"}, "appId": {"type": "string", "title": "所属应用ID"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/apps/clients/extend", "processor": "ServicePathProcessor"}, "method": "POST", "data": "${buildFormData(formData)}"}, "id": "action-25", "activeOn": null}, {"type": "Action.Output", "inputs": {"output": "${createResult}", "description": null}, "id": "action-281", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "54", "name": "createSuccess", "value": "${true}"}, {"_uid": "60", "name": "submitResult", "value": "${createResult}"}], "description": null}, "description": null, "id": "action-53", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "${!createSuccess}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "meta": {"title": "新增应用客户端", "name": "app_client_create", "packageName": "app.client"}}