{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "dmAssociations", "title": "查询列表", "multiple": true, "schema": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "typeId": {"type": "string", "title": "设备类型"}, "code": {"type": "string", "title": "设备编码"}, "imei": {"type": "string", "title": "移动网络设备识别码"}, "meid": {"type": "string", "title": "设备识别码"}, "agentId": {"type": "string", "title": "管理代理"}, "agentVersionId": {"type": "string", "title": "管理代理版本"}, "ownerId": {"type": "string", "title": "设备拥有者ID"}, "ownerType": {"type": "string", "title": "设备拥有者类型"}, "model": {"type": "string", "title": "设备型号，物理设备类型，如：iPhone15,Xiaomi MIX 3"}, "mfr": {"type": "string", "title": "制造商"}, "size": {"type": "string", "title": "设备尺寸，mobile/pad/pc"}, "os": {"type": "string", "title": "操作系统"}, "osVersion": {"type": "string", "title": "操作系统版本"}, "sim": {"type": "string", "title": "SIM卡"}, "ip": {"type": "string", "title": "设备IP"}, "mac": {"type": "string", "title": "MAC地址"}, "status": {"type": "string", "title": "设备状态"}, "enrolledAt": {"type": "string", "title": "设备注册时间"}, "forbidAt": {"type": "string", "title": "设备禁止时间"}, "loseAt": {"type": "string", "title": "设备遗失时间"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "type": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}}, "agent": {"type": "object", "properties": {"id": {"type": "string", "title": "主键ID"}, "name": {"type": "string", "title": "名称"}, "clientId": {"type": "string", "title": "关联的应用客户端"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "client": {}, "properties": {"type": "object"}}}, "agentVersion": {"type": "object", "properties": {"id": {"type": "string", "title": "主键ID"}, "agentId": {"type": "string", "title": "管理代理"}, "version": {"type": "string", "title": "版本"}, "versionAlias": {"type": "string", "title": "版本别名"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}}, "properties": {"type": "object"}}, "fetchWhen": "${utils.FlyVueCore.PermissionService.hasPerm('t.deviceBindM_')}", "lazy": true, "autoReload": true, "url": "/api/tenant/dm_associations/extend/extend", "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${buildFilters()}", "search": null, "expand": "", "joins": null, "orderby": "", "total": "${true}"}}, {"id": "devicesList", "title": "查询列表", "multiple": true, "schema": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "typeId": {"type": "string", "title": "设备类型"}, "code": {"type": "string", "title": "设备编码"}, "imei": {"type": "string", "title": "移动网络设备识别码"}, "meid": {"type": "string", "title": "设备识别码"}, "agentId": {"type": "string", "title": "管理代理"}, "agentVersionId": {"type": "string", "title": "管理代理版本"}, "ownerId": {"type": "string", "title": "设备拥有者ID"}, "ownerType": {"type": "string", "title": "设备拥有者类型"}, "model": {"type": "string", "title": "设备型号，物理设备类型，如：iPhone15,Xiaomi MIX 3"}, "mfr": {"type": "string", "title": "制造商"}, "size": {"type": "string", "title": "设备尺寸，mobile/pad/pc"}, "os": {"type": "string", "title": "操作系统"}, "osVersion": {"type": "string", "title": "操作系统版本"}, "sim": {"type": "string", "title": "SIM卡"}, "ip": {"type": "string", "title": "设备IP"}, "mac": {"type": "string", "title": "MAC地址"}, "status": {"type": "string", "title": "设备状态"}, "enrolledAt": {"type": "string", "title": "设备注册时间"}, "forbidAt": {"type": "string", "title": "设备禁止时间"}, "loseAt": {"type": "string", "title": "设备遗失时间"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "type": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}}, "agent": {"type": "object", "properties": {"id": {"type": "string", "title": "主键ID"}, "name": {"type": "string", "title": "名称"}, "clientId": {"type": "string", "title": "关联的应用客户端"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "client": {}, "properties": {"type": "object"}}}, "agentVersion": {"type": "object", "properties": {"id": {"type": "string", "title": "主键ID"}, "agentId": {"type": "string", "title": "管理代理"}, "version": {"type": "string", "title": "版本"}, "versionAlias": {"type": "string", "title": "版本别名"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}}, "properties": {"type": "object"}}, "fetchWhen": "${utils.FlyVueCore.PermissionService.hasPerm('t.deviceM_')}", "lazy": true, "autoReload": true, "url": "/api/tenant/dm_devices", "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "typeId eq 'personal'", "search": "${search2}", "expand": null, "joins": null, "orderby": "updatedAt desc", "total": "${true}"}}], "variables": {"search1": {"type": "string", "default": "", "title": "search1", "orderNo": 0, "id": "search1"}, "t": {"type": "boolean", "default": false, "title": "t", "orderNo": 1, "id": "t"}, "search2": {"type": "string", "default": "", "title": "search2", "orderNo": 2, "id": "search2"}, "unbindAt": {"type": "string", "default": "0", "title": "unbindAt", "orderNo": 3, "id": "unbindAt"}}, "functions": {"transformed": false, "script": "export function handleBeforeChange(row) {\r\n  return () => {\r\n    return new Promise((resolve) => {\r\n      const that = utils.FlyVueCore.webApplication.getCurrentVue()\r\n      that.$Modal.confirm({\r\n        title: '提示',\r\n        loading: true,\r\n        content: row.secretId ? `是否确认禁止用户 ${row.userName} 与设备 ${row.model} 外网访问？确认后，将不再支持用户使用当前设备在企业网络边界外安全访问内部资源。` : `是否确认同意用户 ${row.userName} 与设备 ${row.model} 外网访问？确认后，将支持用户使用当前设备在企业网络边界外安全访问内部资源。`,\r\n        onOk: async () => {\r\n          let http = new utils.FlyVueCore.HttpRequest()\r\n          try {\r\n            await http.patch(`/api/tenant/dm_associations/extend/${row.id}/externalAccess?enabled=${!row.secretId}`)\r\n            that.$Message.success('操作成功');\r\n            renderContext.dataSources.dmAssociations.deFetch()\r\n            resolve();\r\n          } catch (error) {\r\n\r\n          } finally {\r\n            that.$Modal.remove();\r\n          }\r\n        }\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nexport function buildFilters() {\r\n  const { search1, unbindAt } = renderContext.variables\r\n  let ret = ''\r\n  if (search1.value) {\r\n    ret = `(d.model co '${search1.value}' or u.name co '${search1.value}')`\r\n  }\r\n  if (unbindAt.value === '0') {\r\n    ret = ret ? ret += ` and unbindAt is null` : `unbindAt is null`\r\n  } else if (unbindAt.value === '1') {\r\n    ret = ret ? ret += ` and unbindAt is not null` : `unbindAt is not null`\r\n  }\r\n  return ret\r\n}"}, "body": [{"type": "Card", "designer": {"movein": false}, "props": {"padding": 12, "shadow": false, "bordered": true, "disHover": false, "target": "_self"}, "id": "com_5236", "slots": {"default": {"children": [{"type": "Tabs", "designer": {"moveChild": false, "movein": false}, "props": {"type": "line", "tabPanel": [], "lazyLoad": true, "animated": false, "name": "le_Tabs1702260502284_110"}, "children": [{"type": "TabPane", "props": {"index": 1, "tab": "le_Tabs1702260502284_110", "label": "设备绑定"}, "children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": true, "data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "visibleHeaderOp": true, "visibleMore": false, "padding": 0, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": "${dmAssociations.loading}", "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${dmAssociations}", "fixedIndex": "left", "filters": {"logic": "or", "filters": []}}, "id": "com_5239", "slots": {"header": {"children": [{"type": "IvButton", "props": {"type": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${dmAssociations}"}, "description": null, "continueWhenDeactive": 0, "id": "action-86"}]}}, "id": "com_5240"}, {"type": "IvSelect", "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": [{"label": "已绑定", "value": "0", "color": "primary"}, {"value": "1", "label": "已解绑", "color": "primary"}, {"value": "2", "label": "全部", "color": "primary"}], "keyNames": {}, "size": "default", "placement": "bottom-start", "border": true, "clearable": false, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "value": "${unbindAt}", "style": "width:150px;", "class": "fly-mr-xs "}, "style": "width:150px;", "class": "fly-mr-xs ", "id": "com_5242"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "根据用户、设备名称搜索", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "value": "${search1}", "search": true, "enterButton": false, "style": "width:200px;"}, "style": "width:200px;", "id": "com_5243"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "用户名称", "keyName": "userName", "width": 150, "show": true, "children": [], "fixed": "left", "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5244", "slots": {"content": {"children": [{"type": "IamEntityInfo", "props": {"info": "${{deleted:tableCell.row.userDeleted,name:tableCell.row.userName,enabled: tableCell.row.userEnabled,restricted: tableCell.row.userRestricted}}"}, "id": "com_5245"}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "登录账号", "keyName": "date", "show": true, "width": 120, "children": [], "fixed": "left", "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5246", "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.loginAccount + '@' + tableCell.row.tenantCode}"}, "id": "com_5247"}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "所属组织", "keyName": "orgName", "show": true, "width": 120, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5248"}, {"type": "IvTableGridColumn", "props": {"title": "绑定状态", "keyName": "bind", "show": true, "width": 120, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "style": "", "id": "com_424", "class": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.unbindAt ? '已解绑': '已绑定'}", "style": "${tableCell.row.unbindAt ? 'color: #ff9900' : 'color: #00CD00'\n}"}, "style": "${tableCell.row.unbindAt ? 'color: #ff9900' : 'color: #00CD00'\n}", "id": "com_5253"}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "设备名称", "keyName": "model", "show": true, "width": 100, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5254", "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "${tableCell.row.model}", "type": "page", "linkColor": true, "replace": false}, "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "./tenant.devices.detail", "variables": [{"_uid": "34", "name": "devicesId", "value": "${tableCell.row.deviceId}"}], "tab": false, "cacheOpener": true, "disableOpenerAnimation": false, "redirect": false, "description": null}, "id": "action-33", "activeOn": null, "continueWhenDeactive": 0}]}}, "auth": {"turnOn": true, "forbiddenStatus": "disabled", "requiredPermission": []}, "id": "com_425"}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "操作系统版本", "keyName": "osVersion", "show": true, "width": 100, "children": []}, "designer": {"movein": false}, "id": "com_5255"}, {"type": "IvTableGridColumn", "props": {"title": "MAC地址", "keyName": "mac", "show": true, "children": [], "minWidth": 150, "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5256"}, {"type": "IvTableGridColumn", "props": {"title": "设备编码", "keyName": "meid", "show": true, "children": [], "minWidth": 150, "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5257"}, {"type": "IvTableGridColumn", "props": {"title": "绑定时间", "keyName": "bindAt", "show": true, "width": 150, "children": [], "dataType": "datetime", "dataTimeType": "YYYY-MM-DD HH:mm", "sortable": true, "align": "left", "verticalAlign": "le-align-middle", "filterMultiple": true}, "designer": {"movein": false}, "id": "com_5258"}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "解绑时间", "keyName": "unbindAt", "dataType": "datetime", "dataTimeType": "YYYY-MM-DD HH:mm:ss", "width": 150, "align": "left", "sortable": true, "children": [], "verticalAlign": "le-align-middle", "filterMultiple": true}, "id": "com_5259"}, {"type": "IvTableGridColumn", "props": {"title": "最后活跃时间", "keyName": "lastUse", "show": true, "width": 150, "children": [], "dataType": "datetime", "dataTimeType": "YYYY-MM-DD HH:mm", "sortable": true}, "designer": {"movein": false}, "id": "com_5260"}, {"type": "IvTableGridColumn", "props": {"title": "操作", "width": 80, "keyName": "action", "show": true, "align": "center", "children": [], "fixed": "right"}, "designer": {"movein": false}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.deviceBindM_.manage"]}, "id": "com_5263", "style": "", "class": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "解绑", "linkColor": true, "disabled": "${tableCell.row.unbindAt}", "replace": false}, "class": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确认解绑用户 ${tableCell.row.userName} 与设备 ${tableCell.row.model} ？解绑后将不再支持管理当前设备和用户绑定信息，用户需要重新绑定方可使用设备进行二次身份认证。`}", "okText": "确定", "icon": "", "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-71", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": "/api/tenant/dm_associations/extend/{id}/unbind", "method": "DELETE", "params": {"id": "${tableCell.row.id}"}, "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-73", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "continueWhenDeactive": 0, "id": "action-78"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${dmAssociations}"}, "description": null, "continueWhenDeactive": 0, "id": "action-80"}]}}, "id": "com_5264"}, {"type": "IvLink", "props": {"tag": "a", "text": "${tableCell.row.enabled? '禁用' : '启用'}", "linkColor": true, "disabled": false, "replace": false, "style": "${tableCell.row.enabled ? 'color: #3457CC' : 'color: #ff9900'}"}, "class": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${tableCell.row.enabled ? `是否确认禁用用户：${tableCell.row.userName} 和设备 ${tableCell.row.model} ？禁用后，用户讲无法使用该设备登录，请谨慎操作！` : `是否确认启用用户：${tableCell.row.userName} 和设备 ${tableCell.row.model} ？启用后，用户将正常使用该设备登录。`}", "okText": "确定", "icon": "", "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-71", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": "/api/tenant/dm_associations/extend/{id}/enabled", "method": "PATCH", "params": {"id": "${tableCell.row.id}", "enabled": "${tableCell.row.enabled ? 'false': 'true'}"}, "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-73", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "continueWhenDeactive": 0, "id": "action-78"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${dmAssociations}"}, "description": null, "continueWhenDeactive": 0, "id": "action-80"}]}}, "style": "${tableCell.row.enabled ? 'color: #3457CC' : 'color: #ff9900'}", "id": "com_5266"}, {"type": "IvLink", "props": {"tag": "a", "text": "删除", "linkColor": true, "disabled": "${!tableCell.row.unbindAt}", "replace": false}, "class": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确认删除用户 ${tableCell.row.userName} 与设备 ${tableCell.row.model} 的记录？此操作不可恢复，请谨慎操作！！！`}", "okText": "确定", "icon": "", "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-71", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": "/api/tenant/dm_associations/extend/{id}", "method": "DELETE", "params": {"id": "${tableCell.row.id}"}, "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-73", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-78", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${dmAssociations}"}, "description": null, "continueWhenDeactive": 0, "id": "action-80"}]}}, "style": null, "id": "com_5268"}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.deviceBindM_.read"]}, "id": "com_5238"}, {"type": "TabPane", "props": {"label": "设备管理", "tab": "le_Tabs1702260502284_110", "index": 2}, "children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": true, "data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "visibleHeaderOp": true, "visibleMore": false, "padding": 0, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": "${devicesList.loading}", "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${devicesList}"}, "id": "com_5271", "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}, "slots": {"header": {"children": [{"type": "IvButton", "props": {"type": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${devicesList}"}, "description": null, "continueWhenDeactive": 0, "id": "action-90"}]}}, "id": "com_5272"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "根据设备名称搜索", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "value": "${search2}", "search": true, "enterButton": false, "style": "width:200px;"}, "style": "width:200px;", "id": "com_5274"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "设备名称", "keyName": "model", "show": true, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5275"}, {"type": "IvTableGridColumn", "props": {"title": "设备编码", "keyName": "meid", "show": true, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5276"}, {"type": "IvTableGridColumn", "props": {"title": "MAC地址", "keyName": "mac", "show": true, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5277"}, {"type": "IvTableGridColumn", "props": {"title": "操作系统", "keyName": "os", "show": true, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5278"}, {"type": "IvTableGridColumn", "props": {"title": "操作版本", "keyName": "osVersion", "show": true, "children": [], "ellipsis": true, "tooltip": true}, "designer": {"movein": false}, "id": "com_5279"}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "禁用时间", "keyName": "forbidAt", "width": 150, "dataType": "datetime", "dataTimeType": "YYYY-MM-DD HH:mm"}, "id": "com_431"}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "遗失时间", "keyName": "loseAt", "width": 150, "dataType": "datetime", "dataTimeType": "YYYY-MM-DD HH:mm", "children": []}, "id": "com_432"}, {"type": "IvTableGridColumn", "props": {"title": "操作", "width": 140, "keyName": "action", "show": true, "align": "left", "children": []}, "designer": {"movein": false}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.deviceM_.manage"]}, "id": "com_5280", "style": "", "class": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "编辑", "linkColor": true, "disabled": false, "replace": false}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "./tenant.devices.detail", "variables": [{"_uid": "338", "name": "devicesId", "value": "${tableCell.row.id}"}], "cacheOpener": false}, "description": null, "continueWhenDeactive": 0, "id": "action-337"}]}}, "id": "com_5281", "style": null}, {"type": "IvLink", "props": {"tag": "a", "linkColor": true, "text": "${tableCell.row.status === 'N' ? '禁用' : '启用'}", "style": "${tableCell.row.status === 'N' ? 'color: #3457CC' : 'color: #ff9900'}", "class": "px-sm", "disabled": "${tableCell.row.status === 'L'}"}, "class": "px-sm", "style": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${tableCell.row.status === 'N' ? `是否确认禁用设备 ${tableCell.row.model} ？禁用后将阻止访问基于设备条件保护、基于设备进行身份认证的企业资源。` : `是否确认启用设备 ${tableCell.row.model} ？启用后将恢复用户支持使用当前设备进行身份认证。`}", "okText": "确定", "icon": "", "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-140", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": "/api/tenant/dm_devices/{id}/status", "method": "PATCH", "params": {"id": "${tableCell.row.id}", "status": "${tableCell.row.status === 'N' ? 'F': 'N'}"}}, "description": null, "continueWhenDeactive": 0, "id": "action-142"}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "continueWhenDeactive": 0, "id": "action-151"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${devicesList}"}, "description": null, "continueWhenDeactive": 0, "id": "action-153"}]}}, "id": "com_5283", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "linkColor": true, "text": "${tableCell.row.status === 'L' ? '找回' : '遗失'}", "style": "${tableCell.row.status === 'N' ? 'color: #3457CC' : 'color: #ff9900'}", "class": "px-sm", "disabled": "${tableCell.row.status === 'F'}"}, "class": "px-sm", "style": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${tableCell.row.status === 'N' ? `是否确认遗失设备 ${tableCell.row.model} ？确定遗失后将会清空设备上已经绑定用户的所有相关信息，并且用户不支持使用当前设备登录，请谨慎操作！` : `是否确认找回设备 ${tableCell.row.model} ？确定找回后支持用户使用当前设备进行登录或二次身份操作。`}", "okText": "确定", "icon": "", "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-140", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": "/api/tenant/dm_devices/{id}/status", "method": "PATCH", "params": {"id": "${tableCell.row.id}", "status": "${tableCell.row.status === 'N' ? 'L': 'N'}"}, "description": null}, "description": null, "continueWhenDeactive": 0, "id": "action-142", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "continueWhenDeactive": 0, "id": "action-151"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${devicesList}"}, "description": null, "continueWhenDeactive": 0, "id": "action-153"}]}}, "id": "com_5283", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.deviceM_.read"]}, "id": "com_5270"}], "id": "com_5237"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "master": "app.device.layout", "auth": {"turnOn": false, "requiredPermission": [], "license": null}, "meta": {"title": "个人设备", "platform": "pc", "package": "iam-console"}}