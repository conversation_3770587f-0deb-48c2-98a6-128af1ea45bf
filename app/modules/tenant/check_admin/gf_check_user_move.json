{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "userData", "title": "根据部门id获取用户信息", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "Xs4Fwa8GGNMxSBbUNKFgxd", "value": "/api/tenant/requestOperation/getUserList", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"orgId": "${orgId}"}}, {"id": "userInfo", "multiple": false, "lazy": false, "autoReload": true, "url": "/api/tenant/users/{id}", "method": "GET", "params": {"id": "${request.params.userId}", "select": null}}, {"id": "requestDetail", "title": "获取详情", "multiple": false, "schema": {"properties": {"type": "object"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectStr": {"type": "string", "title": "操作对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "newIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织ID"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户"}, "employeeRoleName": {"type": "string", "title": "角色"}, "phone": {"type": "string", "title": "座机"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型"}, "otherOrgId": {"type": "array", "title": "其他群组ID"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}}}, "oldIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织ID"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户"}, "employeeRoleName": {"type": "string", "title": "角色"}, "phone": {"type": "string", "title": "座机"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型"}, "otherOrgId": {"type": "array", "title": "其他群组ID"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}}}, "newIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼"}, "businessCode": {"type": "string", "title": "业务代码"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型"}, "groupType": {"type": "string", "title": "群组类型"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型"}, "member": {"type": "array", "title": "成员"}, "groupMember": {"type": "array", "title": "组织成员"}, "otherOrg": {"type": "array", "title": "其他群组"}}}, "oldIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼"}, "businessCode": {"type": "string", "title": "业务代码"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型"}, "groupType": {"type": "string", "title": "群组类型"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型"}, "member": {"type": "array", "title": "成员"}, "groupMember": {"type": "array", "title": "组织成员"}, "otherOrg": {"type": "array", "title": "其他群组"}}}, "map": {"type": "object"}, "newStr": {"type": "array"}, "oldStr": {"type": "array"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getDetail", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${request.params.id}"}}], "variables": {"sortOrder": {"type": "string", "default": "", "title": "sortOrder", "orderNo": 0}, "orgId": {"type": "string", "default": "", "title": "orgId", "orderNo": 1}, "sortOptions": {"type": "number", "default": 0, "title": "sortOptions", "orderNo": 2}, "itType": {"type": "string", "default": "", "title": "itType", "orderNo": 3}, "itUrl": {"type": "string", "default": "", "title": "itType", "orderNo": 4}}, "orchestrations": {"Qx6ZUCsoLBq": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-48-condition"}, "action-48-condition": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.operationType == 'update'}", "description": "${tableCell.row.operationType == 'update'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}"}]}, "next": ["139e28a8-d4ed-4631-81b8-388d147189c2", "de17e820-d419-4300-b4d1-933a6d6ebea1", "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9", "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a", "17d85de0-0b53-4dc8-a5e5-7d82d56115be"]}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "139e28a8-d4ed-4631-81b8-388d147189c2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_user", "requestParams": [{"_uid": "258", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "de17e820-d419-4300-b4d1-933a6d6ebea1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_org", "requestParams": [{"_uid": "266", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "77", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.zuzhishanchufuhe", "requestParams": [{"_uid": "199", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "17d85de0-0b53-4dc8-a5e5-7d82d56115be": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.yo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestParams": [{"_uid": "200", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}}}}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "margin-top:12px;", "props": {"icon": "md-card", "padding": 0, "bordered": true, "disHover": false, "shadow": false, "replace": false, "append": false}, "slots": {"default": {"children": [{"type": "IvForm", "props": {"model": "${requestDetail}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${itType}", "labelWidth": 140, "dataDictionary": "IT_TYPE"}, "class": "", "style": "margin:0px;padding:0px;"}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${itUrl}", "labelWidth": 140}, "style": "", "class": ""}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.name}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.sortOrder}", "labelWidth": 140}, "style": "text-align:left;margin:0px;"}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": [{"label": "排在最上", "value": "0"}, {"value": "1", "label": "排在最下"}, {"value": "2", "label": "排在谁之后"}], "keyNames": {}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${sortOptions}", "labelWidth": 140}}, {"type": "IamFormOrgSelect", "designer": {"movein": false}, "props": {"labelName": "移动部门", "span": "24", "type": "input", "multiple": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "showConfig": false, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "请选择组织"}]}, "value": "${orgId}", "requiredName": "ivu-form-item-required", "labelWidth": 140}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "padding:12px;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": true, "data": [], "tableCols": [], "enableSelectionCol": true, "enableIndexCol": false, "visibleHeaderOp": false, "visibleMore": false, "padding": 12, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": false, "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${userData}"}, "slots": {"header": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "显示名", "keyName": "name", "show": true, "children": []}, "designer": {"movein": false}}, {"type": "IvTableGridColumn", "props": {"title": "排序号", "keyName": "sortOrder", "show": true, "width": 120, "children": []}, "designer": {"movein": false}}]}}}], "style": "margin-top:12px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "text-align:right;padding-top:12px;padding-bottom:12px;", "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "long": false}, "events": {"click": {"actions": [{"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/operationUser", "method": "POST", "params": null, "data": {"objectId": "${userInfo.data.id}", "status": "1", "operationType": "${'move'}", "sortOrder": "${userInfo.data.sortOrder}", "orgId": "${orgId}", "sortOptions": "${sortOptions}", "id": "${request.params.id}", "itType": "${itType}", "itUrl": "${itUrl}"}, "description": null}, "id": "action-53", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-65"}, {"type": "Action.CloseDialog", "inputs": {"description": null}, "id": "action-67"}]}}}]}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "用户移动", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}, "style": "padding-top:12px;"}], "auth": {"turnOn": true, "requiredPermission": [], "license": null}, "meta": {"title": "用户复核移动列表", "name": "role_list", "packageName": "tenant.public_role"}}