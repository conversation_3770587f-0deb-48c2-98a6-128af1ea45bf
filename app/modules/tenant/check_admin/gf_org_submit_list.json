{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "checkData", "title": "查询数据列表", "multiple": true, "schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "Xs4Fwa8GGNMxSBbUNKFgxd", "value": "/api/tenant/requestOperation/getList", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": null, "expand": null, "joins": null, "orderby": null, "total": "${true}"}, "data": {"name": "${name}", "objectType": "${'org'}"}}], "variables": {"keyword": {"type": "string", "default": "", "title": "", "orderNo": 0}, "submitBy": {"type": "string", "default": "", "title": "submitBy", "orderNo": 1}, "name": {"type": "string", "default": "", "title": "查询条件", "orderNo": 2}, "selectRequest": {"type": "array", "title": "所选择的复核单", "orderNo": 3}, "selectRequestId": {"type": "array", "title": "选择的复核单id", "orderNo": 4}}, "orchestrations": {"Qx6ZUCsoLBq": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-48-condition"}, "action-48-condition": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.operationType == 'update'}", "description": "${tableCell.row.operationType == 'update'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}"}]}, "next": ["139e28a8-d4ed-4631-81b8-388d147189c2", "de17e820-d419-4300-b4d1-933a6d6ebea1", "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9", "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a", "17d85de0-0b53-4dc8-a5e5-7d82d56115be"]}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "139e28a8-d4ed-4631-81b8-388d147189c2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_user", "requestParams": [{"_uid": "258", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "de17e820-d419-4300-b4d1-933a6d6ebea1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_org", "requestParams": [{"_uid": "266", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "77", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.zuzhishanchufuhe", "requestParams": [{"_uid": "199", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "17d85de0-0b53-4dc8-a5e5-7d82d56115be": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.yo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestParams": [{"_uid": "200", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}}}, "VG7RFZr2vI-": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "1aa0564c-daec-42d8-b4cd-ee0679961c2f"}, "1aa0564c-daec-42d8-b4cd-ee0679961c2f": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.operationType == 'add'}", "description": "${tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.operationType == 'update'}", "description": "${tableCell.row.operationType == 'update'}"}]}, "next": ["37bec0f6-df53-4681-852e-2ed35689ac06", "cf175b21-99cb-4e4a-984c-46fae552e991"]}, "37bec0f6-df53-4681-852e-2ed35689ac06": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.gf_org.gf_add_org_info", "requestParams": [{"_uid": "91", "name": "requestId", "value": "${tableCell.row.id}"}, {"_uid": "92", "name": "type", "value": "${'edit'}"}], "dialogSettings": {"width": "1200", "continueOnClose": "${true}"}}, "next": "8b87cdb7-7c6f-479e-a511-591d6eebd9f3"}, "cf175b21-99cb-4e4a-984c-46fae552e991": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.gf_org.gf_edit_org_info", "requestParams": [{"_uid": "93", "name": "requestId", "value": "${tableCell.row.id}"}, {"_uid": "94", "name": "type", "value": "${'edit'}"}], "dialogSettings": {"width": "1200", "continueOnClose": "${true}"}}, "next": "b752a306-858b-4b2d-bf0f-c0924e1565ec"}, "8b87cdb7-7c6f-479e-a511-591d6eebd9f3": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "df5ec7cf-bc80-4012-92a4-ac8a4d7150a5"}, "b752a306-858b-4b2d-bf0f-c0924e1565ec": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "df5ec7cf-bc80-4012-92a4-ac8a4d7150a5"}, "df5ec7cf-bc80-4012-92a4-ac8a4d7150a5": {"type": "Action.End", "inputs": {}, "next": null}}}, "wx-cx4lxb0n": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "88b9e36d-4795-46f9-955f-1b5ea12de33d"}, "action-264": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchUpdate", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"status": "5"}, "data": "${selectRequestId}"}, "next": "cf28489f-a46f-4f7a-9d0e-cbb487f99460"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "88b9e36d-4795-46f9-955f-1b5ea12de33d": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectRequestId.length > 0}", "description": "${selectRequestId.length > 0}"}, {"condition": "${selectRequestId.length == 0}", "description": "${selectRequestId.length == 0}"}]}, "next": ["276086a0-6553-473b-9fc3-13264b985b79", "bbe213b5-390f-401d-8daa-ca778965778b"]}, "cf28489f-a46f-4f7a-9d0e-cbb487f99460": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已删除", "duration": 1.5, "closable": false, "background": false}, "next": "e620bdae-8e07-4818-851f-38a234ca2615"}, "bbe213b5-390f-401d-8daa-ca778965778b": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${'请至少选择一条数据'}", "duration": 1.5, "closable": false, "background": false}, "next": null}, "e620bdae-8e07-4818-851f-38a234ca2615": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "end"}, "276086a0-6553-473b-9fc3-13264b985b79": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请确认是否删除？", "okText": "确定", "icon": ""}, "next": "action-264"}}}, "a30t54l3b00": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "64c30f64-5ea1-4349-a6bb-dc2d8edac4a8"}, "action-255": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchUpdate", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"status": "1"}, "data": "${selectRequestId}"}, "next": "d06b8f3d-0806-4a24-b826-13be6a525fbc"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "adf9e868-5804-47f4-93a7-caccd3104dd3": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectRequestId == null || selectRequestId.length < 1}", "description": "${selectRequestId == null || selectRequestId.length < 1}"}, {"condition": "${selectRequestId.length >= 1}", "description": "${selectRequestId.length >= 1}"}]}, "next": ["6cbac6b8-5d8b-4ad7-8965-2b840136eaaa", "d25b37ac-dc6c-430d-b1da-d2376b39bd16"]}, "d06b8f3d-0806-4a24-b826-13be6a525fbc": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已全部提交到复核列表", "duration": 1.5, "closable": false, "background": false}, "next": "37b65955-5879-47b1-8ee2-2b89941d828c"}, "6cbac6b8-5d8b-4ad7-8965-2b840136eaaa": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "请至少选择一条数据", "duration": 1.5, "closable": false, "background": false}, "next": null}, "37b65955-5879-47b1-8ee2-2b89941d828c": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "end"}, "d25b37ac-dc6c-430d-b1da-d2376b39bd16": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请确认是否提交？", "okText": "确定", "icon": ""}, "next": "action-255"}, "64c30f64-5ea1-4349-a6bb-dc2d8edac4a8": {"type": "Action.Http", "inputs": {"resultName": "names", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/requestsExists", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"type": "${'org'}"}, "data": "${selectRequestId}"}, "next": "874ceb64-12d7-4b76-aa88-576fd8332884"}, "874ceb64-12d7-4b76-aa88-576fd8332884": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${names==null||names.length==0}", "description": "${names==null||names.length==0}"}, {"condition": "${names&&names.length>0}", "description": "${names&&names.length>0}"}]}, "next": ["adf9e868-5804-47f4-93a7-caccd3104dd3", "192deead-d15f-4e8b-9089-ec0561e167ee"]}, "192deead-d15f-4e8b-9089-ec0561e167ee": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${`组织${names}存在未复核的请求`}", "duration": 1.5, "closable": false, "background": false}, "next": null}}}, "HmbJ9CCBGqB": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "af56a5f8-675f-42f8-bb01-82e7387c171e"}, "af56a5f8-675f-42f8-bb01-82e7387c171e": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.operationType == 'update'||tableCell.row.operationType =='move'}", "description": "${tableCell.row.operationType == 'update'||tableCell.row.operationType =='move'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}"}]}, "next": ["a0be7d8a-e6a5-4621-bd98-b68fdd7b04c2", "e8c2c618-d8c5-4c4f-8273-381f7a4d3ad4", "d38e7475-f5ca-4ea0-ba0d-0ff6ad678f80"]}, "a0be7d8a-e6a5-4621-bd98-b68fdd7b04c2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "57", "name": "type", "value": "${'detail'}"}, {"_uid": "58", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1200"}}, "next": "551bdbd9-e40b-4ae7-b8ae-cca062953c8f"}, "e8c2c618-d8c5-4c4f-8273-381f7a4d3ad4": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_org_delete_check", "requestParams": [{"_uid": "69", "name": "id", "value": "${tableCell.row.id}"}, {"_uid": "71", "name": "type", "value": "${'detail'}"}], "dialogSettings": {"width": "1200"}}, "next": "551bdbd9-e40b-4ae7-b8ae-cca062953c8f"}, "d38e7475-f5ca-4ea0-ba0d-0ff6ad678f80": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_check_org", "requestParams": [{"_uid": "78", "name": "id", "value": "${tableCell.row.id}"}, {"_uid": "79", "name": "type", "value": "${'detail'}"}], "dialogSettings": {"width": "1200"}}, "next": "551bdbd9-e40b-4ae7-b8ae-cca062953c8f"}, "551bdbd9-e40b-4ae7-b8ae-cca062953c8f": {"type": "Action.End", "inputs": {}, "next": null}}}, "P10v9bCdu30": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "84a56cd3-314a-4ad7-a0da-c94695cee52d"}, "action-54": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'请确认是否提交？'}", "okText": "确定", "icon": "", "description": null}, "next": "action-56"}, "action-56": {"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/updateOperation", "method": "POST", "params": {"id": "${tableCell.row.id}", "status": "${1}"}, "description": null}, "next": "action-61"}, "action-61": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "提交成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "next": "action-222"}, "action-222": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "84a56cd3-314a-4ad7-a0da-c94695cee52d": {"type": "Action.Http", "inputs": {"resultName": "names", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/requestExists", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${tableCell.row.id}", "type": "${'org'}"}}, "next": "15511f89-8cc0-4857-a659-6148c1ac27ae"}, "15511f89-8cc0-4857-a659-6148c1ac27ae": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${names==null || names.length == 0}", "description": "${names==null || names.length == 0}"}, {"condition": "${names && names.length > 0}", "description": "${names && names.length > 0}"}]}, "next": ["action-54", "9562f48f-bf6b-4932-9e05-548b5a77d15a"]}, "9562f48f-bf6b-4932-9e05-548b5a77d15a": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${'该组织存在未提交的复核'}", "duration": 1.5, "closable": false, "background": false}, "next": null}}}}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "", "props": {"icon": "md-card", "padding": 0, "bordered": true, "disHover": false, "shadow": false, "replace": false, "append": false}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false, "demo": {"props.data": "[{\"col1\":\"部门管理员\",\"col2\":\"组织管理者\",\"col3\":\"2022-06-17 10:42:00\"},{\"col1\":\"采购负责人\",\"col2\":\"采购经理\",\"col3\":\"2022-06-17 10:42:00\"}]"}, "lock": false}, "props": {"data": [], "tableCols": [], "enableSelectionCol": true, "enableIndexCol": true, "stripe": false, "border": false, "show-header": true, "loading": "${checkData.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "visibleHeaderOp": true, "headerSearch": {"enable": false, "filters": [{"key": "keyword", "condition": "cn"}]}, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "ds": "${checkData}", "loadOnMounted": true}, "events": {"on-current-change": {"actions": []}, "on-select": {"actions": []}, "on-select-cancel": {"actions": []}, "on-select-all": {"actions": []}, "on-select-all-cancel": {"actions": []}, "on-selection-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "252", "name": "selectRequest", "value": "${$event}"}], "description": null}, "description": null, "id": "action-251", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "340", "name": "selectRequestId", "value": "${selectRequest.map(v => v.id)}"}]}, "description": null, "id": "action-339"}]}, "on-sort-change": {"actions": []}, "on-filter-change": {"actions": []}, "on-row-click": {"actions": []}, "on-row-dblclick": {"actions": []}, "on-expand": {"actions": []}, "on-cell-click": {"actions": []}, "on-drag-drop": {"actions": []}, "on-change": {"actions": []}, "on-page-size-change": {"actions": []}, "on-prev": {"actions": []}, "on-next": {"actions": []}, "on-data-change": {"actions": []}}, "slots": {"headerExtra": {"children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "style": "margin-bottom:16px", "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "输入用户名查询", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "value": "${name}"}, "style": "width:150px;", "events": {"on-enter": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "description": null, "id": "action-67"}]}, "on-click": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}]}], "props": {"customCol": "12:12", "gutter": 16, "wrap": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "header": {"children": [{"type": "IvButton", "props": {"type": "default", "size": "default", "shape": "default", "target": "_self", "icon": "ios-arrow-back"}, "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "./tenant.gf_org.gf_org_info", "variables": [], "cacheOpener": false}, "description": null, "id": "action-46"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"type": "default", "size": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "id": "action-393", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "a30t54l3b00", "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "删除", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "wx-cx4lxb0n", "click.stop": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"designer": {"movein": false, "combo": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "需求来源", "keyName": "itType", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "IT_TYPE"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "IT需求"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "需求url", "keyName": "itUrl"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.itUrl}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作对象", "keyName": "requestObjectStr", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.requestObjectStr}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作类型", "keyName": "operationType", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "operationType"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "新增"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "保存时间", "keyName": "submitAtStr", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.submitAtStr}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作描述", "keyName": "description", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.description}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "状态", "keyName": "status", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "request_status"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "草稿"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作", "keyName": "operations", "dataTimeType": "YYYY-MM-DD", "align": "left"}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "详情"}, "events": {"click": "HmbJ9CCBGqB", "click.stop": {"actions": []}}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "编辑"}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.operationType == 'add' || tableCell.row.operationType == 'update'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"click": "VG7RFZr2vI-", "click.stop": {"actions": []}}}, {"type": "IvLink", "props": {"tag": "a", "text": "提交", "type": "page", "linkColor": true, "replace": false}, "events": {"click": "P10v9bCdu30", "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "text": "删除", "type": "page", "linkColor": true, "replace": false}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'请确认是否删除？'}", "okText": "确定", "icon": "", "description": null}, "id": "action-54", "activeOn": null}, {"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/delRecord", "method": "POST", "params": {"id": "${tableCell.row.id}"}, "description": null}, "id": "action-56", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "删除成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-61", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "id": "action-222", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "visible": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "提交列表", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}, "style": "margin-bottom:16px;font-size:16px;padding-top:12px;"}], "auth": {"turnOn": true, "requiredPermission": [], "license": null}, "meta": {"title": "组织提交列表", "name": "role_list", "packageName": "tenant.public_role"}}