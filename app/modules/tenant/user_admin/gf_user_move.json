{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "userData", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "orgId": {"type": "string", "title": "关联组织ID"}, "system": {"type": "boolean", "title": "是否内置"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "org": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}, "fetchWhen": "${form.orgId}", "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user/extend/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": null, "expand": null, "joins": null, "total": "${true}", "inOrgIds": null, "roleFilters": null, "expandPosition": null, "kindId": "${'P'}", "orgId": "${form.orgId}", "onlySelf": "${true}", "order": "${true}"}}, {"id": "userInfo", "title": "获取记录", "multiple": false, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "orgId": {"type": "string", "title": "关联组织ID"}, "system": {"type": "boolean", "title": "是否内置"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "org": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}, "lazy": true, "autoReload": false, "url": "/api/tenant/user/extend/{id}", "method": "GET", "params": {"id": "${id}", "select": null}}], "variables": {"sortOrder": {"type": "string", "default": "", "title": "sortOrder", "orderNo": 0}, "orgId": {"type": "string", "default": "", "title": "orgId", "orderNo": 1}, "sortOptions": {"type": "number", "default": 0, "title": "sortOptions", "orderNo": 2}, "itType": {"type": "string", "default": "0", "title": "itType", "orderNo": 3}, "itUrl": {"type": "string", "default": "", "title": "itType", "orderNo": 4}, "selectUsers": {"type": "array", "title": "所选择的用户", "orderNo": 5}, "sortUserId": {"type": "string", "default": "", "title": "所选择的用户Id", "orderNo": 6}, "id": {"type": "string", "default": "${request.params.id}", "source": "request", "optionSet": null, "entity": null, "title": "用户id", "orderNo": 7}, "form": {"type": "object", "title": "表单", "orderNo": 8}}, "orchestrations": {"Qx6ZUCsoLBq": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-48-condition"}, "action-48-condition": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.operationType == 'update'}", "description": "${tableCell.row.operationType == 'update'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}"}]}, "next": ["139e28a8-d4ed-4631-81b8-388d147189c2", "de17e820-d419-4300-b4d1-933a6d6ebea1", "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9", "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a", "17d85de0-0b53-4dc8-a5e5-7d82d56115be"]}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "139e28a8-d4ed-4631-81b8-388d147189c2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_user", "requestParams": [{"_uid": "258", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "de17e820-d419-4300-b4d1-933a6d6ebea1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.check_org", "requestParams": [{"_uid": "266", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}}, "next": "end"}, "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "77", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.zuzhishanchufuhe", "requestParams": [{"_uid": "199", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}, "17d85de0-0b53-4dc8-a5e5-7d82d56115be": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.yo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestParams": [{"_uid": "200", "name": "id", "value": "${tableCell.row.id}"}]}, "next": "end"}}}, "DzebV5tstA8": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-60"}, "action-60": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "只能选择一个用户", "duration": 1.5, "closable": false, "background": false}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "szDcj5tDRAi": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-60"}, "action-60": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "只能选择一个用户", "duration": 1.5, "closable": false, "background": false}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "XJ6cnyRsRiA": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "7b5bde77-a68b-4b89-9191-2baa0a5b5409"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "7b5bde77-a68b-4b89-9191-2baa0a5b5409": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "74", "name": "selectUsers", "value": "${$event}"}]}, "next": "01ee862c-fb18-4f19-bd7e-5317dfb1316b"}, "01ee862c-fb18-4f19-bd7e-5317dfb1316b": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectUsers!=null&&selectUsers.length>1}", "description": "${selectUsers!=null&&selectUsers.length>1}"}, {"condition": "${selectUsers!=null&&selectUsers.length == 1}", "description": "${selectUsers!=null&&selectUsers.length == 1}"}]}, "next": ["60d5a94c-dc0f-4728-a8a4-921859c184a7", "1d0c9413-16bf-4fe9-8992-18c0a172d3a2"]}, "1d0c9413-16bf-4fe9-8992-18c0a172d3a2": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "81", "name": "form.sortUserId", "value": "${selectUsers[0].id}"}, {"_uid": "105", "name": "form.sortOptions", "value": "2"}]}, "next": "end"}, "60d5a94c-dc0f-4728-a8a4-921859c184a7": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "只能选择一个用户", "okText": "确定", "icon": ""}, "next": "end"}}}, "DziAThXD2ii": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "fc47c5bc-29bf-4991-b9c1-d9c566040210"}, "action-53": {"type": "Action.Http", "inputs": {"url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/operationUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'move'}", "status": "1", "objectId": "${id}"}, "data": "${form}", "description": null, "schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}}, "next": "action-65"}, "action-65": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "已将移动请求提交到复核列表", "duration": 1.5, "closable": false, "background": false, "description": null}, "next": "action-67"}, "action-67": {"type": "Action.CloseDialog", "inputs": {"description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "112fc8aa-866b-4bdc-87c9-bd2a433b0dd4": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${sortOptions!=2||(selectUsers!=null&&selectUsers.length==1)}", "description": "${sortOptions!=2||(selectUsers!=null&&selectUsers.length==1)}"}, {"condition": "${sortOptions==2&&(selectUsers==null||selectUsers.length==0||selectUsers.length>1)}", "description": "${sortOptions==2&&(selectUsers==null||selectUsers.length==0||selectUsers.length>1)}"}]}, "next": ["action-53", "3bb44e7d-b9ac-4b16-a5c7-72327e3149a5"]}, "3bb44e7d-b9ac-4b16-a5c7-72327e3149a5": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请选择一个用户", "okText": "确定", "icon": ""}, "next": null}, "fc47c5bc-29bf-4991-b9c1-d9c566040210": {"type": "Action.Http", "inputs": {"resultName": "num", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/exist", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${userInfo.data.id}", "type": "${'user'}"}}, "next": "9cd14e7e-bdaa-4b9b-9889-6ea81119d902"}, "9cd14e7e-bdaa-4b9b-9889-6ea81119d902": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${num == 0}", "description": "${num == 0}"}, {"condition": "${num > 0}", "description": "${num > 0}"}]}, "next": ["112fc8aa-866b-4bdc-87c9-bd2a433b0dd4", "057ea272-ba54-4514-ab2c-4a5eec043d5a"]}, "057ea272-ba54-4514-ab2c-4a5eec043d5a": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "该用户存在未复核的请求！", "okText": "确定", "icon": ""}, "next": null}}}, "kj6IuM6fn66": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-89"}, "action-89": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userData}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}}, "style": "padding:12px;", "events": {"on-rendered": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userInfo}"}, "description": null, "id": "action-89"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setAcmFlag", "params": []}, "description": null, "id": "action-72"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "103", "name": "form.orgId", "value": "${userInfo.data.orgId}"}]}, "description": null, "id": "action-102"}]}, "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "functions": {"transformed": false, "script": "export function setAcmFlag() {\r\n\r\n  console.log(renderContext.dataSources.userInfo.data, 'userInfo.data')\r\n  let acmId = renderContext.dataSources.userInfo.data.acmUserId;\r\n  console.log(acmId, 'acmId')\r\n  if (acmId == null || acmId == '')\r\n    renderContext.variables.form.acmFlag = '0';\r\n  else renderContext.variables.form.acmFlag = '1';\r\n}\r\nexport function show() {\r\n  console.log(renderContext.variables.id, 'id')\r\n}"}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "padding:12px;", "props": {"icon": "md-card", "padding": 0, "bordered": true, "disHover": false, "shadow": false, "replace": false, "append": false}, "slots": {"default": {"children": [{"type": "IvForm", "props": {"model": "${form}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "IvFormRadio", "designer": {"movein": false}, "props": {"labelName": "是否发送ACM", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "data": [{"label": "是", "value": "${\"1\"}"}, {"label": "否", "value": "${\"0\"}"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "labelWidth": 140, "value": "${form.acmFlag}", "firstValue": false, "requiredName": "ivu-form-item-required", "prop": "acmFlag", "defaultValue": "${\"1\"}"}, "style": ""}]}], "props": {"customCol": "24", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "IamFormOrgSelect", "designer": {"movein": false}, "props": {"labelName": "移动部门", "span": "24", "type": "input", "multiple": false, "disabledChild": false, "modalTitle": "选择组织", "showConfig": false, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "请选择组织"}]}, "value": "${form.orgId}", "requiredName": "ivu-form-item-required", "labelWidth": 140, "orgUrl": "/api/tenant/org/extend/tree/orgs/extend", "rootOrgUrl": "/api/tenant/org/extend/tree/roots/extend"}, "events": {"on-change": "kj6IuM6fn66"}, "style": "width:100%;", "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}], "props": {"customCol": "24", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 8}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${form.itType}", "defaultValue": "${'0'}", "labelWidth": 140, "dataDictionary": "IT_TYPE"}, "class": "", "style": "margin:0px;padding:0px;"}]}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 16}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.itUrl}", "labelWidth": 0}, "style": "", "class": ""}]}], "props": {"customCol": "8:16", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 8}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.name}", "disabled": true, "labelWidth": 140}}]}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 16}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.adOrder}", "labelWidth": 0, "disabled": true}, "style": "text-align:left;margin:0px;"}]}], "props": {"customCol": "8:16", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 8}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择排序类型", "notFoundText": "无匹配数据", "data": [{"label": "排在最上", "value": "0"}, {"value": "1", "label": "排在最下"}, {"value": "2", "label": "排在谁之后"}], "keyNames": {}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${form.sortOptions}", "labelWidth": 140}}]}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 16}}], "props": {"customCol": "8:16", "gutter": 16, "wrap": true}, "style": "width:100%;"}], "style": "margin:16px;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": true, "data": [], "tableCols": [], "enableSelectionCol": true, "enableIndexCol": false, "visibleHeaderOp": false, "visibleMore": false, "padding": 16, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": false, "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${userData}"}, "events": {"on-current-change": {"actions": []}, "on-select": {"actions": []}, "on-select-cancel": {"actions": []}, "on-select-all": {"actions": []}, "on-select-all-cancel": {"actions": []}, "on-selection-change": "XJ6cnyRsRiA", "on-sort-change": {"actions": []}, "on-filter-change": {"actions": []}, "on-row-click": {"actions": []}, "on-row-dblclick": {"actions": []}, "on-expand": {"actions": []}, "on-cell-click": {"actions": []}, "on-drag-drop": {"actions": []}, "on-change": {"actions": []}, "on-page-size-change": {"actions": []}, "on-prev": {"actions": []}, "on-next": {"actions": []}}, "slots": {"header": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "显示名", "keyName": "name", "show": true, "children": []}, "designer": {"movein": false}, "style": "width:50%;"}, {"type": "IvTableGridColumn", "props": {"title": "排序号", "keyName": "adOrder", "show": true, "width": 100, "children": []}, "designer": {"movein": false}, "style": ""}]}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "text-align:right;padding-top:12px;padding-bottom:12px;", "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "long": false}, "events": {"click": "DziAThXD2ii"}}]}], "header": [{"type": "IvText", "props": {"maxLine": 0, "text": "用户移动"}, "style": "font-size:16px;"}], "auth": {"turnOn": true, "requiredPermission": [], "license": null}, "meta": {"title": "用户移动列表", "name": "role_list", "packageName": "tenant.public_role"}}