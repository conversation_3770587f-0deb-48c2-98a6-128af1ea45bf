{"type": "AdaptivePage", "version": "2.0", "variables": {"orgId": {"type": "string", "default": "null", "optionSet": null, "entity": null, "title": "组织id", "orderNo": 0}, "batchMoveForm": {"type": "object", "default": {"userOldOANames": null, "userNewOANames": null, "orgId": [], "addUserIds": [], "itType": "0", "itUrl": null, "moveMainDepartment": false}, "title": "批量移动表", "orderNo": 1}, "userNames": {"type": "array", "default": "${request.params.userNames}", "source": "request", "optionSet": null, "entity": null, "title": "选择的用户UserName或OAName", "orderNo": 2}, "userIds": {"type": "array", "default": "${request.params.userIds}", "source": "request", "optionSet": null, "entity": null, "title": "选择移动的用户Id", "orderNo": 3}, "type": {"type": "string", "default": "${request.params.type}", "source": "request", "optionSet": null, "entity": null, "title": "移动草稿编辑 | 移动", "orderNo": 4}, "requestId": {"type": "string", "default": "${request.params.requestId}", "source": "request", "optionSet": null, "entity": null, "title": "草稿的id", "orderNo": 5}, "copycontent": {"type": "string", "default": "", "title": "复制的内容", "orderNo": 6}, "mainDepartment": {"type": "boolean", "default": "${request.params.mainDepartment}", "source": "request", "optionSet": null, "entity": null, "title": "是否批量移动主部门", "orderNo": 7}}, "dataSources": [{"id": "userList", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "orgId": {"type": "string", "title": "关联组织ID"}, "system": {"type": "boolean", "title": "是否内置"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "org": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}, "fetchWhen": "${batchMoveForm.orgId != null && batchMoveForm.orgId != ''}", "lazy": true, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user/extend/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"sizeNum": "", "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": null, "expand": null, "joins": null, "all": "${true}", "inOrgIds": null, "roleFilters": null, "expandPosition": null, "kindId": "${'P'}", "orgId": "${batchMoveForm.orgId}", "onlySelf": "${true}", "order": "${true}"}}], "functions": {"transformed": false, "script": "export function concatOAName() {\r\n  if (renderContext.variables.batchMoveForm.orgId != null && renderContext.variables.batchMoveForm.orgId != '') {\r\n    console.log(\"orgId\", renderContext.variables.batchMoveForm.orgId)\r\n    // 将userList数据源的oaName都用换行符连接，并且赋值给左边框\r\n    let oaNames = renderContext.dataSources.userList.data.map(v => {\r\n      if (v.oaName != null && v.oaName != '')\r\n        return v.oaName;\r\n      else if (v.username != null && v.username != '')\r\n        return v.username;\r\n      else return v.name;\r\n    }).filter(v => renderContext.variables.userNames.value.indexOf(v) == -1).join(\"\\n\");\r\n    let addOaNames = renderContext.variables.userNames.value.join('\\n');\r\n    if (oaNames != null && oaNames != \"\")\r\n      oaNames = oaNames + \"\\n\" + addOaNames;\r\n    else oaNames = addOaNames;\r\n    renderContext.variables.batchMoveForm.userOldOANames = oaNames;\r\n    console.log(\"更新old\", renderContext.variables.batchMoveForm.userOldOANames)\r\n  }\r\n}\r\nexport function copy() {\r\n  renderContext.variables.copyContent = renderContext.variables.batchMoveForm.userOldOANames\r\n  return utils.copy(renderContext.variables.batchMoveForm.userOldOANames)\r\n}\r\nexport function prase() {\r\n  console.log(\"prase\", renderContext.variables.copyContent)\r\n  renderContext.variables.batchMoveForm.userNewOANames = renderContext.variables.copyContent\r\n}"}, "events": {"on-rendered": "qBTeoBD9lWT", "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "orchestrations": {"qBTeoBD9lWT": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "179ff51a-f9b0-4849-aeb8-f94086e2314d"}, "action-94": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "52", "name": "batchMoveForm.addUserIds", "value": "${userIds}"}, {"_uid": "255", "name": "batchMoveForm.addUserIds", "value": "${userIds}"}, {"_uid": "305", "name": "batchMoveForm.userNames", "value": "${userNames}"}], "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "179ff51a-f9b0-4849-aeb8-f94086e2314d": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${type == 'move'}", "description": "${type == 'move'}"}, {"condition": "${type == 'edit'}", "description": "${type == 'edit'}"}]}, "next": ["1f2ddb51-6f04-4f9b-bb97-996a7a977bcf", "6b9e7b86-0efb-49ed-9d4d-52f5940ea460"]}, "15ef110a-316b-4a3a-af24-84848d7804bb": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "113", "name": "batchMoveForm", "value": "${response.iamUsersMoveVo}"}, {"_uid": "311", "name": "userNames", "value": "${batchMoveForm.userNames}"}]}, "next": "e0afea52-7af2-4204-b91c-ca09b368ab34"}, "6b9e7b86-0efb-49ed-9d4d-52f5940ea460": {"type": "Action.Http", "inputs": {"resultName": "response", "schema": {"properties": {"type": "object"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectStr": {"type": "string", "title": "操作对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "newIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "oldIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "iamUsersMoveVo": {"type": "object", "properties": {"userOldOANames": {"type": "string"}, "userNewOANames": {"type": "string"}, "orgId": {"type": "string"}, "addUserIds": {"type": "array"}, "itType": {"type": "string"}, "itUrl": {"type": "string"}, "acmFlag": {"type": "integer"}}}, "newIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "oldIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "map": {"type": "object"}, "newStr": {"type": "array"}, "oldStr": {"type": "array"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getDetail", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${requestId}"}}, "next": "15ef110a-316b-4a3a-af24-84848d7804bb"}, "e0afea52-7af2-4204-b91c-ca09b368ab34": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "end"}, "1f2ddb51-6f04-4f9b-bb97-996a7a977bcf": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "252", "name": "batchMoveForm.userOldOANames", "value": "${null}"}]}, "next": "action-94"}}}, "eVTdJqtnloK": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-86"}, "action-86": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "action-113"}, "action-113": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "concatOAName", "params": [], "resultName": ""}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}}, "body": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {"text": "文本", "type": "vertical", "orientation": "center", "size": "default"}}, {"type": "IvForm", "props": {"model": "${batchMoveForm}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormRadio", "designer": {"movein": false, "combo": false}, "props": {"labelName": "是否发送ACM", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "data": [{"label": "是", "value": "${1}"}, {"label": "否", "value": "${0}"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "value": "${batchMoveForm.acmFlag}", "defaultValue": "${1}", "requiredName": "ivu-form-item-required", "prop": "acmFlag", "labelWidth": 140}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "IT需求", "placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${batchMoveForm.itType}", "dataDictionary": "IT_TYPE", "dataDictionaryDisabled": [], "requiredName": "ivu-form-item-required", "prop": "itType", "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "labelWidth": 0, "value": "${batchMoveForm.itUrl}"}, "style": ""}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "style": "margin-bottom:16px", "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "IamFormOrgSelect", "designer": {"movein": false}, "props": {"labelName": "选择组织", "span": "24", "type": "input", "multiple": false, "disabled": false, "canSelectDisabledIdChild": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "placeholder": "请选择组织", "showConfig": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "value": "${batchMoveForm.orgId}", "requiredName": "ivu-form-item-required", "prop": "orgId", "labelWidth": 140, "orgUrl": "/api/tenant/org/extend/tree/orgs/extend", "rootOrgUrl": "/api/tenant/org/extend/tree/roots/extend"}, "style": "margin:0px;", "events": {"on-change": "eVTdJqtnloK"}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "width:100%;"}], "props": {"customCol": "24", "gutter": 16, "wrap": true}}], "style": "", "class": "", "id": "form", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "style": "", "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "原排序："}, "style": "width:200px;text-align:right;padding-top:5px;"}, {"type": "Input", "designer": {"movein": false}, "props": {"type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 10, "wrap": "soft", "autocomplete": "off", "autosize": false, "value": "${batchMoveForm.userOldOANames}", "readonly": true}, "style": "", "class": "", "id": "old<PERSON>ser", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "style": "display:flex;"}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "目标排序："}, "style": "width:140px;padding-top:5px;text-align:right;"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 10, "wrap": "soft", "autocomplete": "off", "autosize": false, "value": "${batchMoveForm.userNewOANames}"}, "style": ""}], "style": "margin:0px;display:flex;"}], "props": {"customCol": "12:12", "gutter": 16, "wrap": true}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "复制", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "copy", "params": []}, "description": null, "id": "action-116"}]}, "click.stop": {"actions": []}}, "style": "margin-right:8px;margin-left:120px;"}, {"type": "IvButton", "props": {"text": "粘贴", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "prase", "params": []}, "description": null, "id": "action-46"}]}, "click.stop": {"actions": []}}, "style": "margin-left:8px;"}], "style": "display:flex;justify-content: center;"}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-117"}]}}}, {"type": "IvButton", "props": {"text": "保存", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form"}, "description": null, "id": "action-103"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "245", "name": "batchMoveForm.moveMainDepartment", "value": "${mainDepartment}"}]}, "description": null, "id": "action-244"}, {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchMoveUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'move'}", "status": "0"}, "data": "${batchMoveForm}", "description": null}, "description": null, "id": "action-106", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已保存到提交列表", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-121"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-133"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form"}, "description": null, "id": "action-84"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "234", "name": "batchMoveForm.moveMainDepartment", "value": "${mainDepartment}"}], "description": null}, "description": null, "id": "action-233", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchMoveUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'move'}", "status": "1"}, "data": "${batchMoveForm}", "description": null}, "description": null, "id": "action-87", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已将该移动请求提交到复核列表", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-126"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-129"}]}, "click.stop": {"actions": []}}}], "style": "text-align:right;padding-top:12px;padding-bottom:12px;"}], "meta": {"title": "批量移动用户"}}