{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "userList", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "orgId": {"type": "string", "title": "关联组织ID"}, "system": {"type": "boolean", "title": "是否内置"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "org": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}, "lazy": true, "autoReload": false, "url": "/api/tenant/user/extend/queryList", "method": "POST", "params": {"page": null, "size": null, "limit": null, "offset": null, "total": "${true}"}, "data": "${userQueryDto}"}, {"id": "outputlist", "title": "获取用户列表数据 包括其他所属组织 用于导出", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "string", "title": "自定义扩展字段"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": true, "autoReload": false, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user/extend/output", "processor": "ServicePathProcessor"}, "method": "POST", "data": "${selectedUsers.map(v=>v.id)}"}], "variables": {"selectOrgId": {"type": "string", "default": "", "title": "", "orderNo": 0}, "modal1": {"type": "boolean", "default": false, "title": "对话框1", "orderNo": 1}, "externalKeyword": {"type": "string", "default": "", "title": "", "orderNo": 2}, "cellValue": {"type": "string", "default": "0", "title": "单元格高亮值", "orderNo": 3}, "userFilters": {"type": "string", "default": "", "title": "用户过滤条件", "orderNo": 4}, "orgSearch": {"type": "string", "default": "", "title": "组织搜索关键字", "orderNo": 5}, "currentSelectOrgId": {"type": "string", "default": "", "title": "currentSelectOrgId", "orderNo": 6}, "selectedUsers": {"type": "array", "default": [], "title": "selectedUsers", "orderNo": 7}, "userListQueryOptions": {"type": "object", "default": {"orderby": "updatedAt desc,sortOrder2 asc"}, "title": "userListQueryOptions", "orderNo": 8}, "outputData": {"type": "object", "default": {"list": [], "total": 0}, "title": "选择的数据", "orderNo": 9}, "selectIds": {"type": "array", "title": "选择的用户id", "orderNo": 10}, "selectUserNames": {"type": "array", "title": "所选择的用户OA名称或名称", "orderNo": 11}, "syncAble": {"type": "boolean", "default": true, "title": "是否可以同步", "orderNo": 12}, "enabledFilters": {"type": "string", "default": "and enabled eq true", "title": "是否启动状态条件筛选", "orderNo": 13}, "status": {"type": "number", "default": 3, "title": "筛选用户状态", "orderNo": 14}, "oaName": {"type": "string", "default": "", "title": "oa用户名", "orderNo": 15}, "manager": {"type": "string", "default": "", "title": "管理者账户或名称", "orderNo": 16}, "userFilter2": {"type": "string", "default": "", "title": "用户筛选条件过滤器2", "orderNo": 17}, "erpNum": {"type": "string", "default": "", "title": "erp号", "orderNo": 18}, "keyword": {"type": "string", "default": "", "title": "用户名称或者账号", "orderNo": 19}, "existNames": {"type": "array", "title": "存在复核的用户名", "orderNo": 20}, "msg": {"type": "string", "default": "", "title": "提示消息", "orderNo": 21}, "type": {"type": "string", "default": "", "title": "用户类型", "orderNo": 22}, "userFilters3": {"type": "string", "default": "", "title": "用户类型过滤器", "orderNo": 23}, "userQueryDto": {"type": "object", "default": {"status": "all", "keyword": null, "type": "all", "orgId": null, "adminKeyword": null, "orgKeyword": null, "roleKeyword": null}, "title": "用户请求条件", "orderNo": 24}, "drawer1": {"type": "boolean", "default": false, "title": "抽屉1", "orderNo": 1}}, "functions": {"transformed": false, "script": "export function setSelectUserNames() {\r\n  renderContext.variables.selectUserNames = renderContext.variables.selectedUsers.value.filter(v => v.id !== utils.user.userId)\r\n    .map(v => {\r\n      if (v.oaName != null && v.oaName != '')\r\n        return v.oaName;\r\n      else if (v.username != null && v.username != '')\r\n        return v.username;\r\n      else return v.name;\r\n    })\r\n}\r\nexport function setExpireTimeFilter() {\r\n  let date = new Date();\r\n  let year = date.getFullYear();\r\n  let month = date.getMonth() + 1;\r\n  let day = date.getDate();\r\n  let hour = date.getHours();\r\n  let minute = date.getMinutes();\r\n  let second = date.getSeconds();\r\n  let currentDateTime = pad(year, 4) + \"-\" + pad(month, 2) + \"-\" + pad(day, 2) + \" \" + pad(hour, 2) + \":\" + pad(minute, 2) + \":\" + pad(second, 2) + \".000+0800\";\r\n  console.log(\"date\", date);\r\n  renderContext.variables.userFilters.value = ` expiredAt < '${currentDateTime}'`\r\n  console.log(\"date\", renderContext.variables.userFilters);\r\n}\r\nexport function clearExpireTimeFilter() {\r\n  renderContext.variables.userFilters.value = '';\r\n  console.log(renderContext.variables.userFilters, \"s\");\r\n  console.log(renderContext.variables.userFilter2, \"2\")\r\n}\r\n\r\n\r\nfunction formaData(timer) {\r\n  const year = timer.getFullYear()\r\n  const month = timer.getMonth() + 1 // 由于月份从0开始，因此需加1\r\n  const day = timer.getDate()\r\n  return `${pad(year, 4)}-${pad(month)}-${pad(day)}`\r\n}\r\nfunction pad(timeEl, total = 2, str = '0') {\r\n  return timeEl.toString().padStart(total, str)\r\n}\r\n\r\n\r\n\r\n/**\r\n * 状态文本方法\r\n * 根据 row 对象的属性返回对应的中文状态\r\n */\r\nexport function statusText(row) {\r\n  // 获取当前时间\r\n  const now = new Date();\r\n\r\n  // 注销：deleted 为 true\r\n  if (row.deleted === true) {\r\n    return '注销';\r\n  }\r\n\r\n  // 禁用：deleted 为 false 且 enabled 为 false\r\n  if (row.deleted === false && row.enabled === false) {\r\n    return '禁用';\r\n  }\r\n\r\n  // 已过期：deleted 为 false，enabled 为 true，且 expiredAt 不为空且不晚于当前时间\r\n  if (\r\n    row.deleted === false &&\r\n    row.enabled === true &&\r\n    row.expiredAt &&\r\n    new Date(row.expiredAt) <= now\r\n  ) {\r\n    return '已过期';\r\n  }\r\n\r\n  // 启用：deleted 为 false，enabled 为 true，且 expiredAt 为空或晚于当前时间\r\n  if (\r\n    row.deleted === false &&\r\n    row.enabled === true &&\r\n    (!row.expiredAt || new Date(row.expiredAt) > now)\r\n  ) {\r\n    return '启用';\r\n  }\r\n\r\n  // 默认返回空字符串\r\n  return '';\r\n}\r\n"}, "orchestrations": {"N9CTkaWbo": {"actions": {"d792fda0-f48d-4af7-814f-79dda1ffb48e": {"type": "Action.Start", "inputs": {}, "next": "1193433f-90c1-4b69-b309-150a6aa0e8d4"}, "1193433f-90c1-4b69-b309-150a6aa0e8d4": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${!tableCell.row.deleted && tableCell.row.enabled}", "description": "${!tableCell.row.deleted && tableCell.row.enabled}"}, {"condition": "${!tableCell.row.deleted&&!tableCell.row.enabled}", "description": "${!tableCell.row.deleted&&!tableCell.row.enabled}"}, {"condition": "${tableCell.row.deleted&&!tableCell.row.enabled}", "description": "${tableCell.row.deleted&&!tableCell.row.enabled}"}]}, "next": ["8ec54fbe-b886-4686-9a09-106464e0e397", "0c53d0f8-f063-4d7c-adf2-b78e65b4ae3b", "7528891c-30c9-4764-8a62-9ab0dac258b4"]}, "8ec54fbe-b886-4686-9a09-106464e0e397": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'是否确定禁用用户：'+tableCell.row.name+'？禁用后，该账号将无法登录，请谨慎操作！'}", "okText": "确定", "icon": ""}, "next": "40618eed-a425-4eaa-9c07-9c9155c0719e"}, "3ec9648b-20c0-43c2-8313-a51aa9b1174e": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "5eb20789-81ae-406e-92dc-0b5c02e1cb74"}, "5eb20789-81ae-406e-92dc-0b5c02e1cb74": {"type": "Action.End", "inputs": {}, "next": null}, "0c53d0f8-f063-4d7c-adf2-b78e65b4ae3b": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${`是否确定启用用户 ${tableCell.row.name}？`}", "okText": "确定", "icon": ""}, "next": "bd0d00f5-70bf-49b3-bf32-868159374f54"}, "40618eed-a425-4eaa-9c07-9c9155c0719e": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_enable", "requestParams": [{"_uid": "158", "name": "id", "value": "${tableCell.row.id}"}, {"_uid": "163", "name": "opearType", "value": "${'disable'}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}"}}, "next": "3ec9648b-20c0-43c2-8313-a51aa9b1174e"}, "bd0d00f5-70bf-49b3-bf32-868159374f54": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_enable", "requestParams": [{"_uid": "171", "name": "id", "value": "${tableCell.row.id}"}, {"_uid": "172", "name": "opearType", "value": "${'enable'}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}"}}, "next": "3ec9648b-20c0-43c2-8313-a51aa9b1174e"}, "7528891c-30c9-4764-8a62-9ab0dac258b4": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'是否确定启用注销用户：'+tableCell.row.name}", "okText": "确定", "icon": ""}, "next": "1ca107fa-d0b4-4fe1-8a91-1c4fa30f2326"}, "1ca107fa-d0b4-4fe1-8a91-1c4fa30f2326": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_edit_user", "dialogSettings": {"width": "1200", "continueOnClose": "${true}"}, "requestParams": [{"_uid": "163", "name": "userId", "value": "${tableCell.row.id}"}, {"_uid": "189", "name": "type", "value": "${'enaDel'}"}]}, "next": "3ec9648b-20c0-43c2-8313-a51aa9b1174e"}}}, "i_751vDdm": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "a102b029-62cd-445a-b5ca-c203225da8c0"}, "action-47": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user", "requestParams": [{"_uid": "48", "name": "userId", "value": "${tableCell.row.id}"}], "description": null}, "next": "action-96"}, "action-96": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "next": "action-100"}, "action-100": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activeUserTotal}", "description": null}, "next": "action-104"}, "action-104": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${delUserCount}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "a102b029-62cd-445a-b5ca-c203225da8c0": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.type === 'N'}", "description": "${tableCell.row.type === 'N'}"}, {"condition": "${tableCell.row.type === 'E'}", "description": "${tableCell.row.type === 'E'}"}]}, "next": ["action-47", "a979a22e-b44c-4cde-ab77-6f0f4d52278f"]}, "a979a22e-b44c-4cde-ab77-6f0f4d52278f": {"type": "Action.OpenInDialog", "inputs": {"requestParams": [{"_uid": "73", "name": "userId", "value": "${tableCell.row.id}"}], "uri": "tenant.user_admin.enabled_user_e"}, "next": "action-96"}}}, "YiBUnM8cjiR": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "e6775e10-004a-42f8-8aa5-c1f801ef88c1"}, "e6775e10-004a-42f8-8aa5-c1f801ef88c1": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${$event == '0'}", "description": "${$event == '0'}"}, {"condition": "${$event == '1'}", "description": "${$event == '1'}"}, {"condition": "${$event == '3'}", "description": "${$event == '3'}"}, {"condition": "${$event == '2'}", "description": "${$event == '2'}"}, {"condition": "${$event=='4'}", "description": "${$event=='4'}"}]}, "next": ["f8fdc318-7fda-4fef-a381-671dbfc8118f", "b52ec4cb-4b3d-4e55-9d8c-1740e103cdfe", "b3bd1acb-225c-4e54-aad2-bb379e46b3e7", "c33d2ab6-b3f1-4336-ada7-2a3528dd76f1", "2f63e342-98f6-426c-872e-633b99a0491b"]}, "f8fdc318-7fda-4fef-a381-671dbfc8118f": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "314", "name": "modal1", "value": "${true}"}]}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "e5101446-5959-4385-a879-a196ba87ef6b": {"type": "Action.End", "inputs": {}, "next": null}, "b52ec4cb-4b3d-4e55-9d8c-1740e103cdfe": {"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${`是否确定禁用用户：${selectedUsers.filter(v=> v.id !== utils.user.userId && v.enabled).map(v=> v.name).join(',')}？禁用后，该账号将无法登录，请谨慎操作！`}", "okText": "确定", "icon": ""}, "next": "5f565fde-ad6f-4bcc-bafc-c0c7eb1b1cb3"}, "2f63e342-98f6-426c-872e-633b99a0491b": {"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确认将删除用户：[${selectedUsers.filter(v=> v.id !== utils.user.userId).map(v=> v.name).join(',')}]请求提交到复核列表？`}", "okText": "确定", "icon": ""}, "next": "8f30c6f1-df46-4ec0-a4f4-be96a5408244"}, "b3bd1acb-225c-4e54-aad2-bb379e46b3e7": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.xiaoxitongzhi", "dialogSettings": {"width": "780"}, "requestParams": [{"_uid": "70", "name": "selectedUsers", "value": "${selectedUsers.filter(v=> v.enabled)}"}]}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "5f565fde-ad6f-4bcc-bafc-c0c7eb1b1cb3": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/enabled/batch", "processor": "ServicePathProcessor"}, "method": "PATCH", "params": {"enabled": "${false}"}, "data": "${selectedUsers.filter(v=> v.id !== utils.user.userId && v.enabled).map(v=> v.id)}"}, "next": "a9d542da-0e53-4fdc-9b90-7f3e6c1a944f"}, "a9d542da-0e53-4fdc-9b90-7f3e6c1a944f": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "next": "375e651e-6ace-41e6-8019-4e22c336c44f"}, "375e651e-6ace-41e6-8019-4e22c336c44f": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "8f30c6f1-df46-4ec0-a4f4-be96a5408244": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchDeleteUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"status": "1"}, "data": "${selectedUsers.filter(v=> v.id !== utils.user.userId).map(v=> v.id)}"}, "next": "c5ea0092-ad68-480c-97a6-1d1efd8e7e5b"}, "c5ea0092-ad68-480c-97a6-1d1efd8e7e5b": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "删除成功", "duration": 1.5, "closable": false, "background": false}, "next": "1d3ff1e0-889e-418d-acf0-4fbb445a604e"}, "5e8af6e6-da9b-4c6b-a345-6d269ee837a6": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "5ebae9ff-7557-45f8-b2c9-1a567497a303": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "5e8af6e6-da9b-4c6b-a345-6d269ee837a6"}, "1d3ff1e0-889e-418d-acf0-4fbb445a604e": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "5ebae9ff-7557-45f8-b2c9-1a567497a303"}, "c33d2ab6-b3f1-4336-ada7-2a3528dd76f1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.copy-user", "dialogSettings": {"width": "700"}, "requestParams": [{"_uid": "100", "name": "userId", "value": "${selectedUsers[0].id}"}]}, "next": "15fb442b-9c55-4aab-a419-04a134392285"}, "15fb442b-9c55-4aab-a419-04a134392285": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "4c432d64-aa7f-4dff-b0b4-4e6b9d04b175"}, "4c432d64-aa7f-4dff-b0b4-4e6b9d04b175": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}}}, "2kgvzeuNjpp": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "c6fde84d-5287-49ef-93ac-71cdcb5edc0e"}, "c6fde84d-5287-49ef-93ac-71cdcb5edc0e": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${!selectedUsers||selectedUsers.length == 0 }", "description": "${!selectedUsers||selectedUsers.length == 0 }"}, {"condition": "${selectedUsers.length>0}", "description": "${selectedUsers.length>0}"}]}, "next": ["8524ecad-281d-4696-8a0d-1c3c6240b2ec", "736b2f21-2441-43d8-a4df-be4ccdce3b1b"]}, "8524ecad-281d-4696-8a0d-1c3c6240b2ec": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请先选择用户进行导出", "okText": "确定", "icon": ""}, "next": null}, "736b2f21-2441-43d8-a4df-be4ccdce3b1b": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "46", "name": "modal1", "value": "${true}"}]}, "next": null}}}, "mp5HpVECkHD": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "5951f75c-388b-4ea6-8c06-31a7840ae859"}, "action-59": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${`是否确认批量删除操作？`}", "okText": "确定", "icon": ""}, "next": "ae71cec6-3ea8-4fcf-8ac4-aedda6357f81"}, "action-90": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_deleted_form", "requestParams": [{"_uid": "160", "name": "ids", "value": "${selectedUsers.map(v=> v.id)}"}], "dialogSettings": {"width": "1000"}, "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "5951f75c-388b-4ea6-8c06-31a7840ae859": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectedUsers&&selectedUsers.length>0}", "description": "${selectedUsers&&selectedUsers.length>0}"}, {"condition": "${!selectedUsers||selectedUsers.length==0}", "description": "${!selectedUsers||selectedUsers.length==0}"}]}, "next": ["action-59", "6dd9fbf1-e922-4ad8-83d0-84041342c1a5"]}, "6dd9fbf1-e922-4ad8-83d0-84041342c1a5": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请先选择要删除的用户", "okText": "确定", "icon": ""}, "next": null}, "ae71cec6-3ea8-4fcf-8ac4-aedda6357f81": {"type": "Action.Http", "inputs": {"headersName": "", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/listExists", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"type": "${'user'}"}, "data": "${selectedUsers.map(v=> v.id)}", "resultName": "names"}, "next": "03c7e04c-0864-4a6c-a226-7c5ffc5093a6"}, "03c7e04c-0864-4a6c-a226-7c5ffc5093a6": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${names==null||names.length==0}", "description": "${names==null||names.length==0}"}, {"condition": "${names.length!=0}", "description": "${names.length!=0}"}]}, "next": ["action-90", "81578670-08ab-4940-ae41-4af6ec0a2461"]}, "81578670-08ab-4940-ae41-4af6ec0a2461": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'用户【'+names.join(\",\")+\"】存在未复核的请求\"}", "okText": "确定", "icon": ""}, "next": null}}}, "Dzhc92D68p": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "222e7959-8391-4ef9-b7da-679d2128e2b9"}, "action-252": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_batch_move_user", "requestParams": [{"_uid": "309", "name": "userNames", "value": "${selectUserNames}"}, {"_uid": "47", "name": "userIds", "value": "${selectIds}"}, {"_uid": "88", "name": "type", "value": "${'move'}"}, {"_uid": "183", "name": "mainDepartment", "value": "true"}], "dialogSettings": {"width": "1000", "continueOnClose": "true", "title": "批量移动主部门"}, "description": null}, "next": "action-262"}, "action-262": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "222e7959-8391-4ef9-b7da-679d2128e2b9": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectedUsers!=null&&selectedUsers.length>0}", "description": "${selectedUsers!=null&&selectedUsers.length>0}"}, {"condition": "${selectedUsers==null||selectedUsers.length==0}", "description": "${selectedUsers==null||selectedUsers.length==0}"}]}, "next": ["26b6d96a-1880-40f7-a7af-522c1beed857", "bef4d0fa-762f-4eb2-a9c8-8a12f38ff914"]}, "bef4d0fa-762f-4eb2-a9c8-8a12f38ff914": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "请先选择移动的用户", "okText": "确定", "icon": ""}, "next": null}, "26b6d96a-1880-40f7-a7af-522c1beed857": {"type": "Action.Http", "inputs": {"resultName": "names", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/listExists", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"type": "${'user'}"}, "data": "${selectedUsers.map(v=>v.id)}"}, "next": "441fa72d-e81b-41c4-8f16-2d4cef0cc0d4"}, "441fa72d-e81b-41c4-8f16-2d4cef0cc0d4": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${names==null||names.length==0}", "description": "${names==null||names.length==0}"}, {"condition": "${names.length>0}", "description": "${names.length>0}"}]}, "next": ["action-252", "efce757d-6a68-45b6-acbd-bbc11e04d853"]}, "efce757d-6a68-45b6-acbd-bbc11e04d853": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'用户【'+names.join(\",\")+\"】存在未提交的复核\"}", "okText": "确定", "icon": ""}, "next": null}}}, "RHVVslhrXv0": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "2838c806-b96f-4d11-af89-bdf191707774"}, "action-47": {"type": "Action.Http", "inputs": {"schema": {"code": {"type": "integer", "title": "返回码：1：同步错误，0：同步成功"}, "data": {"type": "object", "title": "返回数据"}, "message": {"type": "string", "title": "消息"}}, "url": {"type": "ServicePath", "source": "local", "value": "/syncLdap/users", "processor": "ServicePathProcessor"}, "method": "GET", "description": null}, "next": "976cc860-14b8-4e42-a37d-f223b80bcd96"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "2838c806-b96f-4d11-af89-bdf191707774": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "开始同步", "duration": 1.5, "closable": false, "background": false}, "next": "d3fa9d2a-e16a-4e04-b69b-14bc714db43f"}, "d3fa9d2a-e16a-4e04-b69b-14bc714db43f": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "45", "name": "syncAble", "value": "${false}"}]}, "next": "action-47"}, "04650044-3fd9-4fd6-b731-a7ddb29acb03": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "48", "name": "syncAble", "value": "${syncData!=null}"}]}, "next": "end"}, "976cc860-14b8-4e42-a37d-f223b80bcd96": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "同步完成", "duration": 1.5, "closable": false, "background": false}, "next": "04650044-3fd9-4fd6-b731-a7ddb29acb03"}}}, "nlWPLJrPmcW": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "9df42493-13e4-4a85-b399-9dd03b97cbe0"}, "9df42493-13e4-4a85-b399-9dd03b97cbe0": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${selectIds==null||selectIds.length == 0}", "description": "${selectIds==null||selectIds.length == 0}"}, {"condition": "${selectIds.length > 0}", "description": "${selectIds.length > 0}"}]}, "next": ["846995fa-acb3-43f6-a8f0-574b24ff0779", "ed980693-4e15-4d40-a90b-781c269ceecd"]}, "846995fa-acb3-43f6-a8f0-574b24ff0779": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${\"请至少选择一个用户\"}", "duration": 1.5, "closable": false, "background": false}, "next": null}, "88cd4ea5-a2c4-4ce4-a731-a28c5e53f6cf": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_batch_delay", "requestParams": [{"_uid": "54", "name": "userIds", "value": "${selectIds}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}", "title": "用户批量延期"}}, "next": "ef1f3247-2501-43b4-b532-70842d2ef2c6"}, "25a6814c-547b-4a42-a13e-8560cdb8cafe": {"type": "Action.End", "inputs": {}, "next": null}, "ef1f3247-2501-43b4-b532-70842d2ef2c6": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "25a6814c-547b-4a42-a13e-8560cdb8cafe"}, "ed980693-4e15-4d40-a90b-781c269ceecd": {"type": "Action.Http", "inputs": {"resultName": "names", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/listExists", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"type": "${'user'}"}, "data": "${selectedUsers.map(v=>v.id)}"}, "next": "2332e176-4688-4e2a-9d8b-2f6fae075366"}, "2332e176-4688-4e2a-9d8b-2f6fae075366": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${names==null||names.length==0}", "description": "${names==null||names.length==0}"}, {"condition": "${names.length!=0}", "description": "${names.length!=0}"}]}, "next": ["88cd4ea5-a2c4-4ce4-a731-a28c5e53f6cf", "db22aeb1-accf-4ae9-9cc9-3b877e68340d"]}, "db22aeb1-accf-4ae9-9cc9-3b877e68340d": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'用户【'+names.join(\",\")+\"】存在未提交的复核\"}", "okText": "确定", "icon": ""}, "next": null}}}, "0N93jnfefDF": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-90"}, "action-68": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "next": "end"}, "action-90": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "91", "name": "selectedUsers", "value": "${[]}"}, {"_uid": "57", "name": "currentSelectOrgId", "value": "${null}"}, {"_uid": "114", "name": "selectOrgId", "value": "${null}"}]}, "next": "action-68"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "3DlN4s8uY_w": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-178"}, "action-178": {"type": "Action.OpenUrl", "inputs": {"url": "/lowcode/tenant.check_admin.gf_user_submit_list", "variables": [], "cacheOpener": true, "description": null}, "next": "action-317"}, "action-317": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "_pArCGcl8RA": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-82"}, "action-82": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "83", "name": "keyword", "value": "${''}"}, {"_uid": "84", "name": "oaName", "value": "${''}"}, {"_uid": "93", "name": "manager", "value": "${''}"}, {"_uid": "96", "name": "erpNum", "value": "${''}"}], "description": null}, "next": "action-111"}, "action-111": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "112", "name": "userFilter2", "value": "${(oaName==''?'':` and oaName like '%${oaName}%' `)+(manager==''?'':`and manager eq '${manager}' `)+(erpNum==''?'':`and erpId eq '${erpNum}'`)}"}], "description": null}, "next": "action-54"}, "action-54": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "55", "name": "userFilters", "value": "${'deleted ne true and enabled eq true'}"}, {"_uid": "90", "name": "status", "value": "${'1'}"}], "description": null}, "next": "action-99"}, "action-99": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "Fwb0sYxOfIb": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "fad5fcc9-b168-43ba-b6aa-930f9ec8afe9"}, "fad5fcc9-b168-43ba-b6aa-930f9ec8afe9": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${type == 1}", "description": "${type == 1}"}, {"condition": "${type == 3}", "description": "${type == 3}"}, {"condition": "${type == 4}", "description": "${type == 4}"}, {"condition": "${type == 2}", "description": "${type == 2}"}]}, "next": ["c9443758-58b7-4f71-90df-4cd2b50cee84", "e8996c94-aec4-4303-b67f-cc97ac9d7c51", "5eb496a3-bff1-41b0-b51e-d1dd757221f4", "b57ffee1-81a7-490a-831a-e308992c53a4"]}, "c9443758-58b7-4f71-90df-4cd2b50cee84": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "79", "name": "userFilters3", "value": "${' '}"}]}, "next": "f409637f-bbb5-4b8e-9d98-1e872f78cfc9"}, "e8996c94-aec4-4303-b67f-cc97ac9d7c51": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "79", "name": "userFilters3", "value": "${'and oaUserId is not null '}"}]}, "next": "f409637f-bbb5-4b8e-9d98-1e872f78cfc9"}, "5eb496a3-bff1-41b0-b51e-d1dd757221f4": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "80", "name": "userFilters3", "value": "${'and externalId is not null'}"}]}, "next": "f409637f-bbb5-4b8e-9d98-1e872f78cfc9"}, "b57ffee1-81a7-490a-831a-e308992c53a4": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "81", "name": "userFilters3", "value": "${'and acmUserId is not null'}"}]}, "next": "f409637f-bbb5-4b8e-9d98-1e872f78cfc9"}, "f409637f-bbb5-4b8e-9d98-1e872f78cfc9": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "8c1f7328-0bb1-4223-851b-864a9192d852"}, "8c1f7328-0bb1-4223-851b-864a9192d852": {"type": "Action.End", "inputs": {}, "next": null}}}}, "body": [{"type": "IvModal", "designer": {"movein": false, "visible": false, "collapse": true}, "props": {"value": "${modal1}", "title": "导出用户数据", "width": "550", "closable": true, "mask": true, "maskClosable": false, "loading": false, "scrollable": false, "lockScroll": false, "draggable": false, "sticky": false, "stickyDistance": 10, "okText": "确定", "cancelText": "取消", "className": "le-vertical-center-modal", "zIndex": 1000, "transfer": true, "footerHide": true}, "slots": {"default": {"children": [{"type": "IamDataExporter", "props": {"pageSize": 100, "maxExportRecords": 10000, "candidateFields": {"type": "ExpKeepValue", "value": [{"fieldName": "name", "fieldDisplayName": "中文名", "fieldValueExp": null, "_uid": "2023090816294289"}, {"fieldName": "username", "fieldDisplayName": "登录名", "fieldValueExp": null, "_uid": "2023090816302092"}, {"fieldName": "oaName", "fieldDisplayName": "OA用户名", "fieldValueExp": null, "_uid": "2023090816311195"}, {"fieldName": "adShowName", "fieldDisplayName": "AD显示名", "fieldValueExp": null, "_uid": "2023090816312598"}, {"fieldName": "pinyin", "fieldDisplayName": "全拼", "fieldValueExp": null, "_uid": "20230908163135101"}, {"fieldName": "email", "fieldDisplayName": "邮件地址", "fieldValueExp": null, "_uid": "20230908163146104"}, {"fieldName": "emailInternet", "fieldDisplayName": "因特网邮件地址", "fieldValueExp": null, "_uid": "20230908163207107"}, {"fieldName": "otherEmail", "fieldDisplayName": "其它邮件地址", "fieldValueExp": null, "_uid": "20230908163257110"}, {"fieldName": "phoneNumber", "fieldDisplayName": "手机号码", "fieldValueExp": null, "_uid": "20230908163304113"}, {"fieldName": "company", "fieldDisplayName": "公司", "fieldValueExp": null, "_uid": "20230908163321116"}, {"fieldName": "otherOrgsName", "fieldDisplayName": "所属其它群组", "fieldValueExp": null, "_uid": "20230908163401122"}, {"fieldName": "position", "fieldDisplayName": "职务岗位", "fieldValueExp": null, "_uid": "20230908163432125"}, {"fieldName": "manager", "fieldDisplayName": "上级经理", "fieldValueExp": null, "_uid": "20230908163444128"}, {"fieldName": "mailboxCapacity", "fieldDisplayName": "邮箱容量", "fieldValueExp": null, "_uid": "20230908163453131"}, {"fieldName": "disableAccessToOa", "fieldDisplayName": "禁止访问OA门户", "fieldValueExp": null, "_uid": "20230908163502134"}, {"fieldName": "orgname", "fieldDisplayName": "部门名称", "fieldValueExp": null, "_uid": "20230908163823137"}]}, "exportFileName": "${utils.user.tenantName+'-用户列表-'+utils.dayjs().format('YYYYMMDDHHmmss')}", "dataSource": "${outputlist}", "exportDataSource": "${outputlist}"}, "designer": {"visible": false}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "visible": true}}}}, {"type": "FuseDoubleColumnsView", "props": {"type": "left", "fixedPartWidth": 250, "showScroll": false}, "style": "", "slots": {"left": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "margin-bottom:16px", "props": {"icon": "md-card", "padding": 12, "bordered": true, "disHover": false, "shadow": false, "replace": false, "append": false}, "class": "", "id": "", "visible": "${request.params.from !== 'external'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "", "children": [{"type": "IvText", "props": {"text": "通讯录", "showTitle": false, "maxline": 0}, "style": "color:gray;", "class": "", "id": "", "visible": false, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}]}, {"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "lock": false}, "props": {"title": "所有组织", "extra": "右侧额外内容", "name": "2", "disabled": false, "selected": "${cellValue == '2'}", "replace": false, "append": false}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "195", "name": "currentSelectOrgId", "value": "${null}"}, {"_uid": "166", "name": "cellValue", "value": "${'2'}"}], "description": null}, "description": null, "id": "action-194", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-191"}]}}, "style": "user-select: none", "slots": {"extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false}, "props": {"mode": "num", "type": "normal", "dot": true, "showZero": true, "overflowCount": 999}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "icon": {"children": [{"type": "Icon", "props": {"type": "bingo-organization", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IamOrg<PERSON>ree", "props": {"placeholder": "根据组织搜索", "value": "${selectOrgId}", "orgOptions": {"filters": "kindId eq P", "kindId": "P", "orderby": "${`sortOrder2,name`}"}, "orgUrl": "/api/tenant/org/extend/tree/orgs/extend", "rootOrgUrl": "/api/tenant/org/extend/tree/roots/extend", "showEdit": false}, "style": "", "class": "", "id": "orgTree", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "events": {"on-select-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "807", "name": "cellValue", "value": "${''}"}, {"_uid": "56", "name": "currentSelectOrgId", "value": "${$event.value ? $event.value : ''}"}, {"_uid": "158", "name": "userQueryDto.orgId", "value": "${$event.value ? $event.value : ''}"}], "description": null}, "id": "action-806", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-155"}]}, "on-edit-change": {"actions": []}, "on-delete-change": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "right": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px;height:100%;", "children": [{"type": "Card", "designer": {"movein": false, "lock": false}, "props": {"title": "${cellValue == '0' ? '所有用户':cellValue == '1' ? '已删除用户':'组织用户'}", "padding": 0, "bordered": true}, "style": "margin-top:0px;", "class": "ledesign-card-extra", "id": "", "visible": "${cellValue!=='3'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false, "demo": {"props.data": "[{\"col1\":\"admin\",\"col2\":\"admin\",\"col3\":\"企业用户\",\"col4\":\"2022-06-20 15:00:00\"}]"}}, "props": {"data": [], "tableCols": [], "enableSelectionCol": true, "enableIndexCol": true, "stripe": false, "border": false, "show-header": true, "loading": "${userList.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无用户", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "visibleHeaderOp": true, "headerSearch": {"enable": false, "filters": [{"key": "keyword", "condition": "cn"}]}, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1, "simple": false}, "ds": "${userList}", "filters": {}, "loadOnMounted": true}, "events": {"on-current-change": {"actions": []}, "on-select": {"actions": []}, "on-select-cancel": {"actions": []}, "on-select-all": {"actions": []}, "on-select-all-cancel": {"actions": []}, "on-selection-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "329", "name": "selectedUsers", "value": "${$event}"}], "description": null}, "description": null, "id": "action-328", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "194", "name": "selectIds", "value": "${selectedUsers.filter(v=> v.id !== utils.user.userId).map(v=> v.id)}"}], "description": null}, "description": null, "id": "action-193", "activeOn": null}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setSelectUserNames", "params": []}, "description": null, "id": "action-41"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${outputlist}"}, "description": null, "id": "action-82"}]}, "on-sort-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "67", "name": "userListQueryOptions.orderby", "value": "${`sortOrder ${$event.order === 'normal' ? 'asc' : $event.order},updatedAt desc`}"}], "description": null}, "description": null, "id": "action-66", "activeOn": null}]}, "on-filter-change": {"actions": []}, "on-row-click": {"actions": []}, "on-row-dblclick": {"actions": []}, "on-expand": {"actions": []}, "on-cell-click": {"actions": []}, "on-drag-drop": {"actions": []}, "on-change": {"actions": []}, "on-page-size-change": {"actions": []}, "on-prev": {"actions": []}, "on-next": {"actions": []}}, "slots": {"default": {"children": [{"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "姓名", "keyName": "name", "dataTimeType": "YYYY-MM-DD", "minWidth": 80, "width": 80, "children": []}, "style": "margin:0px;", "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "${tableCell.row.name}", "type": "page", "linkColor": true, "disabled": "${cellValue === '1'}", "replace": false}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_details", "requestParams": [{"_uid": "40", "name": "userId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1200"}, "description": null}, "description": null, "id": "action-39", "activeOn": null}]}, "click.stop": {"actions": []}}, "auth": {"turnOn": true, "forbiddenStatus": "disabled", "requiredPermission": []}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "登录名", "keyName": "username", "dataTimeType": "YYYY-MM-DD", "sortable": false, "minWidth": 80, "width": 100}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.username}", "showTitle": false, "maxline": 0}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "OA用户名", "keyName": "oaName", "width": 100}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "ERP号", "keyName": "erpId", "width": 80}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "主部门", "keyName": "orgName", "dataTimeType": "YYYY-MM-DD", "minWidth": 80, "resizable": true, "ellipsis": true, "children": [], "tooltip": true}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "任职岗位", "keyName": "position", "minWidth": 80, "width": 100}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 1, "text": "${tableCell.row.positions?.map(v=> v.positionName).join(',')}", "showTitle": true}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "主邮箱地址", "keyName": "email", "sortable": false, "width": 150}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "管理者", "keyName": "admin", "width": 100, "children": []}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "账号类型", "keyName": "staffType", "width": 100, "children": []}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "AD_ID", "keyName": "externalId", "width": 150}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "ACM_ID", "keyName": "acmUserId", "width": 150}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "OA_ID", "keyName": "oaUserId", "width": 150, "children": []}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "状态", "keyName": "enabled", "dataDictionary": "UserStatus", "width": 60, "children": []}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${statusText(tableCell.row)}", "maxLine": 0}, "designer": {"demo": {"props.text": "启用", "props.text.type": "string"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作", "keyName": "operations", "dataTimeType": "YYYY-MM-DD", "width": 200, "fixed": "right", "dataType": "text", "align": "left", "children": [], "verticalAlign": "le-align-middle", "filterMultiple": true}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"text": "编辑", "type": "text", "size": "small"}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_edit_user", "requestParams": [{"_uid": "64", "name": "userId", "value": "${tableCell.row.id}"}, {"_uid": "74", "name": "type", "value": "${'update'}"}], "dialogSettings": {"width": "1200"}, "description": null}, "description": null, "id": "action-63", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvLink", "props": {"text": "注销", "type": "text", "size": "small", "disabled": "${tableCell.row.id === utils.user.userId}"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'是否确认提交注销用户【'+tableCell.row.name +'】的需求到复核列表 ？'}", "okText": "确定", "icon": "", "description": null}, "id": "action-76", "activeOn": null}, {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_delete_info_one", "requestParams": [{"_uid": "74", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}, "description": null}, "description": null, "id": "action-58", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvLink", "props": {"text": "移动", "type": "text", "size": "small"}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_move", "requestParams": [{"_uid": "316", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}, "description": null}, "description": null, "id": "action-312", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "designer": {"collapse": false}}, {"type": "IvLink", "props": {"text": "修改密码", "type": "text", "size": "small"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.zhong<PERSON>mima", "requestParams": [{"_uid": "49", "name": "userId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000"}, "description": null}, "description": null, "id": "action-48", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "置空密码"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "确定置空密码吗？", "okText": "确定", "icon": "", "description": null}, "description": null, "id": "action-79", "activeOn": null}, {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_clear_pass", "requestParams": [{"_uid": "197", "name": "id", "value": "${tableCell.row.id}"}, {"_uid": "66", "name": "name", "value": "${tableCell.row.name}"}], "dialogSettings": {"width": "1000", "title": "置空密码"}, "description": null}, "description": null, "id": "action-196", "activeOn": null}]}, "click.stop": {"actions": []}}}, {"type": "IvLink", "props": {"text": "${!tableCell.row.enabled ? '启用':'禁用'}", "type": "text", "size": "small", "linkColor": false, "disabled": "${tableCell.row.id === utils.user.userId}"}, "events": {"click": "N9CTkaWbo"}, "style": "color:#3457cc;", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "刷新ldap"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "确定触发刷新ldap吗？", "okText": "确定", "icon": "", "description": null}, "description": null, "id": "action-96", "activeOn": null}, {"type": "Action.Http", "inputs": {"url": "/api/tenant/user/extend/incre", "method": "GET", "params": {"id": "${tableCell.row.id}"}}, "description": null, "id": "action-101"}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已触发刷新ldap", "duration": 1.5, "closable": false, "background": false, "description": null}, "description": null, "id": "action-112", "activeOn": null}]}, "click.stop": {"actions": []}}}, {"type": "IvLink", "props": {"text": "同步", "type": "text", "size": "small"}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_sync_user", "requestParams": [{"_uid": "64", "name": "username", "value": "${tableCell.row.username}"}], "dialogSettings": {"width": "1000", "title": "用户同步"}, "description": null}, "description": null, "id": "action-63", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "visible": true}}}}]}, "header": {"children": [{"type": "IvButton", "props": {"type": "default", "size": "default", "icon": "md-refresh"}, "events": {"click": "0N93jnfefDF", "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "创建用户", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_add_user", "requestParams": [{"_uid": "73", "name": "orgId", "value": "${selectOrgId}"}, {"_uid": "80", "name": "type", "value": "${'add'}"}], "dialogSettings": {"width": "1200"}, "description": null}, "id": "action-26", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "id": "action-317"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activeUserTotal}"}, "description": null, "id": "action-38"}]}, "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${cellValue !== '1'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvButton", "props": {"text": "提交列表", "type": "primary", "size": "default"}, "events": {"click": "3DlN4s8uY_w", "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${cellValue !== '1'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvButton", "props": {"text": "导出", "type": "primary", "size": "default", "replace": false, "target": "_self", "append": false}, "events": {"click": "2kgvzeuNjpp"}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvButton", "props": {"text": "批量新增AD用户", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_upload_ad", "dialogSettings": {"width": "1300", "title": "批量新增AD用户"}, "description": null}, "description": null, "id": "action-51", "activeOn": null}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "批量删除", "type": "primary", "size": "default", "replace": false, "target": "_self", "append": false}, "events": {"click": "mp5HpVECkHD"}, "style": "", "class": "", "id": "", "visible": "${cellValue === '0' || selectOrgId}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvButton", "props": {"text": "移动", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "Dzhc92D68p"}}, {"type": "IvButton", "props": {"text": "移动主部门", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "Dzhc92D68p"}}, {"type": "IvButton", "props": {"text": "批量延期", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "nlWPLJrPmcW", "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "用户同步", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_sync_user", "requestParams": [], "dialogSettings": {"width": "1000", "title": "用户同步"}, "description": null}, "description": null, "id": "action-74", "activeOn": null}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "批量导入延期用户", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.batch_import_delay_user", "requestParams": [], "dialogSettings": {"width": "1000", "title": "批量延期用户"}, "description": null}, "description": null, "id": "action-63", "activeOn": null}]}, "click.stop": {"actions": []}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "状态："}, "style": "width:140px;text-align:right;"}, {"type": "IvSelect", "props": {"transfer": true, "placeholder": "状态", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('UserStatusQuery').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "value": "${userQueryDto.status}", "dataDictionaryDisabled": [], "dataDictionary": "UserStatusQuery"}, "style": "margin:0px;", "events": {"on-change": {"actions": []}, "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": []}, "on-create": {"actions": []}, "on-select": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "用户："}, "style": "width:140px;text-align:right;"}, {"type": "Input", "props": {"type": "text", "size": "default", "placeholder": "用户名称 | 账号 | erp号 | OA用户名", "border": true, "search": true, "value": "${userQueryDto.keyword}", "clearable": true, "enterButton": false}, "style": "", "events": {"on-enter": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-123"}]}, "on-click": {"actions": []}, "on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "60", "name": "userFilter2", "value": "${keyword==''?'':`( oaName like '%${keyword}%' or admin like '%${keyword}%' or erpId eq '${keyword}' or username like '%${keyword}%' or name like '%${keyword}%' ) `}"}], "description": null}, "description": null, "id": "action-59", "activeOn": null}]}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "管理者："}, "style": "width:140px;text-align:right;"}, {"type": "Input", "props": {"type": "text", "size": "default", "placeholder": "账号", "border": true, "search": true, "value": "${userQueryDto.adminKeyword}", "clearable": true, "enterButton": false}, "style": "", "events": {"on-enter": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-123"}]}, "on-click": {"actions": []}, "on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "60", "name": "userFilter2", "value": "${keyword==''?'':`( oaName like '%${keyword}%' or admin like '%${keyword}%' or erpId eq '${keyword}' or username like '%${keyword}%' or name like '%${keyword}%' ) `}"}], "description": null}, "description": null, "id": "action-59", "activeOn": null}]}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "组织："}, "style": "width:140px;text-align:right;"}, {"type": "Input", "props": {"type": "text", "size": "default", "placeholder": "组织名称", "border": true, "search": true, "value": "${userQueryDto.orgKeyword}", "clearable": true, "enterButton": false}, "style": "", "events": {"on-enter": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-123"}]}, "on-click": {"actions": []}, "on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "60", "name": "userFilter2", "value": "${keyword==''?'':`( oaName like '%${keyword}%' or admin like '%${keyword}%' or erpId eq '${keyword}' or username like '%${keyword}%' or name like '%${keyword}%' ) `}"}], "description": null}, "description": null, "id": "action-59", "activeOn": null}]}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "用户类型："}, "style": "width:140px;text-align:right;"}, {"type": "IvSelect", "props": {"notFoundText": "无匹配数据", "data": "${utils.optionSet.find('UserAccountQueryType').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "border": true, "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "value": "${userQueryDto.type}", "dataDictionary": "UserAccountQueryType"}, "events": {"on-change": "Fwb0sYxOfIb", "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": []}, "on-create": {"actions": []}, "on-select": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "角色："}, "style": "width:140px;text-align:right;"}, {"type": "IvSelect", "props": {"notFoundText": "无匹配数据", "data": "${utils.optionSet.find('userRole').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "border": true, "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "value": "${userQueryDto.roleKeyword}", "dataDictionary": "userRole"}, "events": {"on-change": "Fwb0sYxOfIb", "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": []}, "on-create": {"actions": []}, "on-select": {"actions": []}}}], "style": "width:33%;display:flex;align-items:center;margin-bottom:8px;"}], "style": "display:flex;padding:0px;align-items:center;margin-top:8px;margin-right:0px;margin-bottom:0px;margin-left:0px;flex:1;flex-wrap:wrap;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "查询", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-85"}]}, "click.stop": {"actions": []}}, "style": "margin-left:12px;"}, {"type": "IvButton", "props": {"text": "重置", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "133", "name": "userQueryDto", "value": "${{\r\n  \"status\": \"all\",\r\n  \"keyword\": null,\r\n  \"type\": \"all\",\r\n  \"orgId\": null,\r\n\"adminKeyword\":null,\r\n\"orgKeyword\":null,\r\n\"roleKeyword\":null\r\n}}"}, {"_uid": "84", "name": "currentSelectOrgId", "value": "${null}"}], "description": null}, "description": null, "id": "action-129", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "description": null, "id": "action-121", "activeOn": null}]}, "click.stop": {"actions": []}}}], "style": "width:200px;padding-top:8px;"}], "style": "display:flex;justify-content:space-between;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false, "lock": false}, "props": {"title": "用户", "content": "支持管理当前租户的组织和用户信息", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}, "style": ""}], "auth": {"turnOn": true, "requiredPermission": [], "license": null}, "meta": {"title": "用户列表", "name": "user_list", "packageName": "tenant.user_admin"}}