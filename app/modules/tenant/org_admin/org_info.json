{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "kindData", "title": "获取记录", "multiple": false, "schema": {"id": {"type": "string", "title": "唯一ID"}, "name": {"type": "string", "title": "类型名称"}, "description": {"type": "string", "title": "类型描述"}, "system": {"type": "boolean", "title": "是否内置组织类型"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}}, "fetchWhen": "${formData.type === 'V'}", "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/org_kinds/{id}", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${formData.kindId}", "select": null}}], "variables": {"formData": {"type": "object", "default": {}, "title": "", "orderNo": 0}, "isRootOrg": {"type": "boolean", "default": false, "title": "", "orderNo": 1}}, "events": {"on-render": {"actions": [{"type": "Action.Http", "inputs": {"resultName": "data", "schema": {"id": {"type": "string"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "childIndex": {"type": "integer"}, "path": {"type": "string"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/orgs/{id}", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${request.params.orgId}"}, "description": null}, "id": "action-345", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "355", "name": "formData", "value": "${data}"}], "description": null}, "id": "action-354", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "375", "name": "isRootOrg", "value": "${!data.parentId}"}], "description": null}, "id": "action-374", "activeOn": null}]}, "on-rendered": {"actions": []}, "on-destroy": {"actions": []}}, "functions": {"transformed": false, "script": "export function validateCode(rule, value, callback) {\r\n  if (!value) {\r\n    callback()\r\n    return\r\n  }\r\n  const code = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ@#$*_'\r\n  for (let i = 0; i < value.length; i++) {\r\n    if (!code.includes(value[i])) {\r\n      callback(new Error('仅包含数字字母@#$*_'));\r\n      return\r\n    }\r\n  }\r\n  callback()\r\n}\r\n\r\nexport function validateCodeUnique(rule, value, callback) {\r\n  if (!value) {\r\n    callback()\r\n    return\r\n  }\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  http.get('/api/orgs', { params: { filters: `code eq '${value}' and id ne ${renderContext.variables.formData.id}` } })\r\n    .then(res => {\r\n      if (res.data.length) {\r\n        callback(new Error('code重复，请修改！'))\r\n      } else {\r\n        callback()\r\n      }\r\n    })\r\n}\r\n\r\nexport function buildSubmitForm(form) {\r\n  return utils.lodash.omit(form, ['childTenantId', 'createdAt', 'createdBy', 'dataLevel', 'deleted', 'enabled', 'extended', 'externalId', 'id', 'kindId', 'path', 'pinyin', 'py', 'tenantId', 'type', 'updatedAt', 'updatedBy'])\r\n}"}, "body": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "FuseTogglePanel", "props": {"title": "基本设置", "subtitle": "更新组织信息，如名称，上级组织等", "collapse": false, "separator": true}, "slots": {"default": {"children": [{"type": "IvForm", "props": {"gutter": "0", "labelPosition": "right", "labelWidth": 130, "showMessage": true, "model": "${formData}", "disabled": "${formData.dataLevel >= 2}"}, "children": [{"type": "IvFormInput", "props": {"labelName": "组织名称", "span": "24", "width": "100%", "type": "text", "size": "default", "placeholder": "请输入组织名称", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "该项为必填项"}]}, "prop": "name", "value": "${formData.name}", "requiredName": "ivu-form-item-required", "maxlength": 50}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "组织全称", "placeholder": "请输入组织全称", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${formData.alias}"}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "组织类型", "span": "24", "width": "100%", "value": "${kindData.data.name}", "type": "text", "size": "default", "placeholder": "请输入", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "required": true, "message": "此项为必填项"}]}, "disabled": true, "requiredName": "ivu-form-item-required", "prop": "kindId"}, "style": "", "class": "", "id": "", "visible": "${formData.kindId !== 'P'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IamFormOrgSelect", "designer": {"movein": false}, "props": {"labelName": "上级组织", "span": "24", "type": "input", "multiple": false, "disabled": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "prop": "parentId", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "该项为必填项"}]}, "value": "${formData.parentId}", "disabledId": "${request.params.orgId}", "requiredName": "ivu-form-item-required", "orgOptions": "${{filters:`kindId eq ${formData.kindId}`}}", "switchOrgKind": false, "canSelectDisabledIdChild": false}, "style": "", "class": "", "id": "", "visible": "${formData.type && !isRootOrg}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "组织编码", "placeholder": "请输入组织编码", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}, {"type": "method", "value": "method", "label": "自定义函数", "pattern": "", "method": "${validateCode}", "message": ""}, {"type": "method", "value": "method", "label": "自定义函数", "trigger": "blur", "pattern": "", "method": "${validateCodeUnique}", "message": ""}]}, "width": "100%", "show-message": true, "value": "${formData.code}", "prop": "code"}}, {"type": "IvFormInputNumber", "designer": {"movein": false}, "props": {"labelName": "排序号", "span": "24", "width": "100%", "value": "${formData.sortOrder}", "size": "default", "step": 1, "editable": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "该项为必填项"}]}, "min": 0, "precision": 0, "requiredName": "ivu-form-item-required", "prop": "sortOrder", "placeholder": "输入排序号"}, "slots": {"label": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "排序号"}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": false}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "组织根据排序号顺序排序，数字越小越靠前", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "表单项", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true}, "style": "", "class": "", "id": "", "visible": "${formData.kindId === 'P'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "slots": {"label": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "成为虚拟组织"}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": false}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "企业内的外部人员或一些特殊账号所在组织支持成为虚拟组织，虚拟组织及其所有子组织、组织内的所有用户，在同步数据到下游应用时，避免影响企业正常用户适用，支持选择是否同步。", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvSwitch", "props": {"size": "default", "trueValue": "${'V'}", "falseValue": "${'S'}", "value": "${formData.type}", "disabled": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "是否受限", "span": 24, "width": "100%", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "此项为必填项"}]}}, "slots": {"label": {"children": [{"type": "IvText", "props": {"text": "是否隐藏", "maxLine": 0}}, {"type": "IvTooltip", "designer": {"movein": false, "lock": false}, "props": {"placement": "top", "delay": "0", "theme": "dark", "offset": "0", "disabled": false, "always": false, "eventsEnabled": false, "transfer": true, "content": "设置为隐藏，将不会在通讯录中显示（仅作用于通讯录）", "maxWidth": "200"}, "slots": {"title": {"children": [{"type": "Icon", "props": {"type": "ios-help-circle-outline", "size": 16, "color": "rgba(45,140,240,1)"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvRadioSingle", "designer": {"moveChild": false, "movein": false}, "props": {"value": "${formData.hidden}", "text": "是", "size": "default"}}, {"type": "IvRadioSingle", "designer": {"moveChild": false, "movein": false}, "props": {"value": "${formData.hidden}", "text": "否", "size": "default", "trueValue": "${false}", "falseValue": "${true}"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormInput", "props": {"labelName": "邮件列表地址", "span": "24", "width": "100%", "type": "text", "autosize": true, "size": "default", "placeholder": "请输入用户组的邮件地址", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "邮箱", "type": "regex", "value": "email", "trigger": "change", "pattern": "/^[\\w-]+(\\.[\\w-]+)*@([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$/", "message": "请输入正确格式的邮箱"}]}, "rows": 1, "value": "${formData.email}", "maxlength": 200, "prop": "email"}, "slots": {"label": {"children": [{"type": "IvText", "props": {"text": "邮箱地址", "maxLine": 0}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormInput", "props": {"labelName": "描述", "span": "24", "width": "100%", "type": "textarea", "autosize": true, "size": "default", "placeholder": "请输入组织简要描述", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${formData.description}"}}, {"type": "MetaDynamicEntityForm", "props": {"mode": "view", "formType": "edit", "useDefaultStore": true, "span": "24", "formId": "meta_file_org_detail", "entityName": "IamOrg", "onlyDynamicFields": true, "formData": "${formData}", "labelWidth": 130}, "style": "width:100%;", "class": "", "id": "", "visible": "${formData.id}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"span": "24", "requiredName": "ivu-form-item-required"}, "slots": {"default": {"children": [{"type": "IvButton", "props": {"text": "保存", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1", "description": null}, "id": "action-304"}, {"type": "Action.Http", "inputs": {"url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/orgs/{id}", "processor": "ServicePathProcessor"}, "method": "PATCH", "params": {"id": "${formData.id}"}, "data": "${buildSubmitForm(formData)}", "description": null}, "id": "action-308", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "保存成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-334"}]}, "click.stop": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "", "class": "", "id": "form1", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"onValidate": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1", "description": null}, "id": "action-390"}]}}, "designer": {"lock": false}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "FuseTogglePanel", "props": {"title": "高级设置", "subtitle": "删除组织等高级功能", "collapse": false, "separator": true}, "style": "", "class": "", "id": "", "visible": "${formData.dataLevel === 0}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"text": "${'删除 ' + formData.name}", "maxline": 0, "showTitle": false}, "style": "display:block;font-weight:500;color:#EA1A1A;"}, {"type": "IvText", "props": {"text": "${`是否确认删除组织 ${formData.name}？`}", "maxline": 0, "showTitle": false}, "style": "display:block;margin-top:8px;margin-bottom:8px;"}, {"type": "IvButton", "props": {"text": "删除组织", "type": "error", "size": "default"}, "style": "", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${'确定删除当前组织 '+ formData.name + '？'}", "okText": "确定", "icon": "", "description": null}, "id": "action-133"}, {"type": "Action.Http", "inputs": {"url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/orgs/{id}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"id": "${formData.id}", "soft": "${true}"}, "description": null}, "id": "action-136", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-146"}, {"type": "Action.Back", "inputs": {"description": null}, "id": "action-36"}]}}}], "style": "margin:0px;border-style:none;padding:8px;margin-top:0px;background-color:#FFECEC;border-radius:4px;", "designer": {"lock": false}, "class": "", "id": "", "visible": "${!isRootOrg}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"lock": false}, "style": ""}], "master": "tenant.org_admin.org_detail_layout", "meta": {"title": "组织信息", "name": "org_info", "packageName": "tenant.org_admin"}}