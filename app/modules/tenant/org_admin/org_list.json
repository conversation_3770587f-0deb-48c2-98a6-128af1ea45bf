{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "orgKinds", "title": "查询列表", "multiple": true, "schema": {"id": {"type": "string"}, "name": {"type": "string", "title": "类型名称"}, "description": {"type": "string", "title": "类型描述"}, "system": {"type": "boolean", "title": "是否内置组织类型"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}}, "lazy": true, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/org_kinds/orgs/of", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": "${100}", "offset": null, "select": null, "filters": "${`system ne true`}", "search": null, "expand": null, "joins": null, "orderby": null, "total": "${true}"}}, {"id": "userList", "title": "查询用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "sortOrder": {"type": "integer", "title": "排序"}, "username": {"type": "string", "title": "登陆账户"}, "type": {"type": "string", "title": "用户类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "fetchWhen": "${selectOrgKindId}", "lazy": true, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${selectOrgId ? `o.id eq ${selectOrgId} and o.kindId eq ${selectOrgKindId} and deleted ne true` : `o.kindId eq ${selectOrgKindId} and deleted ne true`}", "search": "${keyword}", "expand": "${'org(name,id)'}", "joins": null, "orderby": "${userQueryOptions.orderby}", "total": "${true}", "inOrgIds": null, "orgType": "${'V'}", "roleFilters": "${selectOrgKindId ? (selectOrgId ? `r.ownerId eq '${selectOrgKindId}' and o.id eq ${selectOrgId}` : `r.ownerId eq '${selectOrgKindId}'`) : \"\"\r\n}", "kindId": "${selectOrgKindId}"}}, {"id": "orgList", "title": "查询列表", "multiple": true, "schema": {"id": {"type": "string"}, "sortOrder": {"type": "integer", "title": "排序"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}, "fetchWhen": "${selectOrgKindId}", "lazy": true, "autoReload": false, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/orgs", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${`kindId eq ${selectOrgKindId} and type eq 'V' and deleted eq false and parentId is null`}", "search": null, "expand": null, "joins": null, "orderby": null, "total": null}}], "events": {"on-render": {"actions": []}, "on-rendered": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${orgKinds}"}, "description": null, "id": "action-179"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "198", "name": "selectOrgKindId", "value": "${orgKinds.data.length && orgKinds.data[0].id}"}], "description": null}, "description": null, "id": "action-197", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${orgList}"}, "description": null, "id": "action-184"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "44", "name": "selectOrgId", "value": "${orgList.data.length && orgList.data[0].id}"}], "description": null}, "description": null, "id": "action-43", "activeOn": null}]}, "on-destroy": {"actions": []}}, "variables": {"selectOrgKindId": {"type": "string", "default": "", "title": "选择的虚拟组织", "orderNo": 0}, "selectOrgId": {"type": "string", "default": "", "title": "selectOrgId", "orderNo": 1}, "keyword": {"type": "string", "default": "", "title": "keyword", "orderNo": 2}, "userQueryOptions": {"type": "object", "default": {"orderby": "sortOrder,updatedAt desc"}, "title": "userQueryOptions", "orderNo": 3}}, "orchestrations": {}, "body": [{"type": "FuseDoubleColumnsView", "props": {"type": "left", "fixedPartWidth": 250, "showScroll": false}, "style": "margin:16px;", "slots": {"left": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "", "props": {"title": "卡片容器", "padding": 12, "shadow": false, "bordered": true, "disHover": false, "replace": false, "target": "_self", "append": false}, "class": "ivu-card-small", "slots": {"title": {"children": [{"type": "IvFlex", "designer": {"movein": false, "moveout": false}, "style": "", "children": [{"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "props": {"flex": "auto"}}, {"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "children": [{"type": "Icon", "props": {"type": "md-add", "size": "20"}, "style": "margin-left:8px;cursor: pointer;", "events": {"click.stop": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.xunizuzhi.chuangjianzuzhi", "requestParams": [{"_uid": "44", "name": "kindId", "value": "${selectOrgKindId}"}, {"_uid": "182", "name": "parentId", "value": "${selectOrgId}"}], "dialogSettings": null, "description": null}, "description": null, "id": "action-300", "activeOn": null}, {"type": "Action.CallComponentMethod", "inputs": {"comId": "orgTree", "method": "refresh", "params": []}, "description": null, "id": "action-160"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${orgList}"}, "description": null, "id": "action-84"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "157", "name": "selectOrgId", "value": "${orgList.data.length && orgList.data[0].id}"}]}, "description": null, "id": "action-156"}]}}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.multiOrgM_.createMultiOrg"]}}]}], "props": {"mode": "horizontal", "gutter": "0", "align": "center"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvText", "props": {"text": "组织", "showTitle": false, "maxline": 0}, "style": "color:gray;"}, {"type": "IvSelect", "props": {"transfer": true, "placeholder": "请选择组织类型", "size": "default", "placement": "bottom-start", "notFoundText": "无匹配数据", "data": "${[{name:'默认组织',id:'P'}].concat(orgKinds.data)}", "keyNames": {"label": "name", "value": "id"}, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "value": "${selectOrgKindId}"}, "events": {"on-change": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${orgList}"}, "description": null, "id": "action-265"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "398", "name": "selectOrgId", "value": "${orgList.data.length ? orgList.data[0].id : ''}"}], "description": null}, "description": null, "id": "action-397", "activeOn": null}, {"type": "Action.CallComponentMethod", "inputs": {"comId": "orgTree", "method": "refresh", "params": [], "description": null}, "description": "刷新组件树", "id": "action-39", "activeOn": null}]}, "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": []}, "on-create": {"actions": []}, "on-select": {"actions": []}}}, {"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "lock": false}, "props": {"title": "所有组织", "extra": "右侧额外内容", "name": "2", "disabled": false, "selected": "${cellValue == '2'}", "replace": false, "append": false}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "813", "name": "active", "value": "${!active}"}], "description": null}, "id": "action-1258", "activeOn": null}]}}, "style": "user-select: none", "slots": {"extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false}, "props": {"mode": "num", "count": "${activeOrgCount.total}", "type": "normal", "dot": true, "showZero": true, "overflowCount": 999}}, {"type": "Icon", "props": {"type": "md-add", "size": "16"}, "events": {"click.stop": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.org_admin.add_org", "requestParams": [{"_uid": "58", "name": "parentId", "value": "${currentSelectOrgId || ''}"}], "description": null}, "id": "action-221", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activeOrgCount}", "description": null}, "id": "action-148"}, {"type": "Action.CallComponentMethod", "inputs": {"comId": "orgTree", "method": "refresh", "params": [], "description": null}, "id": "action-37", "activeOn": null, "description": "刷新组织树"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "60", "name": "currentSelectOrgId", "value": null}]}, "description": null, "id": "action-59"}]}}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["t.userM_.manageOrg"]}}, {"type": "Icon", "props": {"type": "${!active ? 'ios-arrow-down':'ios-arrow-up'}", "size": 16}, "events": {"click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [], "description": null}, "id": "action-1090", "activeOn": null}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "icon": {"children": [{"type": "Icon", "props": {"type": "bingo-organization", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IamOrg<PERSON>ree", "props": {"placeholder": "根据组织搜索", "value": "${selectOrgId}", "orgOptions": {"filters": "${`kindId eq ${selectOrgKindId} and type eq 'V'`}", "kindId": "${selectOrgKindId}", "orderby": "${`sortOrder,name`}"}, "scopeCodeMap": {"update": "${`t.multiOrgM_.${selectOrgKindId}.updateMultiOrg`}", "delete": "${`t.multiOrgM_.${selectOrgKindId}.deleteMultiOrg`}"}, "rootOrgUrl": "/api/tenant/orgs/tree/roots", "orgUrl": "/api/tenant/orgs/tree/orgs", "showEdit": true}, "style": "", "class": "", "id": "orgTree", "visible": "${selectOrgKindId}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "events": {"on-select-change": {"actions": []}, "on-edit-change": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "/lowcode/tenant.org_admin.org_info", "variables": [{"_uid": "548", "name": "orgId", "value": "${$event.value}"}], "cacheOpener": true, "description": null}, "id": "action-547", "activeOn": null}]}, "on-delete-change": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`确定删除当前组织 ${$event.current.name}？`}", "okText": "确定", "icon": "", "description": null}, "description": null, "id": "action-282", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/orgs/{id}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"id": "${$event.value}", "transferOrgId": null, "soft": "${false}"}, "description": null}, "description": null, "id": "action-290", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-58"}, {"type": "Action.CallComponentMethod", "inputs": {"comId": "orgTree", "method": "refresh", "params": []}, "description": null, "id": "action-63"}]}}, "designer": {"demo": {"props.value": [], "props.value.type": "array"}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "text-align:center;", "children": [{"type": "IvText", "props": {"text": "请先选择组织类型", "maxLine": 0}}], "class": "", "id": "", "visible": "${!selectOrgKindId}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "right": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "", "props": {"title": "组织用户", "padding": 16, "shadow": false, "bordered": true, "disHover": false, "replace": false, "target": "_self", "append": false}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": false, "data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "padding": 0, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": "${userList.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "indentSize": 16, "visibleHeaderOp": true, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${userList}"}, "events": {"on-current-change": {"actions": []}, "on-select": {"actions": []}, "on-select-cancel": {"actions": []}, "on-select-all": {"actions": []}, "on-select-all-cancel": {"actions": []}, "on-selection-change": {"actions": []}, "on-sort-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "155", "name": "userQueryOptions.orderby", "value": "${${`sortOrder ${$event.order === 'normal' ? 'asc' : $event.order},updatedAt desc`}}"}]}, "description": null, "id": "action-154"}]}, "on-filter-change": {"actions": []}, "on-row-click": {"actions": []}, "on-row-dblclick": {"actions": []}, "on-expand": {"actions": []}, "on-cell-click": {"actions": []}, "on-drag-drop": {"actions": []}, "on-change": {"actions": []}, "on-page-size-change": {"actions": []}, "on-prev": {"actions": []}, "on-next": {"actions": []}}, "slots": {"header": {"children": [{"type": "IvButton", "props": {"type": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-74"}]}}}, {"type": "IvButton", "props": {"text": "添加用户", "type": "primary", "disabled": "${!orgList.data.length}"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.xunizuzhi.tianjiayonghu", "requestParams": [{"_uid": "95", "name": "orgId", "value": "${selectOrgId}"}, {"_uid": "102", "name": "kindId", "value": "${selectOrgKindId}"}, {"_uid": "323", "name": "selectOrgKindId", "value": "${selectOrgKindId}"}], "description": null}, "description": null, "id": "action-94", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-37"}]}}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "style": "", "class": "", "id": "", "visible": "${utils.FlyVueCore.PermissionService.hasPerm(`t.multiOrgM_.${selectOrgKindId}.manageMember`)}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "Input", "designer": {"movein": false}, "props": {"type": "text", "size": "default", "placeholder": "根据用户名称搜索", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "value": "${keyword}", "search": true}, "style": "width:200px;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "用户名称", "keyName": "name", "show": true}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "${tableCell.row.name}", "type": "page", "linkColor": true}, "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "/tenant/lowcode/tenant.xunizuzhi.yonghuxiangqing", "variables": [{"_uid": "297", "name": "userId", "value": "${tableCell.row.id}"}, {"_uid": "68", "name": "orgId", "value": "${tableCell.row.otherOrgId}"}, {"_uid": "254", "name": "otherOrgId", "value": "${tableCell.row.otherOrgId}"}], "cacheOpener": true, "description": null}, "description": null, "id": "action-296", "activeOn": null}]}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "登录账号", "keyName": "name_59"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.username + '@' + tableCell.row.tenantCode}", "showTitle": false, "maxline": 0}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "所属多维组织", "keyName": "date", "show": true}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvTooltip", "designer": {"movein": false}, "props": {"title": "${tableCell.row.otherOrgName}", "content": "${tableCell.row.otherFullOrgName}", "trigger": "mouseenter", "placement": "top", "delay": 0, "theme": "dark", "maxWidth": 200}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "角色", "keyName": "roleNames", "dataType": "text", "dataTimeType": "YYYY-MM-DD", "align": "left", "verticalAlign": "le-align-middle", "sortable": false, "filterMultiple": true}, "slots": {"content": {"children": [{"type": "IvText", "designer": {"demo": {"props.text": "角色"}}, "props": {"text": "${tableCell.row.roles && tableCell.row.roles.map(v=> v.name).join('，') || '/'}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "排序号", "keyName": "sortOrder", "sortable": true}}, {"type": "IvTableGridColumn", "props": {"title": "操作", "width": 140, "keyName": "action", "show": true, "align": "left"}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "查看", "linkColor": true, "disabled": false, "replace": false}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "/tenant/lowcode/tenant.xunizuzhi.yonghuxiangqing", "variables": [{"_uid": "235", "name": "userId", "value": "${tableCell.row.id}"}, {"_uid": "61", "name": "orgId", "value": "${tableCell.row.otherOrgId}"}, {"_uid": "250", "name": "otherOrgId", "value": "${tableCell.row.otherOrgId}"}], "cacheOpener": true, "description": null}, "description": null, "id": "action-231", "activeOn": null}]}}}, {"type": "IvLink", "props": {"tag": "a", "text": "退出", "linkColor": true, "disabled": false, "replace": false}, "class": "px-sm", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确认移除用户 ${tableCell.row.name}？移除后，用户将退出当前虚拟组织。`}", "okText": "确定", "icon": ""}, "description": null, "id": "action-251"}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user_orgs/remove", "processor": "ServicePathProcessor"}, "method": "POST", "params": null, "data": {"orgId": "${tableCell.row.otherOrgId}", "userId": "${tableCell.row.id}"}, "description": null}, "description": null, "id": "action-256", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "退出成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-265"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-270"}]}}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "style": "", "id": "", "visible": "${utils.FlyVueCore.PermissionService.hasPerm(`t.multiOrgM_.${selectOrgKindId}.manageMember`)}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "多维组织管理", "content": "创建多种类型多维组织，添加用户进入多维组织，多维度管理企业组织架构", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}}], "master": "tenant.org_admin.org_detail_layout", "meta": {"title": "组织列表", "name": "org_list", "packageName": "tenant.org_admin"}}