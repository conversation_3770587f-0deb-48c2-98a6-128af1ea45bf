{"type": "AdaptivePage", "version": "2.0", "variables": {"form": {"type": "object", "default": {"name": "", "title": "", "items": []}, "title": "form", "orderNo": 0}}, "functions": {"transformed": false, "script": "export function removeItem(items, index) {\r\n  items.splice(index, 1)\r\n  return items\r\n}\r\n\r\nexport function validateItems(rule, value, callback) {\r\n  const { items } = renderContext.variables.form\r\n  if (items.some(v => !v.title || !v.value)) {\r\n    callback(new Error('选择项名称或选择项编码不能为空'))\r\n    return\r\n  }\r\n  // if (items.some(v => !/^[a-zA-Z0-9_]+$/.test(v.value))) {\r\n  //   callback(new Error('选项编码仅支持输入大小写英文、下划线“_”、数字'))\r\n  //   return\r\n  // }\r\n  const ids = [...new Set(items.map(v => v.value))]\r\n  if (ids.length < items.length) {\r\n    callback(new Error('选择项编码不能重复'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\nexport function validateName(rule, value, callback) {\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  http.get(`/api/tenant/meta/dicts/${value}/check_unique`)\r\n    .then(res => {\r\n      if (!res.data) {\r\n        callback(new Error('字典编码重复，请重新输入'))\r\n      } else {\r\n        callback()\r\n      }\r\n    })\r\n}\r\n\r\nexport function reloadDists() {\r\n  utils.dictReload()\r\n}"}, "body": [{"type": "IvForm", "props": {"model": "${form}", "labelPosition": "right", "labelWidth": 100, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "字典名称", "span": "24", "width": "100%", "type": "text", "size": "default", "placeholder": "输入字典名称", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "required": true, "message": "此项为必填项"}]}, "disabled": false, "requiredName": "ivu-form-item-required", "value": "${form.title}", "maxlength": 32, "prop": "title"}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "字典编码", "span": "24", "width": "100%", "type": "text", "size": "default", "placeholder": "输入字典编码", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "required": true, "message": "此项为必填项"}, {"type": "regex", "value": "regex", "label": "正则校验", "trigger": "blur", "pattern": "^[a-zA-Z0-9_]+$", "message": "仅支持输入大小写英文、下划线“_”、数字"}, {"type": "method", "value": "method", "label": "自定义函数", "trigger": "blur", "method": "${validateName}"}]}, "disabled": false, "requiredName": "ivu-form-item-required", "value": "${form.name}", "prop": "name"}}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "选择项", "span": "24", "width": "100%", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"type": "required", "value": "required", "label": "必填", "message": "此项为必填项", "required": true}, {"type": "arraySize", "value": "arraySize", "label": "数组长度", "min": 1, "max": 800, "message": "至少添加一个选择项，最多支持800个选项"}, {"label": "自定义函数", "type": "method", "value": "method", "trigger": "blur", "method": "${validateItems}"}]}, "prop": "items", "requiredName": "ivu-form-item-required"}, "slots": {"default": {"children": [{"type": "IvFlex", "designer": {"movein": false, "moveout": false, "lock": false}, "style": "margin-bottom:16px", "children": [{"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "props": {"flex": "auto"}, "children": [{"type": "Input", "designer": {"movein": false}, "props": {"value": "${form.items[loopIndex].title}", "type": "text", "size": "default", "placeholder": "请输入选项名称", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "maxlength": 32}}]}, {"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "props": {"flex": "auto"}, "children": [{"type": "Input", "designer": {"movein": false}, "props": {"value": "${form.items[loopIndex].value}", "type": "text", "size": "default", "placeholder": "输入选择项编码", "border": true, "rows": 2, "wrap": "soft", "autocomplete": "off", "maxlength": 32}}]}, {"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "props": {"flex": "40"}, "children": [{"type": "IvButton", "props": {"type": "default", "size": "small", "replace": false, "target": "_self", "append": false, "shape": "circle", "icon": "md-trash"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "确定移除当前项吗？", "okText": "确定", "icon": ""}, "description": null, "id": "action-95"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "100", "name": "form.items", "value": "${removeItem(form.items,loopIndex)}"}]}, "description": null, "id": "action-99"}]}}}]}], "props": {"mode": "horizontal", "gutter": 16, "customCol": "4"}, "class": "", "id": "", "visible": true, "loop": {"data": "${form.items}", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvButton", "props": {"text": "添加选择项", "type": "dashed", "size": "default", "replace": false, "target": "_self", "append": false, "icon": "md-add"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "utils", "funcExp": "utils.arrayPush(form.items,{title:'',value:'',type:'string'})", "params": [], "description": null}, "description": null, "id": "action-56", "activeOn": null}]}, "click.stop": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "", "class": "", "id": "form1", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "header": [{"type": "IvText", "props": {"text": "创建数据字典", "showTitle": false, "maxline": 0}}], "footer": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "创建", "type": "primary", "size": "default", "long": false}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "description": null, "id": "action-80"}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/meta/dicts/{name}", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"name": "${form.name}"}, "data": "${form}", "description": null}, "description": null, "id": "action-82", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "创建成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-87"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "reloadDists", "params": []}, "description": null, "id": "action-104"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-89"}]}, "click.stop": {"actions": []}}}], "meta": {"title": "创建", "platform": "pc"}}