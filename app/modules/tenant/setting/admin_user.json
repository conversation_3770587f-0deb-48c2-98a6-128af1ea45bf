{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "queryTenantMembers", "title": "查询租户管理员列表", "description": "查询租户管理员", "multiple": true, "schema": {"id": {"type": "string", "title": "权限数据ID"}, "ownerId": {"type": "string", "title": "用户名称"}, "ownerType": {"type": "string", "title": "登录账号"}, "memberId": {"type": "string", "title": "成员Id"}, "role": {"type": "string", "title": "角色编码"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "userId": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "username": {"type": "string", "title": "登录账号"}, "tenantCode": {"type": "string", "title": "所属租户编码"}, "type": {"type": "string", "title": "用户类型"}}, "lazy": true, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/members", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": "${keyword}", "orderby": "updatedAt desc", "total": "${true}"}}], "variables": {"keyword": {"type": "string", "default": "", "title": "", "orderNo": 0}}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "", "props": {"title": "租户管理员", "padding": 16, "shadow": false, "bordered": true, "disHover": false, "target": "_self"}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false, "demo": {"props.data": "[{\"col1\":\"小明\",\"col2\":\"xiaoming@bingo\"},{\"col1\":\"小红\",\"col2\":\"xiaoh<PERSON>@bingo\"}]"}}, "props": {"data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "stripe": false, "border": false, "show-header": true, "loading": "${queryTenantMembers.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "visibleHeaderOp": true, "headerSearch": {"enable": false, "filters": [{"key": "keyword", "condition": "cn"}]}, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "ds": "${queryTenantMembers}", "loadOnMounted": true, "padding": 0}, "slots": {"headerExtra": {"children": [{"type": "Input", "props": {"type": "text", "size": "default", "placeholder": "根据用户名称搜索", "border": true, "value": "${keyword}", "search": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "header": {"children": [{"type": "IvButton", "props": {"type": "default", "size": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${queryTenantMembers}", "description": null}, "id": "action-144"}]}}}, {"type": "IvButton", "props": {"text": "添加管理员", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.setting.add_admin", "description": null}, "id": "action-567"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${queryTenantMembers}", "description": null}, "id": "action-88"}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "用户名称", "keyName": "name", "dataTimeType": "YYYY-MM-DD"}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "登录账号", "keyName": "username", "dataTimeType": "YYYY-MM-DD", "sortable": "true"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.username}", "showTitle": false, "maxline": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作", "keyName": "operations", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "移除", "type": "page", "linkColor": true, "disabled": "${utils.user.userId === tableCell.row.userId}", "replace": false}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${'即将移除应用管理员 '+ tableCell.row.name +'，移除后该用户将无租户管理权限！请确认是否继续？'}", "okText": "确定", "icon": "", "description": null}, "id": "action-59", "activeOn": null}, {"type": "Action.Http", "inputs": {"url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/members/{memberId}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"memberId": "${tableCell.row.memberId}"}}, "id": "action-183"}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-61"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${queryTenantMembers}", "description": null}, "id": "action-63"}]}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "auth": {"turnOn": true, "requiredPermission": ["t.memberI_"], "license": null}, "meta": {"title": "租户管理员", "name": "admin_user", "packageName": "tenant.setting"}}