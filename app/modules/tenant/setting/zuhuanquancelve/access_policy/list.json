{"type": "AdaptivePage", "version": "2.0", "style": "margin:16px;", "dataSources": [{"id": "accessPolicies", "title": "查询列表", "multiple": true, "schema": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "策略名称"}, "config": {"type": "object", "properties": {"properties": {"type": "object"}, "conditions": {"type": "array", "title": "动态条件"}, "relation": {"type": "string", "title": "运算关系"}, "expr": {"type": "string", "title": "运算表达式"}, "computeExpr": {"type": "string", "title": "运行时计算表达式"}, "enabledResolvePolicy": {}}}, "tenantId": {"type": "string", "title": "所属企业ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}, "lazy": true, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user_access_policies", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": null, "expand": null, "joins": null, "orderby": "updatedAt desc", "total": "${true}"}}], "body": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "支持根据企业用户使用应用的实际情况，按指定策略设置用户访问应用，例如，指定时间内用户可访问应用等，以保证企业应用访问安全。", "type": "info", "showIcon": true, "fade": true}}, {"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": true, "data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "visibleHeaderOp": true, "visibleMore": false, "padding": 0, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": "${accessPolicies.loading}", "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "autoReload": true, "ds": "${accessPolicies}"}, "slots": {"header": {"children": [{"type": "IvButton", "props": {"type": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${accessPolicies}"}, "description": null, "id": "action-88"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "创建访问策略", "type": "primary"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.setting.zuhuanquancelve.access_policy.create", "dialogSettings": {"width": "640"}, "description": null}, "description": null, "id": "action-55", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${accessPolicies}"}, "description": null, "id": "action-99"}]}, "click.stop": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "策略名称", "keyName": "name", "width": 100, "show": true}, "designer": {"movein": false}}, {"type": "IvTableGridColumn", "props": {"title": "处理", "keyName": "date", "show": true, "children": []}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.config.resolvePolicy.type === 'reject' ? '拒绝访问' : '二次身份验证'}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "操作", "width": 140, "keyName": "action", "show": true, "align": "left"}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "编辑", "linkColor": true, "disabled": false, "replace": false}, "class": "", "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.setting.zuhuanquancelve.access_policy.edit", "requestParams": [{"_uid": "145", "name": "policyId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "640"}, "description": null}, "description": null, "id": "action-144", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${accessPolicies}"}, "description": null, "id": "action-147"}]}, "click.stop": {"actions": []}}}, {"type": "IvLink", "props": {"tag": "a", "text": "删除", "linkColor": true, "disabled": false, "replace": false}, "class": "px-sm", "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确定删除访问策略 ${tableCell.row.name}？若有应用使用了当前策略，删除将会使应用使用当前策略失效。此操作不可恢复，请谨慎操作！！！`}", "okText": "删除", "icon": ""}, "description": null, "id": "action-187"}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user_access_policies/{id}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"id": "${tableCell.row.id}"}}, "description": null, "id": "action-189"}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "删除成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-195"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${accessPolicies}"}, "description": null, "id": "action-197"}]}, "click.stop": {"actions": []}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "master": "tenant.setting.zuhuanquancelve.zuhuyingyonganquanmoban", "auth": {"turnOn": true, "requiredPermission": ["acc_policy"], "license": null}, "meta": {"title": "列表", "platform": "pc"}}