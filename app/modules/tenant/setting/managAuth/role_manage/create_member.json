{"type": "AdaptivePage", "version": "2.0", "variables": {"type": {"type": "string", "default": "user", "title": "type", "orderNo": 0}, "copyXTenantId": {"type": "string", "default": "self", "title": "copyXTenantId", "orderNo": 1}, "modal1": {"type": "boolean", "default": false, "title": "对话框1", "orderNo": 2}, "xTenantId": {"type": "string", "default": "self", "title": "xTenantId", "orderNo": 3}, "search": {"type": "string", "default": "", "title": "search", "orderNo": 4}, "users": {"type": "array", "default": [], "title": "users", "orderNo": 5}, "orgs": {"type": "array", "default": [], "title": "orgs", "orderNo": 6}, "groups": {"type": "array", "default": [], "title": "groups", "orderNo": 7}, "usersDetail": {"type": "object", "title": "orgsDetail", "orderNo": 8}, "orgsDetail": {"type": "object", "title": "orgsDetail", "orderNo": 9}}, "dataSources": [{"id": "queryGroups", "title": "查询用户组，包括平台：includeSys=true", "multiple": true, "schema": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "ownerType": {"type": "string", "title": "所属对象类型"}, "ownerId": {"type": "string", "title": "所属对象ID"}, "system": {"type": "boolean", "title": "是否内置群组"}, "dynamic": {"type": "boolean", "title": "是否动态群组"}, "filters": {"type": "string", "title": "条件表达式"}, "rule": {"type": "object", "properties": {"conditions": {"type": "array", "title": "动态条件"}, "relation": {"type": "string", "title": "规则关系"}, "expr": {"type": "string", "title": "规则表达式"}}}, "description": {"type": "string", "title": "描述"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "tenantId": {"type": "string", "title": "所属企业ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/groups/data/sys_tenant", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": "1000", "offset": null, "select": null, "filters": null, "search": "${search}", "expand": null, "joins": null, "orderby": "updatedAt desc", "total": "${true}", "includeSys": "${true}", "excludeGroupId": null}, "headers": "${xTenantId === 'relative' ? { 'x-tenant-id': 'relative' } : null}"}], "functions": {"transformed": false, "script": "export function buildData() {\r\n  const { usersDetail, orgsDetail, orgs, groups } = renderContext.variables\r\n  let data = []\r\n  Object.keys(usersDetail).forEach(key => {\r\n    data.push({\r\n      memberId: key,\r\n      memberType: 'U',\r\n      name: usersDetail[key].name\r\n    })\r\n  })\r\n  Object.keys(orgsDetail).forEach(key => {\r\n    data.push({\r\n      memberId: key,\r\n      memberType: 'O',\r\n      name: orgsDetail[key].name\r\n    })\r\n  })\r\n  groups.value.forEach(item => {\r\n    data.push({\r\n      memberId: item,\r\n      memberType: 'G',\r\n      name: renderContext.dataSources.queryGroups.data.find(v => v.id === item).name\r\n    })\r\n  })\r\n  //处理v-if导致orgs有值但是orgsDetail无值的情况\r\n  if (orgs.value.length > 0 && Object.keys(orgsDetail).length === 0) {\r\n    data.push(...request.params.members.filter(v => v.memberType === 'O'))\r\n  }\r\n  console.log(data,\"data\")\r\n  return data\r\n}"}, "events": {"on-rendered": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "82", "name": "users", "value": "${request.params.members.filter(v=> v.memberType === 'U').map(v=> v.memberId)}"}, {"_uid": "83", "name": "orgs", "value": "${request.params.members.filter(v=> v.memberType === 'O').map(v=> v.memberId)}"}, {"_uid": "84", "name": "groups", "value": "${request.params.members?.filter(v=> v.memberType === 'G').map(v=> v.memberId)}"}], "description": null}, "description": null, "id": "action-81", "activeOn": "${request.params.members}"}]}, "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "IvModal", "designer": {"movein": false, "visible": false}, "props": {"value": "${modal1}", "title": "对话框1", "width": "550", "closable": true, "okText": "确定", "cancelText": "取消", "mask": true, "maskClosable": false, "loading": false, "scrollable": false, "draggable": false, "sticky": true, "stickyDistance": 10, "zIndex": 1000, "transfer": true, "lockScroll": false}, "events": {"on-ok": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "85", "name": "xTenantId", "value": "${copyXTenantId}"}]}, "description": null, "id": "action-84"}]}, "on-cancel": {"actions": []}, "on-visible-change": {"actions": []}}, "slots": {"default": {"children": [{"type": "IvForm", "props": {"model": "${{}}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": false, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormRadio", "designer": {"movein": false}, "props": {"labelName": "用户组", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "data": [{"label": "查看本企业用户组", "value": "self"}, {"label": "查看全部用户组", "value": "relative"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "value": "${copyXTenantId}"}}], "designer": {"visible": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvForm", "props": {"model": "${{}}", "rowspace": 8, "labelPosition": "left", "labelWidth": 120, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormRadio", "designer": {"movein": false}, "props": {"labelName": "添加成员类型", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "data": [{"label": "用户", "value": "user"}, {"label": "组织", "value": "org"}, {"value": "group", "label": "用户组"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "value": "${type}"}, "class": " px-sm"}], "class": ""}, {"type": "IamMemberSelect", "props": {"type": "raw", "multiple": true, "disabled": false, "modalTitle": "选择用户", "placeholder": "请选择用户", "modalWidth": 650, "switchOrgKind": true, "switchTenantRange": true, "value": "${users}"}, "style": "", "class": "", "id": "", "visible": "${type === 'user'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"on-init": {"actions": []}, "on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "273", "name": "usersDetail", "value": "${$event.selectedUsers}"}]}, "description": null, "id": "action-272"}]}}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IamOrgSelect", "props": {"type": "raw", "multiple": true, "disabled": false, "canSelectDisabledIdChild": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "placeholder": "请选择组织", "replaceFilters": false, "switchOrgKind": true, "switchTenantRange": true, "value": "${orgs}"}, "style": "", "class": "", "id": "", "visible": "${type === 'org'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "276", "name": "orgsDetail", "value": "${$event.selectedOrgs}"}]}, "description": null, "id": "action-275"}]}}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvForm", "props": {"model": "${{}}", "gutter": "0", "labelPosition": "right", "labelWidth": 100, "showMessage": true, "hideRequiredMark": false, "labelColon": false, "disabled": false}, "children": [{"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "选择用户组", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true}, "class": " pr-md", "slots": {"default": {"children": [{"type": "IvFlex", "designer": {"movein": false, "moveout": false}, "style": "", "children": [{"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "children": [{"type": "IvSelect", "props": {"transfer": true, "placeholder": "选择用户组，可搜索", "notFoundText": "无匹配数据", "data": "${queryGroups.data}", "keyNames": {"label": "name", "value": "id"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "multiple": true, "filterable": true, "value": "${groups}"}, "events": {"on-change": {"actions": []}, "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "543", "name": "search", "value": "${$event}"}]}, "description": null, "id": "action-542"}]}, "on-create": {"actions": []}, "on-select": {"actions": []}}}], "props": {"flex": "auto"}}, {"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "children": [{"type": "Icon", "props": {"type": "ios-funnel", "size": 16}, "style": "cursor:pointer;", "events": {"click.stop": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "137", "name": "modal1", "value": "${true}"}]}, "description": null, "id": "action-136"}]}}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": ["par_child_ten"]}}]}], "props": {"mode": "horizontal", "gutter": 12, "align": "center"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "", "class": "", "id": "", "visible": "${type === 'group'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "header": [{"type": "IvText", "props": {"text": "成员管理", "showTitle": false, "maxline": 0}}], "footer": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "确定", "type": "primary", "size": "default", "long": false}, "events": {"click": {"actions": [{"type": "Action.Output", "inputs": {"output": "${buildData()}"}, "description": null, "id": "action-67"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-70"}]}, "click.stop": {"actions": []}}}], "meta": {"title": "添加成员", "platform": "pc"}}