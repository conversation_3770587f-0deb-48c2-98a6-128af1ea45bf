{"type": "AdaptivePage", "version": "2.0", "variables": {"isTenantEnv": {"type": "string", "default": "${window.location.hash.includes('/tenant')}", "title": "isTenantEnv", "orderNo": 0}, "userOptions": {"type": "object", "default": {"page": 1, "size": 5, "search": ""}, "title": "userOptions", "orderNo": 1}, "orgOptions": {"type": "object", "default": {"page": 1, "size": 5, "search": ""}, "title": "orgOptions", "orderNo": 2}, "groupOptions": {"type": "object", "default": {"page": 1, "size": 5, "search": ""}, "title": "groupOptions", "orderNo": 3}, "currentRoleId": {"type": "string", "default": "${request.params.roleId}", "source": "request", "optionSet": null, "entity": null, "title": "currentRoleId", "orderNo": 4}}, "dataSources": [{"id": "queryRoleUser", "title": "查询角色用户成员", "description": "查询角色用户成员", "multiple": true, "schema": {"id": {"type": "string"}, "roleId": {"type": "string"}, "userId": {"type": "string"}, "appId": {"type": "string"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}, "properties": {"type": "object"}}, "fetchWhen": "${request.params.roleId}", "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/apps/{appId}/roles/{roleId}/users", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"appId": "${utils.iamClientInfo.appId}", "roleId": "${currentRoleId}", "page": "${userOptions.page}", "size": "${userOptions.size}", "limit": null, "offset": null, "select": null, "filters": null, "search": "${userOptions.search}", "expand": null, "joins": null, "orderby": "updatedAt desc", "total": "${true}"}}, {"id": "queryRoleGroup", "title": "查询角色群组成员", "description": "查询角色群组成员", "multiple": true, "schema": {"roleId": {"type": "string"}, "groupId": {"type": "string"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "properties": {"type": "object"}}, "fetchWhen": "${request.params.roleId}", "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/apps/roles/{roleId}/groups", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"roleId": "${currentRoleId}", "page": "${groupOptions.page}", "size": "${groupOptions.size}", "limit": null, "offset": null, "select": null, "filters": null, "search": "${groupOptions.search}", "expand": null, "joins": null, "orderby": "", "total": "${true}"}}, {"id": "queryRoleOrg", "title": "查询角色组织成员", "description": "查询角色组织成员", "multiple": true, "schema": {"roleId": {"type": "string"}, "orgId": {"type": "string"}, "tenantId": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "properties": {"type": "object"}}, "fetchWhen": "${request.params.roleId}", "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/apps/roles/{roleId}/orgs", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"roleId": "${request.params.roleId}", "page": "${orgOptions.page}", "size": "${orgOptions.size}", "limit": null, "offset": null, "select": null, "filters": null, "search": "${orgOptions.search}", "expand": null, "joins": null, "orderby": "", "total": "${true}"}}, {"id": "roleInfo", "multiple": false, "fetchWhen": "${request.params.roleId}", "lazy": false, "autoReload": true, "url": "/api/tenant/apps/${utils.iamClientInfo.appId}/roles/${request.params.roleId}", "method": "GET"}], "events": {"on-render": {"actions": []}, "on-rendered": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "Row", "designer": {"movein": false, "moveout": false, "lock": false}, "style": "margin-bottom:16px;margin-top:12px;", "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "Card", "designer": {"movein": false}, "style": "margin-bottom:16px;width:100%;", "props": {"title": "用户", "padding": "16", "shadow": false, "bordered": true, "disHover": false, "replace": false, "target": "_self", "append": false}, "slots": {"default": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designer": {"lock": false}, "style": "height: 230px;position: relative;display: flex;flex-direction: column;", "children": [{"type": "IvText", "props": {"text": "暂无用户", "showTitle": false, "maxline": 0}, "style": "text-align:center;display:block;color:gray;margin-top:24px;", "class": "", "id": "", "visible": "${!queryRoleUser.loading && !queryRoleUser.data.length}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvSpin", "props": {"type": "style1", "text": "加载中...", "fix": true}, "style": "", "class": "", "id": "", "visible": "${queryRoleUser.loading}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designer": {"lock": false}, "style": "flex: 1", "children": [{"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "lock": false}, "props": {"title": "${loopItem.name}", "disabled": false, "replace": false, "target": "_self", "append": false, "name": "${loopItem.userId}"}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "${queryRoleUser.data}", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"on-click": {"actions": []}}, "slots": {"title": {"children": [{"type": "IvTooltip", "designer": {"movein": false, "lock": false}, "props": {"title": "${loopItem.name}", "content": "${`${loopItem.username}@${loopItem.tenantCode}(${loopItem.fullOrgName})`}", "placement": "top", "delay": 0, "theme": "dark", "transfer": true, "maxWidth": 200}}, {"type": "Icon", "props": {"type": "md-information-circle-outline", "size": 16, "color": "rgba(255,153,0,1)"}, "style": "", "class": "", "id": "", "visible": "${loopItem.restricted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "Icon", "props": {"type": "md-trash", "size": 16}, "events": {"click.stop": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${'是否确认移除成员  '+ loopItem.name}", "okText": "确定", "icon": "", "description": null}, "id": "action-680", "activeOn": null}, {"type": "Action.Http", "inputs": {"url": {"type": "ServicePath", "source": "local", "value": "/api/apps/{appId}/roles/{roleId}/users/{userId}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"appId": "${utils.iamClientInfo.appId}", "roleId": "${currentRoleId}", "userId": "${loopItem.userId}"}, "description": null}, "id": "action-682", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${queryRoleUser}", "description": null}, "id": "action-943"}]}}, "style": "", "class": "", "id": "", "visible": false, "loop": {"data": "", "variableName": "loopItem2", "indexName": "loopIndex2", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "text-align:right;", "children": [{"type": "Page", "props": {"current": "${userOptions.page}", "total": "${queryRoleUser.total}", "pageSize": "${userOptions.size}", "showTotal": true, "showElevator": false, "showSizer": false, "pageSizeOpts": "${[10,20,30,40]}", "simple": false, "value": "${userOptions.page}", "size": "small"}, "events": {"on-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "215", "name": "userOptions.page", "value": "${$event}"}], "description": null}, "description": null, "id": "action-214", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}]}]}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "IamSearchInput", "style": "height:100%;", "props": {"value": "${userOptions.search}", "placeholder": "请输入关键字搜索"}}, {"type": "IvButton", "props": {"type": "text", "size": "default", "icon": "md-add", "disabled": "${currentRoleType === 'T'}"}, "style": "cursor:pointer;", "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "app.user_authorization.add_role_user", "requestParams": [{"_uid": "316", "name": "roleId", "value": "${request.params.roleId}"}, {"_uid": "294", "name": "appId", "value": "${utils.iamClientInfo.appId}"}], "dialogSettings": {"width": "650", "class-name": "ivu-modal-body-padding-0"}, "description": null}, "id": "action-315", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${queryRoleUser}", "description": null}, "id": "action-1019"}]}}, "class": "", "id": "", "visible": "${roleInfo.data.ownerType === 'A'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}], "props": {"type": "flex", "customCol": "24", "gutter": 16, "wrap": true}, "class": "", "id": "", "visible": "${roleInfo.loaded && roleInfo.data.ownerType !== 'O_K'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "LcdpInlineRenderer", "props": {"forceHideHeader": false, "forceHideFooter": false, "noCache": false, "uri": "tenant.setting.managAuth.org_role_member", "requestParams": [{"_uid": "77", "name": "roleId", "value": "${request.params.roleId}"}]}, "style": "", "class": "", "id": "", "visible": "${roleInfo.loaded && roleInfo.data.ownerType === 'O_K'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "meta": {"title": "角色成员内容"}}