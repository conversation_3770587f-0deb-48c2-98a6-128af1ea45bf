{"content": {"cells": [{"position": {"x": 30, "y": 45}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.Start", "nodeType": "Action.Start", "label": "开始", "data": {}, "component": "action-start", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "start", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 160}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Confirm", "nodeType": "Action.Confirm", "label": "弹出确认框", "data": {"type": "primary", "title": "提示", "content": "确定发送ACM吗？", "okText": "确定", "icon": "", "description": null}, "component": "action-confirm", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-102", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 275}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Http", "nodeType": "Action.Http", "label": "发送HTTP请求", "data": {"url": "/acm/{id}", "method": "GET", "params": {"id": "${tableCell.row.requestId}"}, "resultName": "res"}, "component": "action-http", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-91", "_order": 0, "zIndex": 1, "_validateError": false, "_selected": false}, {"position": {"x": 30, "y": 390}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.End", "nodeType": "Action.End", "label": "结束", "data": {}, "component": "action-end", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "end", "_order": 0, "zIndex": 1, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "299859c9-4933-43a6-adcd-ee737b91705f", "zIndex": 1, "source": {"cell": "start", "port": "bottom"}, "target": {"cell": "action-102", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "4779e09c-e718-43aa-b136-36b3b0f716d9", "zIndex": 1, "source": {"cell": "action-102", "port": "bottom"}, "target": {"cell": "action-91", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "20fe1e35-36d1-4456-aa87-e6b6ce0b8c59", "zIndex": 1, "source": {"cell": "action-91", "port": "bottom"}, "target": {"cell": "2985c3ef-b1be-45d7-8c98-bdce57699c0d", "port": "top"}}, {"position": {"x": 231, "y": 359}, "size": {"width": 51, "height": 51}, "view": "vue-shape-view", "shape": "Action.Switch", "nodeType": "Action.Switch", "label": "分支", "data": {}, "component": "action-switch", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "2985c3ef-b1be-45d7-8c98-bdce57699c0d", "zIndex": 2, "_validateError": false}, {"position": {"x": 95, "y": 415}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Message", "nodeType": "Action.Message", "label": "顶部消息提示", "data": {"notifyType": "info", "content": "发送ACM成功", "duration": 1.5, "closable": false, "background": false}, "component": "action-message", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "9d8b9a77-d3a4-4e7a-9fb4-c4cf93a40203", "zIndex": 3, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "7fc4dc52-72b5-4fa9-956e-753be95d1f17", "zIndex": 4, "data": {"condition": "${res.code==0}"}, "source": {"cell": "2985c3ef-b1be-45d7-8c98-bdce57699c0d", "port": "left"}, "target": {"cell": "9d8b9a77-d3a4-4e7a-9fb4-c4cf93a40203", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "fb3a1cd2-decc-40e4-94ca-4ac38186ab84", "zIndex": 5, "source": {"cell": "9d8b9a77-d3a4-4e7a-9fb4-c4cf93a40203", "port": "bottom"}, "target": {"cell": "d41585bf-b57f-4440-b6aa-ebf46ee501dd", "port": "top"}}, {"position": {"x": 310, "y": 440}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Message", "nodeType": "Action.Message", "label": "顶部消息提示", "data": {"notifyType": "info", "content": "${`发送ACM失败:原因：${res.message}`}", "duration": 5, "closable": false, "background": false}, "component": "action-message", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "1f6f46aa-c83c-4dbb-a079-08d0e09ada17", "zIndex": 6, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "c39e9fca-2c9c-48dd-9ebe-c82a248816fd", "zIndex": 7, "data": {"condition": "${res.code==1}"}, "source": {"cell": "2985c3ef-b1be-45d7-8c98-bdce57699c0d", "port": "right"}, "target": {"cell": "1f6f46aa-c83c-4dbb-a079-08d0e09ada17", "port": "top"}}, {"position": {"x": 95, "y": 544}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.DataSourceDataReload", "nodeType": "Action.DataSourceDataReload", "label": "单数据源刷新", "data": {"dataSource": "${logList}"}, "component": "action-data-source-data-reload", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "d41585bf-b57f-4440-b6aa-ebf46ee501dd", "zIndex": 8, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "e63d5449-a130-4133-9b0a-9a5fbfac2639", "source": {"cell": "1f6f46aa-c83c-4dbb-a079-08d0e09ada17", "port": "bottom"}, "target": {"cell": "d41585bf-b57f-4440-b6aa-ebf46ee501dd", "port": "top"}, "zIndex": 9}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "f894b504-4935-40aa-b8df-3a69d202cc03", "source": {"cell": "d41585bf-b57f-4440-b6aa-ebf46ee501dd", "port": "bottom"}, "target": {"cell": "end", "port": "top"}, "zIndex": 10}]}, "meta": {"title": ""}}