{"content": {"cells": [{"position": {"x": 30, "y": 45}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.Start", "nodeType": "Action.Start", "label": "开始", "data": {}, "component": "action-start", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "start", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 160}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Http", "nodeType": "Action.Http", "label": "发送HTTP请求", "data": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/app/clientsDataAuth/interface/authz", "processor": "ServicePathProcessor"}, "method": "POST", "data": "${interForm}"}, "component": "action-http", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-54", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 275}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.CloseDialog", "nodeType": "Action.CloseDialog", "label": "关闭对话框", "data": {}, "component": "action-close-dialog", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-88", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 390}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Message", "nodeType": "Action.Message", "label": "顶部消息提示", "data": {"notifyType": "success", "content": "已添加", "duration": 1.5, "closable": false, "background": false}, "component": "action-message", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-96", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 505}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.End", "nodeType": "Action.End", "label": "结束", "data": {}, "component": "action-end", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "end", "_order": 0, "zIndex": 1, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "333e47c7-0d3c-406f-bd15-276f37451e0d", "zIndex": 1, "source": {"cell": "action-54", "port": "bottom"}, "target": {"cell": "action-88", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "90a74905-9f2e-4138-84c7-5d246e6091cb", "zIndex": 1, "source": {"cell": "action-88", "port": "bottom"}, "target": {"cell": "action-96", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "02d4e098-506c-4ce3-b09a-9a4e4a30edb0", "zIndex": 1, "source": {"cell": "action-96", "port": "bottom"}, "target": {"cell": "end", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "ad226a35-d8c7-42ea-9a6f-199c3fc49155", "zIndex": 1, "source": {"cell": "start", "port": "bottom"}, "target": {"cell": "bd02ffb7-50f0-413b-9a13-ca9d3b0d7112", "port": "top"}}, {"position": {"x": 277, "y": 101}, "size": {"width": 51, "height": 51}, "view": "vue-shape-view", "shape": "Action.Switch", "nodeType": "Action.Switch", "label": "分支", "data": {}, "component": "action-switch", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "9ffc8077-bcc8-4d79-9cb1-68f3b1ee3da4", "zIndex": 2, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "2d0c7ead-e70a-4adb-9ece-dc7d2c2eb3f4", "zIndex": 3, "data": {"condition": "${ifExist == false}"}, "source": {"cell": "9ffc8077-bcc8-4d79-9cb1-68f3b1ee3da4", "port": "left"}, "target": {"cell": "action-54", "port": "top"}}, {"position": {"x": 352, "y": 212}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Confirm", "nodeType": "Action.Confirm", "label": "弹出确认框", "data": {"type": "primary", "title": "提示", "content": "检测到选择的部门已存在权限，若要修改，请到编辑页面进行修改", "okText": "确定", "icon": ""}, "component": "action-confirm", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "ce23d19f-6d68-495e-b520-b839cb95b526", "zIndex": 4, "_validateError": false, "_selected": true}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "a0c049f1-0dfd-4ee9-83a2-ffe15725a841", "zIndex": 5, "data": {"condition": "${ifExist == true}"}, "source": {"cell": "9ffc8077-bcc8-4d79-9cb1-68f3b1ee3da4", "port": "right"}, "target": {"cell": "ce23d19f-6d68-495e-b520-b839cb95b526", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "da938680-2a17-4a48-80dd-4cd96279dc07", "zIndex": 6, "source": {"cell": "ce23d19f-6d68-495e-b520-b839cb95b526", "port": "bottom"}, "target": {"cell": "end", "port": "right"}}, {"position": {"x": 638, "y": 27.5}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.ValidateForm", "nodeType": "Action.ValidateForm", "label": "校验表单", "data": {"formId": "form"}, "component": "action-validate-form", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "bd02ffb7-50f0-413b-9a13-ca9d3b0d7112", "zIndex": 7, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "60b28e54-2f2a-4e4d-b719-8aad7ea85cc4", "zIndex": 8, "source": {"cell": "bd02ffb7-50f0-413b-9a13-ca9d3b0d7112", "port": "bottom"}, "target": {"cell": "9ffc8077-bcc8-4d79-9cb1-68f3b1ee3da4", "port": "top"}}]}, "meta": {"title": ""}}