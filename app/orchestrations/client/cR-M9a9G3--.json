{"content": {"cells": [{"position": {"x": 30, "y": 45}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.Start", "nodeType": "Action.Start", "label": "开始", "data": {}, "component": "action-start", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "start", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 160}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.SetPageVariable", "nodeType": "Action.SetPageVariable", "label": "设置页面变量", "data": {"variables": [{"_uid": "89", "name": "formData.sysType", "value": "${'3'}"}, {"_uid": "69", "name": "formData.groupType", "value": "${'1'}"}], "description": null}, "component": "action-set-page-variable", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "action-88", "_order": 0, "zIndex": 1, "_validateError": false, "_selected": false}, {"position": {"x": 50, "y": 570}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.End", "nodeType": "Action.End", "label": "结束", "data": {}, "component": "action-end", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "end", "_order": 0, "zIndex": 1, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "51df263c-0f47-467f-9d1a-1ebc261a1e15", "zIndex": 1, "source": {"cell": "action-88", "port": "bottom"}, "target": {"cell": "end", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "47e9ee46-f92b-4135-8298-c8b9b9d297c0", "zIndex": 1, "source": {"cell": "start", "port": "bottom"}, "target": {"cell": "5140018a-159e-49e1-b405-ecdb18aca41a", "port": "top"}}, {"position": {"x": 180, "y": 150}, "size": {"width": 51, "height": 51}, "view": "vue-shape-view", "shape": "Action.Switch", "nodeType": "Action.Switch", "label": "分支", "data": {}, "component": "action-switch", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "5140018a-159e-49e1-b405-ecdb18aca41a", "zIndex": 2, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "239ee80f-9a3e-4d10-a53a-daa83730654c", "zIndex": 3, "data": {"condition": "${type == 'add'}"}, "source": {"cell": "5140018a-159e-49e1-b405-ecdb18aca41a", "port": "left"}, "target": {"cell": "action-88", "port": "top"}}, {"position": {"x": 276, "y": 185}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.Http", "nodeType": "Action.Http", "label": "发送HTTP请求", "data": {"resultName": "response", "schema": {"properties": {"type": "object"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectStr": {"type": "string", "title": "操作对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "newIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "oldIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "iamUsersMoveVo": {"type": "object", "properties": {"userOldOANames": {"type": "string"}, "userNewOANames": {"type": "string"}, "orgId": {"type": "string"}, "userNames": {"type": "array"}, "addUserIds": {"type": "array"}, "itType": {"type": "string"}, "itUrl": {"type": "string"}, "acmFlag": {"type": "integer"}}}, "newIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "oldIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "map": {"type": "object"}, "newStr": {"type": "array"}, "oldStr": {"type": "array"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getDetail", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${requestId}"}}, "component": "action-http", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "5fe0590e-04e5-49b0-bafd-176d68532c9d", "zIndex": 4, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "9cf60d8d-fbde-42b8-ada7-13f477ab79ba", "zIndex": 5, "data": {"condition": "${type == 'edit'}"}, "source": {"cell": "5140018a-159e-49e1-b405-ecdb18aca41a", "port": "right"}, "target": {"cell": "5fe0590e-04e5-49b0-bafd-176d68532c9d", "port": "top"}}, {"position": {"x": 277, "y": 291}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.SetPageVariable", "nodeType": "Action.SetPageVariable", "label": "设置页面变量", "data": {"variables": [{"_uid": "92", "name": "formData", "value": "${response.newIamOrg}"}, {"_uid": "107", "name": "formData.acmFlag", "value": "${response.acmFlag}"}]}, "component": "action-set-page-variable", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "269239b1-1c8a-43f6-9a50-0b8bc3744a26", "zIndex": 6, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "3513b07b-39c6-4efd-aee2-e5ce4f62045d", "zIndex": 7, "source": {"cell": "5fe0590e-04e5-49b0-bafd-176d68532c9d", "port": "bottom"}, "target": {"cell": "269239b1-1c8a-43f6-9a50-0b8bc3744a26", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "3b8e6d21-b841-4b02-abc5-d08dce014660", "zIndex": 8, "source": {"cell": "269239b1-1c8a-43f6-9a50-0b8bc3744a26", "port": "bottom"}, "target": {"cell": "949b4352-6964-498d-b04d-3c3d8d57b374", "port": "top"}}, {"position": {"x": 260, "y": 610}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action<PERSON>", "nodeType": "Action<PERSON>", "label": "执行函数", "data": {"funcType": "page", "script": "consolelog"}, "component": "action-script", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "949b4352-6964-498d-b04d-3c3d8d57b374", "zIndex": 9, "_validateError": false, "_selected": true}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "7ccd0105-a1e9-4f0d-9a8e-363b69f1cb90", "zIndex": 10, "source": {"cell": "949b4352-6964-498d-b04d-3c3d8d57b374", "port": "left"}, "target": {"cell": "end", "port": "right"}}]}, "meta": {"title": ""}}