{"content": {"nodes": [{"id": "start", "shape": "Action.Start", "nodeType": "Action.Start", "data": {}}, {"id": "action-100", "shape": "Action.DataSourceDataReload", "nodeType": "Action.DataSourceDataReload", "data": {"dataSource": "${userList}"}}, {"id": "end", "shape": "Action.End", "nodeType": "Action.End", "data": {}}], "edges": [{"source": {"cell": "start", "port": "bottom"}, "target": {"cell": "action-100", "port": "top"}}, {"source": {"cell": "action-100", "port": "bottom"}, "target": {"cell": "end", "port": "top"}}]}, "meta": {}}