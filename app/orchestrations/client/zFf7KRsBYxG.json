{"content": {"cells": [{"position": {"x": 30, "y": 45}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.Start", "nodeType": "Action.Start", "label": "开始", "data": {}, "component": "action-start", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "start", "_order": 0, "zIndex": 1, "_validateError": false}, {"position": {"x": 30, "y": 275}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.End", "nodeType": "Action.End", "label": "结束", "data": {}, "component": "action-end", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "end", "_order": 0, "zIndex": 1, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "85658b72-7c52-472f-a9ac-1cc3e77c1797", "zIndex": 1, "source": {"cell": "start", "port": "bottom"}, "target": {"cell": "3c14d2a3-841c-4493-b45e-6d43c24c3215", "port": "top"}}, {"position": {"x": 158, "y": 101}, "size": {"width": 51, "height": 51}, "view": "vue-shape-view", "shape": "Action.Switch", "nodeType": "Action.Switch", "label": "分支", "data": {}, "component": "action-switch", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "3c14d2a3-841c-4493-b45e-6d43c24c3215", "zIndex": 2, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#4d90ff", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "3c57b04d-9091-4132-a69e-a4f61acc41fc", "zIndex": 3, "data": {"condition": "${(staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==4||staffTypeChoose==5||staffTypeChoose==7)&&orgName&&orgName!=''&&form.name&&form.name!=''}"}, "source": {"cell": "3c14d2a3-841c-4493-b45e-6d43c24c3215", "port": "left"}, "target": {"cell": "8af4cbde-cf8d-48a9-b1cd-5143987d6145", "port": "top"}, "tools": {"items": [{"name": "button-remove", "args": {"distance": "-40"}}], "name": null}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "950a731e-1dc2-437c-9389-d3e9695a831e", "zIndex": 4, "data": {"condition": "${!(staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==4||staffTypeChoose==5||staffTypeChoose==7)}"}, "source": {"cell": "3c14d2a3-841c-4493-b45e-6d43c24c3215", "port": "right"}, "target": {"cell": "end", "port": "right"}}, {"position": {"x": 305, "y": 155}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.SetPageVariable", "nodeType": "Action.SetPageVariable", "label": "设置页面变量", "data": {"variables": [{"_uid": "235", "name": "form.adShowName", "value": "${form.name+\"(\"+orgName+\")\"}"}]}, "component": "action-set-page-variable", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "8af4cbde-cf8d-48a9-b1cd-5143987d6145", "zIndex": 7, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "6bf26831-82d5-43ae-9f73-9b84877a6cda", "zIndex": 8, "source": {"cell": "8af4cbde-cf8d-48a9-b1cd-5143987d6145", "port": "bottom"}, "target": {"cell": "end", "port": "right"}}]}, "meta": {"title": ""}}