server:
  port: 9000
  servlet:
    context-path: /iamsso
spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************
    username: portal
    password: Tdsql_test_2023!
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: IAMSSOClusteredScheduler   # 集群名
            instanceId: AUTO
          jobStore:
            isClustered: false     # 集群模式开关
  redis:
    host: *************
    port: 6379
    timeout: 3000
    password: iamdev
    database: 6
    lettuce:
      cluster:
        refresh:
          adaptive: true
          period: 20s
      shutdown-timeout: 10000ms  # 关闭超时时间
      pool:
        max-wait: 10
        max-active: 50
        max-idle: 30
        min-idle: 5

# COM.ALIBABA.NACOS.DISCOVERY setting
spring.application.name: iamsso
nacos:
  discovery:
    enabled: false
    auto-register: true
    server-addr: dfuse-dev.bingosoft.net:31048
    username: cse-app
    namespace: cse-app
    password: cse-app

FRONT_CHANNEL_LOGOUT_URI: ${FRONT_CHANNEL_LOGOUT_URI:http://iamdev.gf.com.cn/logout}

#LtpaToken2相关秘钥
sso.ltpa:
  # 启用ltpa认证
  enabled: true
  # 3DES加密key
  desSharedKey: ${LTPA_DES_SHARED_KEY:2VFhlhPEPNALkOaUa4XZmiPfa/s0TJFe69SMYWguqfs=}
  # 管理密码
  password: ${LTPA_PWD:123456}
  # rsa签名key
  priRsaSharedKey: ${LTPA_PRI_RSA_SHARED_KEY:rP+2Ue6G+sB2js6KlXQlYvL/qcKBYM01QXgIetgJVk1EMK+xKZVU+QKzxrJi8P0EipishGkYC4Dp4uhHUDxi/Jcj2aSRScR5apNEwplxp0HuGzCuBfsVukkWiZBG/BrJ9PjYqa4odith+8gnxmGiHMKYSq89G4d3uChP3s03YOyKyiH4Ba9Quw==}
  pubRsaSharedKey: ${LTPA_PUB_RSA_SHARED_KEY:AMT7AmIa/tZsoXvbcHKfqQl2JkeH+tGgO+mmFSEj9ltNjE+hFF4oYcziwEOj0cRucS2o+a762NPK+AnKYJhw+mCORT/BONqg+vTIU49ufq06BNPSbQYJTAXEYIIm4jsA3TbLLwPXLVZTyEOlSVjXtJ1iw+4f7BPIgxfijaoxT/LTAQAB}
  # ltpa token生成的用户属性格式，el表达式，其中user为用户上下文
  userValExpr:  ${LTPA_USER_VAL_EXPR:"user:tldap.gf.com.cn:389/".concat(#user.dn)}
  #token过期时间：时
  tokenExpirationTime: ${LTPA_TOKEN_EXPIRATION_HOURS:10}
  cookie:
    path: /
    timeout: PT1H
    name: LtpaToken2
    domain: ${LTPA_DOMAIN:iamdev.gf.com.cn}


sso.authc.ldap:
  enabled: false
  # ldap连接服务地址
  url: ${LDAP_URL:ldap://***********:389}
  # 连接的管理员账号密码
  username: ${LDAP_USER_NAME:<EMAIL>}
  password: ${LDAP_PASSWORD:Gfte5tHw2022!}
  userSearchBase: ${LDAP_USER_SEARCH_BASE:ou=广发证券股份有限公司,dc=gfdev,dc=com}
  userSearchFilter: ${LDAP_USER_SEARCH_FILTER:sAMAccountName={0}}
host:
  portal: http://tam.gf.com.cn # tam环境
springdoc:
  api-docs:
    enabled: true
