package net.bingosoft.fuse.iam.sso.utils;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA加解密工具类
 */
@Component
public class RSAUtil {
    private static final Logger log = LoggerFactory.getLogger(RSAUtil.class);

    private static final String ALGORITHM = "RSA";
    private static final String TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final int MAX_ENCRYPT_BLOCK = 117; // RSA最大加密明文大小
    private static final int MAX_DECRYPT_BLOCK = 128; // RSA最大解密密文大小

    private static final String privateKeyStr;
    private static final String publicKeyStr;
    static {
        publicKeyStr =
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDiNOeJAlmKmVq5eJ7wkmvic0zKaAFpiJ51iuq4zK2us+krbPIxU7dwTan7ycwOsED+xQ9283P0x/ueo5EgJzU4XRXcxcUkMMZz/***************************CP0tuxtLbsyqf5HjmxVe9lNf9Vmu6UbMkMYojnUP5oqEAQIDAQAB";
        privateKeyStr  =
                "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOI054kCWYqZWrl4nvCSa+JzTMpoAWmInnWK6rjMra6z6Sts8jFTt3BNqfvJzA6wQP7FD3bzc/TH+56jkSAnNThdFdzFxSQwxnP/eN9djWbrdRMkaxevdqISkOqxXcMI/S27G0tuzKp/keObFV72U1/1Wa7pRsyQxiiOdQ/mioQBAgMBAAECgYEAz1/y118JGY/nZVsYUrHZJ7vOLUzV5prxsg6NiqdRnnSfEUmKawA1jkYUzJi+csTQgP/FYqZjqodfvJp1JEXteao6UeDXC/Si2WSB0g7WjaNr9S7MhVutSEg3XZXR8ufDDwWvNSe7ynwdwQZdnkEX6tg26QyWtsZVnEeVoIE0UrECQQD2g4J9z8orKmLqJrvUZFM0xGvalKHr/hs0kuHwXbNtKBLvdBt9uSR+czwQqC8B2otYNq2B5Ou9J32BTVjXjRydAkEA6ulX0U4XH2XD+NTbZDULPAl+xtU6WlAe6VhLQwCyb8DhCAmro/vYOS8QCO4pna9ZZFGIlnZSD2uLCLc1ok2dtQJAF3ADfSC/cQEzrF2IaDYtrwup0XtmNcDQJ1x7vNW71HoF6txYLMjojlk4ccD7zypPK1DviDUSsMZLaXrIIp5ZBQJBANg3GCZFKxEyGLOSL7DHBeUnlDGslv4DWsBKDVPJc3Na44UDaaEQr87KW4bstw81EyE7zm020Iby5FCMWOhfajUCQQC0Br4oQbGmt1n/S/l9kYCxB6LdJ2zgdnnj7r0gmIy63j6Jm+jmKfLjHPw8U3LofAubdBlUG2s9I4/szikZLZrW";

    }


    /**
     * 获取公钥
     */
    public static String getPublicKey() {
        return publicKeyStr;
    }

    /**
     * 获取私钥
     */
    public static String getPrivateKey() {
        return privateKeyStr;
    }

    /**
     * 从字符串获取公钥
     */
    private static PublicKey getPublicKeyFromString() {
        try {
            byte[] keyBytes = Base64.decodeBase64(publicKeyStr);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("获取公钥失败", e);
            return null;
        }
    }

    /**
     * 从字符串获取私钥
     */
    private static PrivateKey getPrivateKeyFromString() {
        try {
            byte[] keyBytes = Base64.decodeBase64(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("获取私钥失败", e);
            return null;
        }
    }

    /**
     * RSA加密（公钥加密）
     */
    public static String encrypt(String plainText) {
        try {
            PublicKey publicKey = getPublicKeyFromString();
            if (publicKey == null) {
                return null;
            }

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            byte[] data = plainText.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = doFinalWithBlock(cipher, data, MAX_ENCRYPT_BLOCK);

            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            log.error("RSA加密失败", e);
            return null;
        }
    }

    /**
     * RSA解密（私钥解密）
     */
    public static String decrypt(String encryptedText) {
        try {
            PrivateKey privateKey = getPrivateKeyFromString();
            if (privateKey == null) {
                return null;
            }

            byte[] encryptedBytes = Base64.decodeBase64(encryptedText);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            byte[] decryptedBytes = doFinalWithBlock(cipher, encryptedBytes, MAX_DECRYPT_BLOCK);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("RSA解密失败", e);
            return null;
        }
    }

    /**
     * 分段处理加密/解密（处理长文本）
     */
    private static byte[] doFinalWithBlock(Cipher cipher, byte[] data, int maxBlockSize) throws Exception {
        int inputLen = data.length;
        int offSet = 0;
        byte[] cache;
        int i = 0;

        // 创建输出缓冲区
        byte[] result = new byte[0];

        // 对数据分段处理
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > maxBlockSize) {
                cache = cipher.doFinal(data, offSet, maxBlockSize);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }

            // 合并结果
            byte[] temp = new byte[result.length + cache.length];
            System.arraycopy(result, 0, temp, 0, result.length);
            System.arraycopy(cache, 0, temp, result.length, cache.length);
            result = temp;

            i++;
            offSet = i * maxBlockSize;
        }

        return result;
    }

    public static void main(String[] args) {
        String decrypt = decrypt("bIUV7cwRCEEHy9LON3qbm9+4yrxzlZi6WrtdBuIm+DBBpTuMx6pdCYS2OAdXeywouFcHEzjp4X/E6LCp+LMRM+qnmE4vQiavM632wa2iPdd7J50loQvwfsCpVPl+Peq42lVQ6Fboel2hR97xtphbhLzv6ym7UaXdHSzRMybIrGc=");
        System.out.println(decrypt);
    }
}
