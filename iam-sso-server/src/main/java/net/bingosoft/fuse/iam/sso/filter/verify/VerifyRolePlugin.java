//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package net.bingosoft.fuse.iam.sso.filter.verify;

import java.io.IOException;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import fly.core.security.Security;
import net.bingosoft.fuse.iam.common.entities.roles.IamRoleUserEntity;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.managers.members.IamTenantMemberManager;
import net.bingosoft.fuse.iam.common.managers.roles.IamRoleUserManager;
import net.bingosoft.fuse.iam.common.managers.users.IamTenantManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.sso.config.service.GfCommTenantConfigServiceImpl;
import net.bingosoft.fuse.iam.sso.dto.StringUtil;
import openfuse.sso.core.plugin.SSOPlugin;
import openfuse.sso.core.security.authentication.user.AuthenticationInterceptor;
import openfuse.sso.core.security.login.LoginInterceptor;
import openfuse.sso.core.security.session.UserSession;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
@Component
public class VerifyRolePlugin implements SSOPlugin {
    private static final Logger logger = LoggerFactory.getLogger(VerifyRolePlugin.class);
    private String DEFAULT_TENANT_ID = "J9364cyRHGCmCfBXn5jc83";
    private String SYS_CONFIG_NAME = "sso.plugins.verify-role";
    @Autowired
    IamRoleUserManager iamRoleUserManager;
    @Autowired
    IamUserManager iamUserManager;
    @Autowired
    IamTenantMemberManager iamTenantMemberManager;
    @Autowired
    IamTenantManager iamTenantManager;
    @Autowired
    GfCommTenantConfigServiceImpl gfCommTenantConfigService;

    public LoginInterceptor getLoginInterceptor() {
        return new LoginInterceptor() {
            public boolean onLoginSuccess(HttpServletRequest request, HttpServletResponse response, UserSession session) throws IOException, ServletException {
                return true;
            }

            public boolean onLoginFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
                return true;
            }
        };
    }

    public AuthenticationInterceptor getAuthenticationInterceptor() {
        return new AuthenticationInterceptor() {
            public void preAuthentication(HttpServletRequest request, Authentication authentication) {
                VerifyRolePlugin.this.checkUserRole(request, authentication);
            }
        };
    }

    private void checkUserRole(HttpServletRequest request, Authentication authentication) {
        // 排除掉门户的获取token接口
        if ("/iamsso/oauth2/login".equals(request.getRequestURI())) {
            String commUserLimit = gfCommTenantConfigService.findCommTenantConfig(SYS_CONFIG_NAME);
            if("true".equals(commUserLimit)) {
                logger.info("【角色认证开启】");
                if (authentication instanceof UsernamePasswordAuthenticationToken) {
                    String username = authentication.getName();
                    if (StringUtil.isNotEmpty(username)) {
                        logger.error("【角色认证】查询用户{}角色", username);
                        IamUserEntity user = iamUserManager.findByUsername(null, username);
                        if (ObjectUtils.isEmpty(user)) {
                            logger.error("【角色认证】用户账户为空");
                            throw new InternalAuthenticationServiceException("该用户账户或密码错误");
                        } else {
                            if (iamTenantMemberManager.checkTenantAdmin(DEFAULT_TENANT_ID, user.getId())) {
                                logger.info("【角色认证】{}为租户管理员", username);
                                return;
                            }
                            List<IamRoleUserEntity> list = iamRoleUserManager.queryUserRoles(user.getId(), null).list();
                            if (ObjectUtils.isNotEmpty(list)) {
                                logger.info("【角色认证】{}为有登录权限用户");
                                return;
                            }
                            try {
                                iamTenantMemberManager.findSysMember(user.getId());
                            } catch (Exception e) {
                                logger.info("【角色认证】{}为普通用户，不予登录", username);
                                throw new InternalAuthenticationServiceException("当前用户无登录权限");
                            }
                            logger.info("【角色认证】{}为平台管理员", username);
                        }
                    } else {
                        logger.error("【角色认证】用户账户为空");
                        throw new InternalAuthenticationServiceException("该用户账户或密码错误");
                    }
                }
                }
        }
    }
}
