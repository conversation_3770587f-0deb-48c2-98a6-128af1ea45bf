/*
 * Copyright 2021 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package fly.data.relational.field;

import fly.core.WrapperBeanFactory;
import fly.data.common.exception.SerializeException;
import fly.data.relational.model.Field;
import org.springframework.data.util.Lazy;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <p>PasswordFieldSerializer class.</p>
 *
 * <AUTHOR>
 */
public class PasswordFieldSerializer implements FieldSerializer {

    private static final String ENCODER_ID_PREFIX = "{";
    private static final String ENCODER_ID_SUFFIX = "}";

    private final Lazy<PasswordEncoder> encoder;

    public PasswordFieldSerializer(WrapperBeanFactory beanFactory) {
        this.encoder = Lazy.of(() -> beanFactory.getBeanOrElseGet(PasswordEncoder.class,
                PasswordEncoderFactories::createDelegatingPasswordEncoder));
    }

    @Override
    public Object encode(Field field, Object plain) throws SerializeException {
        String password = plain.toString();
        if (hasEncoderId(password)) {
            return password;
        } else {
            return encoder.get().encode(password);
        }
    }

    @Override
    public Object tryDecode(Field field, Object value) throws SerializeException {
        return null;
    }

    private static boolean hasEncoderId(String password) {
        return password.startsWith(ENCODER_ID_PREFIX) && password.indexOf(ENCODER_ID_SUFFIX, 1) > -1 && password.substring(password.indexOf(ENCODER_ID_SUFFIX, 1)).length() > 6;
    }
}
