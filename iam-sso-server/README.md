## 概述

开发版sso启动工程

## 项目开发&部署

### 服务依赖

开发版SSO不依赖第三方基础服务，该开发版本SSO仅用于开发&测试环境用于验证OAuth2认证服务基础能力

### 启动入口

```yaml
cn.openfuse.SSOApplicationServer
```

### 配置文件

```yaml
resources/application.yml
```

> 参考配置文件，SSO启动后默认启用8060端口上下文为/sso，且启用https连接，因此本地开发启动后默认访问路径为`https://localhost:8060/sso`

### 日志管理

```yaml
resources/META-INF/logback-spring.xml
```

### 主题目录

```yaml
resources/themes
```

更多请参考[主题定制](https://openfuse.bingosoft.net/sso/docs/custom/theme.html)

### 项目打包

```yaml
mvn clean package
```

部署包构建路径`{basePath}/target/jsw/sso-server.zip`

### 项目部署

- 传统部署方式（JSW部署包）

```bash
# 解压部署包
unzip sso-server.zip -d sso-server
# 服务启动
cd sso-server && chmod +x bin/* && ./bin/sso-server console
```

- Docker部署

项目根路径内置有`Dockerfile`文件，利用该Dockerfile构建镜像

镜像构建

```yaml
docker build --build-arg JSW_DIR=./sso-server -t openfuse/sso-server:5.0.0-SNAPSHOT .
```
其中`JSW_DIR`参数为jsw部署包存放路径

## 了解更多

### 服务启动

该服务JSW部署启动方式为前台启动，更多启动方式请参考[jsw-部署](https://openfuse.bingosoft.net/fly-framework/deploy/jsw.html#jsw-%E9%83%A8%E7%BD%B2)

### 数据初始化

初始化数据统一在`resources/application.yml`中维护，内置有以下用户、客户端数据

|参数说明|参数值|
|----|----|----|
|SSO内置客户端 | client1 / secret1 |
|SSO内置客户端 | client2 / secret2 |
|SSO内置普通用户 | user1 / pass1 |
|SSO内置普通用户 | user2 / pass2 |