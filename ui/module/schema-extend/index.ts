import { SchemaRegistry } from '@fly-vue/lcdp-engine'

// 组件扩展
const schemaExtend = {}

const requireComponents = require.context('./components', false, /\.ts/)
// 遍历出每个组件的路径
requireComponents.keys().forEach((fileName) => {
  // 组件扩展
  const reqCom = requireComponents(fileName)

  Object.assign(schemaExtend, reqCom.default || reqCom)
})
export async function extendSchema() {
  await import('../schemas/schema.json').then(({ default: schema }) => {
    SchemaRegistry.addSchema({ schema, packageName: process.env.VUE_APP_NAME })
  })
  SchemaRegistry.extendSchema(schemaExtend)
}
