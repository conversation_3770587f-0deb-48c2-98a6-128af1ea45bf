import { AsyncModuleRegistry } from '@fly-vue/loader'
const moduleDef = AsyncModuleRegistry.find(process.env.VUE_APP_NAME)
// eslint-disable-next-line no-undef
__webpack_public_path__ = moduleDef && moduleDef.publicPath
import {
  IWebModule,
  WebApplicationEventType,
  webApplication
} from '@fly-vue/core'
import { extendSchema } from './schema-extend'
import { registerPlugins } from './plugins'
import { registerActions } from './actions'
import components from './components'
import './style/index.css'

const _module: IWebModule = {
  dependencies: ['@fly-vue/core'],
  name: process.env.VUE_APP_NAME,
  version: process.env.VUE_APP_VERSION,
  components: components as any,
  install: function () {
    webApplication.registerEvent(
      WebApplicationEventType.moduleInstalled,
      async () => {
        await extendSchema()
        await registerPlugins()
        await registerActions()
      },
      process.env.VUE_APP_NAME
    )
  }
}

webApplication.addModule(_module)

export default _module
