{"$schema": "http://json-schema.org/draft-06/schema#", "id": "", "definitions": {"OrganModel": {"title": "组织展示", "description": "", "group": "基础/组织成员", "orderNo": 300, "icon": "", "customicon": "idesignfont ides-table", "defType": "component", "container": false, "htmlAttrs": "on", "properties": {"value": {"type": "array", "items": {"type": "string"}, "title": "值", "group": "基础", "orderNo": 1}, "title": {"type": "string", "title": "标题", "group": "基础", "orderNo": 2, "default": "标题:"}, "col": {"type": "number", "title": "布局", "group": "基础", "orderNo": 3, "$ref": "#/definitions/FormItemSpan", "default": 24}, "labelWidth": {"type": "string", "title": "标题宽度", "group": "基础", "orderNo": 4, "default": "80px"}, "labelTitleAlign": {"type": "string", "title": "标题对齐方式", "group": "基础", "orderNo": 5, "default": "right", "$ref": "#/definitions/AlignMode"}, "disabled": {"type": "boolean", "title": "禁用", "enum": [true, false], "group": "基础", "orderNo": 6}, "parentId": {"type": "string", "title": "父组织id", "group": "基础", "default": "", "orderNo": 7}}, "events": {"on-change": {"title": "数据改变时触发", "group": "交互", "orderNo": 0}}, "type": "object", "additionalProperties": false}, "SelectDown": {"title": "下拉选择", "description": "", "group": "基础/表单", "orderNo": 300, "icon": "", "customicon": "idesignfont ides-table", "defType": "component", "container": false, "htmlAttrs": "on", "properties": {"value": {"type": "string", "title": "值", "group": "基础", "orderNo": 10}, "title": {"type": "string", "title": "标题", "group": "基础", "orderNo": 20}, "labelWidth": {"type": "string", "title": "标题宽度", "group": "基础", "orderNo": 30}, "apiName": {"type": "string", "title": "接口名", "group": "api相关", "orderNo": 40, "default": "/api/tenant/user/extend/all"}, "apiMethod": {"$ref": "#/definitions/RequestType", "title": "请求方法", "group": "api相关", "orderNo": 50, "default": "get"}, "edit": {"type": "boolean", "title": "编辑", "enum": [true, false], "group": "基础", "orderNo": 55, "default": true}, "col": {"type": "number", "title": "布局", "group": "基础", "description": "24栅格,将区域进行24等分,16即占16等分", "orderNo": 60, "$ref": "#/definitions/FormItemSpan"}, "search": {"type": "string", "title": "搜索字段名", "group": "基础", "orderNo": 70, "default": "name"}, "fieldVal": {"type": "string", "title": "选项value值", "group": "基础", "orderNo": 80, "default": "value"}, "fieldLabel": {"type": "string", "title": "选项label值", "group": "基础", "orderNo": 90, "default": "label"}, "fieldSecLabel": {"type": "string", "title": "选项描述信息", "group": "基础", "description": "放在选项后面的，用括号包裹的", "orderNo": 100}}, "events": {"on-change": {"title": "数据改变时触发", "group": "交互", "orderNo": 0}}, "type": "object", "additionalProperties": false}, "UserSelect": {"title": "用户列表选择", "description": "", "group": "基础/组织成员", "orderNo": 300, "icon": "", "customicon": "idesignfont ides-table", "defType": "component", "container": false, "htmlAttrs": "on", "properties": {"value": {"type": "array", "items": {"type": "string"}, "title": "值", "group": "基础", "orderNo": 1}, "title": {"type": "string", "title": "标题", "group": "基础", "orderNo": 2}, "col": {"type": "number", "title": "布局", "default": 24, "group": "基础", "orderNo": 3, "$ref": "#/definitions/FormItemSpan"}, "labelWidth": {"type": "string", "title": "标题宽度", "group": "基础", "orderNo": 4, "default": "80px"}, "labelTitleAlign": {"type": "string", "title": "标题对齐方式", "group": "基础", "orderNo": 5, "default": "right", "$ref": "#/definitions/AlignMode"}, "disabled": {"type": "boolean", "title": "禁用", "enum": [true, false], "group": "基础", "orderNo": 6}, "orgId": {"type": "string", "title": "组织id", "group": "基础", "orderNo": 7}}, "events": {"on-change": {"title": "数据改变时触发", "group": "交互", "orderNo": 0}}, "type": "object", "additionalProperties": false}, "UserSort": {"title": "部门用户排序", "description": "", "group": "基础/组织成员", "orderNo": 300, "icon": "", "customicon": "idesignfont ides-table", "defType": "component", "container": false, "htmlAttrs": "on", "properties": {"value": {"type": "array", "items": {"type": "object"}, "title": "值", "group": "基础", "orderNo": 1}, "title": {"type": "string", "title": "标题", "group": "基础", "orderNo": 2}, "col": {"type": "number", "title": "布局", "default": 24, "group": "基础", "orderNo": 3, "$ref": "#/definitions/FormItemSpan"}, "labelWidth": {"type": "string", "title": "标题宽度", "group": "基础", "orderNo": 4}, "isMultiple": {"type": "boolean", "title": "多选", "enum": [true, false], "group": "基础", "orderNo": 5}, "readonly": {"type": "boolean", "title": "只读", "enum": [true, false], "group": "基础", "orderNo": 6}, "sortStatus": {"$ref": "#/definitions/SortStatus", "title": "排序按钮跳转状态", "group": "基础", "orderNo": 7, "default": "default", "description": "选择'弹窗'，需要在交互中'点击排序按钮触发'配置弹窗才会生效"}}, "events": {"on-change": {"title": "数据改变时触发", "group": "交互", "orderNo": 0}, "on-sort-status": {"title": "点击排序按钮触发", "group": "交互", "orderNo": 1, "description": "前提是排序按钮跳转状态是弹窗", "properties": {"value": {"type": "string", "title": "orgId值"}}}}, "type": "object", "additionalProperties": false}, "AlignMode": {"anyOf": [{"enum": ["left", "center", "right"], "originEnum": [{"value": "left", "title": "左对齐"}, {"value": "center", "title": "居中"}, {"value": "right", "title": "右对齐"}]}, {"pattern": "^([l|L][e|E][f|F][t|T])|([c|C][e|E][n|N][t|T][e|E][r|R])|([r|R][i|I][g|G][h|H][t|T])$"}]}, "RequestType": {"anyOf": [{"enum": ["get", "post"], "originEnum": [{"value": "get", "title": "get"}, {"value": "post", "title": "post"}]}, {"pattern": "^([g|G][e|E][t|T])|([p|P][o|O][s|S][t|T])$"}]}, "SortStatus": {"anyOf": [{"enum": ["default", "show", "dialog"], "originEnum": [{"value": "default", "title": "默认"}, {"value": "show", "title": "展示"}, {"value": "dialog", "title": "弹窗"}]}, {"pattern": "^([d|D][e|E][f|F][a|A][u|U][l|L][t|T])|([s|S][h|H][o|O][w|W])|([d|D][i|I][a|A][l|L][o|O][g|G])$"}]}}, "anyOf": []}