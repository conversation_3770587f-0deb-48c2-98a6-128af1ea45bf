<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iam-template-parent</artifactId>
        <groupId>net.bingosoft.fuse</groupId>
        <version>5.3.3.2-sp2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>iam-api-extension</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.bingosoft.fuse</groupId>
                <artifactId>iam-bom</artifactId>
                <version>${iam.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>net.bingosoft.fuse</groupId>
            <artifactId>iam-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.6.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bingosoft.fuse.oss</groupId>
            <artifactId>dengine-security</artifactId>
            <version>1.7.9</version>
            <scope>compile</scope>
        </dependency>
        <!-- ldap -->
        <dependency>
            <groupId>com.novell.ldap</groupId>
            <artifactId>jldap</artifactId>
            <version>4.3</version>
        </dependency>
    </dependencies>


</project>
