package net.bingosoft.fuse.iam.api.tenant.msg.utils;

import net.bingosoft.fuse.iam.api.tenant.msg.enums.UserTypeEnum;

public class MsgParameterUtils {

    public static String getCodeByStaffType(String staffType){
        if(staffType.contains("OA")&&staffType.contains("AD")&&staffType.contains("mailbox")){
            return UserTypeEnum.OA_ERP_EMAIL.getValue();
        }
        if(staffType.contains("OA")){
            return UserTypeEnum.OA_ERP.getValue();
        }
        if(staffType.contains("mailbox")){
            return UserTypeEnum.EMAIL.getValue();
        }
        if(staffType.contains("AD")){
            return UserTypeEnum.AD.getValue();
        }
        return UserTypeEnum.OA_ERP_EMAIL.getValue();
    }
}
