package net.bingosoft.fuse.iam.api.tenant.requst.entity;

import cn.hutool.core.date.DateUtil;
import fly.core.data.annotation.*;
import fly.core.data.domains.CreatedAt;
import fly.core.data.domains.CreatedByName;
import fly.core.data.domains.Deleted;
import fly.core.meta.annotation.Summary;
import net.bingosoft.fuse.commons.core.data.entities.AbstractTenantUpdatedEntity;

import java.util.Date;

@Entity(table = "iam_request_operation")
public class IamRequestOperationEntity extends AbstractTenantUpdatedEntity {

    @UUID
    @Summary("ID")
    protected String id;

    @Summary("操作对象的ID")
    @Column
    protected String objectId;

    @Summary("操作对象类型;user-用户，org-组织")
    @Column
    protected  String objectType;

    @Summary("操作类型;add-新增，update-修改，delete-删除")
    @Column
    protected String operationType;

    @Summary("请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回")
    @Column
    @Settable
    protected Integer status;

    @Summary("提交用户ID")
    @CreatedByName(
            nullable = true
    )
    @Column
    @Settable
    protected String submitBy;

    @CreatedAt
    @Summary("提交时间")
    @Column
    @Settable
    protected Date submitAt;

    @Summary("提交时间")
    @Settable
    protected String submitAtStr;

    @Summary("审核用户ID")
    @Column
    @Settable
    protected String approvalBy;

    @Summary("审核时间")
    @Column
    @Settable
    protected Date approvalAt;

    @Summary("审核意见")
    @Column
    @Settable
    protected String approvalOpinion;

    @Summary("复核人名称")
    @Column
    @Settable
    protected String approvalByName;

    @Summary("描述")
    @Column
    @Settable
    protected String description;

    @Summary("请求对象")
    @Settable
    @JsonColumn(JsonColumn.JsonSize.LARGE)
    protected String requestObject;

    @Summary("操作对象")
    @Settable
    protected String requestObjectStr;

    @Summary("请求对象（旧）")
    @Settable
    @JsonColumn(JsonColumn.JsonSize.LARGE)
    protected String requestObjectOld;

    @Summary("IT需求类型")
    @Column
    @Settable
    protected String itType;

    @Summary("IT需求地址")
    @Column
    @Settable
    protected String itUrl;

    @Summary("是否同步ACM 0：否 1：是")
    @Column
    @Settable
    protected Integer acmFlag;

    @Deleted
    @ApiReadonly
    @Summary("可用状态")
    protected Boolean deleted = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSubmitBy() {
        return submitBy;
    }

    public void setSubmitBy(String submitBy) {
        this.submitBy = submitBy;
    }

    public Date getSubmitAt() {
        return submitAt;
    }

    public void setSubmitAt(Date submitAt) {
        this.submitAt = submitAt;
    }

    public String getApprovalBy() {
        return approvalBy;
    }

    public void setApprovalBy(String approvalBy) {
        this.approvalBy = approvalBy;
    }

    public Date getApprovalAt() {
        return approvalAt;
    }

    public void setApprovalAt(Date approvalAt) {
        this.approvalAt = approvalAt;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    public String getApprovalByName() {
        return approvalByName;
    }

    public void setApprovalByName(String approvalByName) {
        this.approvalByName = approvalByName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRequestObject() {
        return requestObject;
    }

    public void setRequestObject(String requestObject) {
        this.requestObject = requestObject;
    }

    public String getRequestObjectOld() {
        return requestObjectOld;
    }

    public void setRequestObjectOld(String requestObjectOld) {
        this.requestObjectOld = requestObjectOld;
    }

    public String getItType() {
        return itType;
    }

    public void setItType(String itType) {
        this.itType = itType;
    }

    public String getItUrl() {
        return itUrl;
    }

    public void setItUrl(String itUrl) {
        this.itUrl = itUrl;
    }

    public Integer getAcmFlag() {
        return acmFlag;
    }

    public void setAcmFlag(Integer acmFlag) {
        this.acmFlag = acmFlag;
    }

    public String getSubmitAtStr() {
        return submitAtStr = DateUtil.format(submitAt,"yyyy-MM-dd HH:mm:ss");
    }

    public void setSubmitAtStr(String submitAtStr) {
        this.submitAtStr = submitAtStr;
    }

    public String getRequestObjectStr() {
        return requestObjectStr;
    }

    public void setRequestObjectStr(String requestObjectStr) {
        this.requestObjectStr = requestObjectStr;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
