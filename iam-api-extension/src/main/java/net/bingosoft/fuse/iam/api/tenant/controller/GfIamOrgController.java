package net.bingosoft.fuse.iam.api.tenant.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import fly.core.data.model.PageAndSize;
import fly.core.data.model.RowMap;
import fly.core.meta.annotation.Summary;
import fly.data.common.query.Query;
import fly.data.common.query.QueryBuilder;
import fly.data.common.query.QueryResult;
import fly.orm.dao.query.CriteriaQuery;
import fly.rest.data.annotation.Crud;
import fly.rest.data.annotation.SupportsCrudDefaults;
import fly.rest.data.crud.CrudContext;
import fly.rest.data.crud.operation.CrudFind;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.models.security.SecurityScheme;
import net.bingosoft.fuse.iam.api.tenant.entity.GfIamUserOrgEntity;
import net.bingosoft.fuse.iam.api.tenant.ldap.domain.GFDeptWithMember;
import net.bingosoft.fuse.iam.api.tenant.ldap.domain.GFUser;
import net.bingosoft.fuse.iam.api.tenant.ldap.service.LdapDeptService;
import net.bingosoft.fuse.iam.api.tenant.managers.GfIamOrgManager;
import net.bingosoft.fuse.iam.api.tenant.managers.GfIamUserManager;
import net.bingosoft.fuse.iam.api.tenant.vo.IamOrgVo;
import net.bingosoft.fuse.iam.common.entities.users.IamOrgEntity;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.managers.users.IamOrgManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.common.security.OrgRestrictSecurity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "iamOrgExtend", description = "组织管理(扩展)")
@RestController
@RequestMapping("/api/tenant/org/extend")
@Crud(entityClass = IamOrgEntity.class)
public class GfIamOrgController implements CrudFind<IamOrgEntity> {
    @Autowired
    protected IamOrgManager iamOrgManager;
    @Autowired
    protected GfIamOrgManager iamOrgManagerGF;
    @Autowired
    protected IamUserManager iamUserManager;
    @Autowired
    protected GfIamUserManager gfIamUserManager;
    @Autowired
    protected OrgRestrictSecurity orgRestrictSecurity;
    @Autowired
    protected LdapDeptService ldapDeptService;
    @Summary("查询组织详细信息（包括用户成员和群组成员）")
    @GetMapping("/get")
    public IamOrgVo getUserDetails(String orgId) {
        return iamOrgManagerGF.getDetails(orgId);
    }

    @Summary("获取组织列表数据（包括用户成员和群组成员和组织父组织名称）用于导出")
    @PostMapping("/output")
    public List<RowMap> list(@RequestBody List<String> ids) {
        return iamOrgManagerGF.list(ids);
    }

    @GetMapping({""})
    @Summary("查询组织列表")
    public QueryResult<IamOrgEntity> query(CrudContext<IamOrgEntity> context, Query query, String kindId) throws Exception {
        QueryResult<IamOrgEntity> result = iamOrgManager.query(query, true, kindId);
        return result;
    }

    /*
     * 查询组织列表，如果orgId不为空，将改组织放在列表的第一条
     * */
    @SupportsCrudDefaults
    @GetMapping({"/list"})
    @Summary("查询组织列表")
    public QueryResult<RowMap> queryIncludeSelf(Query query, String orgId, String keyword) throws Exception {
        // 子组织
        String sql1 = "select io2.*, io2p.name_ as parent_org_name\n" +
                " from iam_org io2\n" +
                "          left join iam_org io2p on io2.parent_id = io2p.id_\n" +
                " where io2.deleted_ = false ";
        if(StrUtil.isNotEmpty(keyword))
            sql1 += "  and ( io2.name_ like concat('%', '"+keyword+"', '%'))\n";
        if(StrUtil.isNotEmpty(orgId)) {
            IamOrgEntity iamOrgEntity = iamOrgManager.find(orgId);
            sql1 += "   and (io2.code_ like concat('%','"+iamOrgEntity.getCode()+","+"','%') )\n";
        }
        sql1 += " order by sort_order2 asc";
        // 夫组织
        String sql2 = "select io1.*, io1p.name_ as parent_org_name\n" +
                " from iam_org io1\n" +
                "          left join iam_org io1p on io1.parent_id = io1p.id_\n" +
                " where io1.id_ = '" + orgId+"' ";
        // 有关键词的时候搜索不加父组织
        String sql = "";
        QueryResult<RowMap> execute = null;
        if(StrUtil.isNotEmpty(keyword)|| StrUtil.isEmpty(orgId)){
            sql = sql1;
            execute = iamOrgManager.dao().createSQLQuery(sql).limit(query.getPageAndSize().toLimitAndOffset()).withTotal().execute();
        }else {
            sql = "( "+sql2+" ) union ( "+sql1+" )";
            execute = iamOrgManager.dao().createSQLQuery(sql).limit(query.getPageAndSize().toLimitAndOffset()).withTotal().execute();
        }
        return execute;
    }

    /*
    *  查询组织列表树
    * */
    @GetMapping({"/tree/orgs"})
    @Summary("查询组织列表树")
    public QueryResult<IamOrgEntity> queryTree(@RequestParam(required = false) String parentId,@RequestParam(required = false) String name) throws Exception {
        QueryResult<IamOrgEntity> execute = iamOrgManager.createQuery()
//                .filter("parent_id", parentId)
                .alias("o")
                .leftJoin(IamOrgEntity.class,"o1","o.parent_id = o1.id_")
                .where("o.name_ like ? or o.id_ = ?","%"+name+"%",name)
                .where("o.deleted_ = false")
                .selectExtra("o1.name_ parent_org_name")
                .orderBy("o.sort_order asc")
                .execute();
        return execute;
    }

    /*
     *  查询组织列表树(与原接口区别：无权限控制）
     * */
    @GetMapping({"/tree/orgs/extend"})
    @Summary("查询组织列表树")
    public QueryResult<IamOrgEntity> queryOrgsWithDataScope(CrudContext<IamOrgEntity> context, Query query, String kindId) throws Exception {
        QueryResult<IamOrgEntity> result = iamOrgManager.query(query, false, kindId);
//        this.manager.expandParentsPath(result.list());
        return result;
    }

    /*
    *  查询组织列表根节点
    * */
    @GetMapping({"/tree/roots/extend"})
    @Summary("查询组织列表根节点")
    public QueryResult queryOrgRoots(CrudContext<IamOrgEntity> context, Query query, String kindId) throws Exception {
        return iamOrgManagerGF.findTenantRootOrg("P");
    }

    /*
    * 查询组织成员
    * */
    @GetMapping({"/orgMember/{orgId}"})
    @Summary("查询组织成员")
    public QueryResult<IamOrgEntity> queryOrgRoots(@PathVariable("orgId")String orgId, Boolean queryable,Query query,Boolean adOrder) throws Exception {
        CriteriaQuery<IamOrgEntity> filter = iamOrgManager.createQuery().filter("parentId", orgId).filter("deleted", false);
        if(queryable!=null&&queryable){
            filter.queryable(query);
        }
        if(ObjectUtil.isNull(adOrder)){
            filter.orderBy("sort_order2 asc");
        }else {
            filter.orderBy("ad_order desc");
        }
        QueryResult<IamOrgEntity> result = filter.execute();
        return result;
    }

    /*
    * 批量查询组织下是否存在成员
    * */
    @PostMapping({"/batch/member"})
    @Summary("批量查询组织成员")
    public List<IamOrgEntity> queryOrgsExistMember(@RequestBody Set<String> ids) throws Exception {
        List<IamOrgEntity> existsOrg = new ArrayList<>();
        for (String id : ids) {
            List<IamUserEntity> list = gfIamUserManager.query(false,null, true, false, null, false, "P", id, false,false)
                    .list();
            if(ObjectUtil.isNotEmpty(list)){
                IamOrgEntity iamOrgEntity = iamOrgManager.find(id);
                existsOrg.add(iamOrgEntity);
            }
        }
        return existsOrg;
    }

    @PostMapping({"/query"})
    @Summary("根据组织id查询组织")
    public List<IamOrgEntity> queryOrgs(@RequestBody(required = false) List<String> ids, @RequestParam(required = false) String keyword) {
        if(ObjectUtil.isEmpty(ids)&&StrUtil.isEmpty(keyword)) {
            return new ArrayList<>();
        }
        CriteriaQuery<IamOrgEntity> query = iamOrgManager.createQuery();
        if(StrUtil.isNotEmpty(keyword)) {
            query = query.where("simple_spell like '%"+keyword+"%' or name_ like '%"+keyword+"%'");
        }
        if(ObjectUtil.isNotEmpty(ids)) {
            query = query.in("id_","id_",ids);
        }
        QueryResult<IamOrgEntity> execute = query
                .filter("deleted",false)
                .orderBy("sort_order2 asc")
                .execute();
        return execute.list();
    }
    /**
     * 通过父组织id查询子组织列表
     */
    @PostMapping("/list/member")
    public List<String> queryOrgsByParentId(@RequestParam(required = false)String parentId){
        List<String> groupList = iamOrgManager.createQuery()
                .where("#parent_id = ?", parentId)
                .where("#deleted_ = ?", false)
                .execute().list().stream().map(e -> e.getId()).collect(Collectors.toList());
        return groupList;
    }
    /**
     * 触发增量更新
     */
    @GetMapping("/incre")
    public void incre(@RequestParam String id){
        iamOrgManager.createQuery().filter("id",id).update("updatedAt", LocalDateTime.now());
    }

    /**
     * 触发组织同步
     */
    @PostMapping("/syncOrg/{simpleSpell}")
    public void syncOrg(@PathVariable String simpleSpell) {
        iamOrgManagerGF.sysOrgBySimpleSpell(simpleSpell);
    }

    @Summary("从domino获取用户信息")
    @GetMapping("oa/{oaSimpleSpell}")
    public GFDeptWithMember oaInfo(@PathVariable("oaSimpleSpell") String oaSimpleSpell)  {
        List<GFDeptWithMember> deptWithMembersFormLdapForDominoBySimpleSpell = ldapDeptService.getDeptWithMembersFormLdapForDominoBySimpleSpell(oaSimpleSpell);
        if(ObjectUtil.isNotEmpty(deptWithMembersFormLdapForDominoBySimpleSpell)){
            return deptWithMembersFormLdapForDominoBySimpleSpell.get(0);
        }
        return null;
    }

}
