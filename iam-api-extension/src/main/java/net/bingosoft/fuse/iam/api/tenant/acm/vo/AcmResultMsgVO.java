package net.bingosoft.fuse.iam.api.tenant.acm.vo;

import lombok.Data;

/**
 * 接口返回结果封装
 * <AUTHOR>
 * @Date 2022/6/7
 */
@Data
public class AcmResultMsgVO {

    /**
     * 1=错误，0=成功
     */
    private int code;

    /**
     * 返回数据
     */
    private Object data;

    /**
     * 消息
     */
    private String message;

    public AcmResultMsgVO() {
    }

    public AcmResultMsgVO(int code, Object data, String message) {
        this.code = code;
        this.data = data;
        this.message = message;
    }

    //成功返回
    public static AcmResultMsgVO success(Object data){
        return new AcmResultMsgVO(0,data,"返回成功");
    }

    //失败返回
    public static AcmResultMsgVO error(String message){
        return new AcmResultMsgVO(1,null, message);
    }
}
