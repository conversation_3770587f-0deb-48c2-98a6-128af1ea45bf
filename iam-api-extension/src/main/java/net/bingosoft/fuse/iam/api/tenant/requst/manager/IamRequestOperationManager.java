package net.bingosoft.fuse.iam.api.tenant.requst.manager;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import fly.core.data.model.RowMap;
import fly.core.security.Security;
import fly.core.security.userinfo.UserInfo;
import fly.data.common.query.Query;
import fly.data.common.query.QueryResult;
import fly.orm.dao.entity.AbstractEntityOperations;
import fly.orm.dao.query.CriteriaQuery;
import fly.orm.dao.query.SQLQuery;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.tenant.acm.dto.AcmUserEmpDto;
import net.bingosoft.fuse.iam.api.tenant.acm.service.AcmApiService;
import net.bingosoft.fuse.iam.api.tenant.acm.service.impl.AcmSendService;
import net.bingosoft.fuse.iam.api.tenant.acm.service.impl.AcmUserInfoGeninfoService;
import net.bingosoft.fuse.iam.api.tenant.acm.vo.AcmResultMsgVO;
import net.bingosoft.fuse.iam.api.tenant.entity.GfIamUserOrgEntity;
import net.bingosoft.fuse.iam.api.tenant.enums.DeptType;
import net.bingosoft.fuse.iam.api.tenant.enums.GroupType;
import net.bingosoft.fuse.iam.api.tenant.managers.*;
import net.bingosoft.fuse.iam.api.tenant.models.GfIamUserCreation;
import net.bingosoft.fuse.iam.api.tenant.msg.service.SendService;
import net.bingosoft.fuse.iam.api.tenant.oa.manager.ErpInfoManager;
import net.bingosoft.fuse.iam.api.tenant.requst.entity.IamRequestOperationEntity;
import net.bingosoft.fuse.iam.api.tenant.requst.models.IamRequestOperationCreation;
import net.bingosoft.fuse.iam.api.tenant.requst.models.IamRequestOperationQuery;
import net.bingosoft.fuse.iam.api.tenant.util.DictUtil;
import net.bingosoft.fuse.iam.api.tenant.util.IdUtils;
import net.bingosoft.fuse.iam.api.tenant.vo.*;
import net.bingosoft.fuse.iam.common.entities.users.IamOrgEntity;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.managers.groups.IamGroupUserManager;
import net.bingosoft.fuse.iam.common.managers.users.IamOrgManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserOrgManager;
import net.bingosoft.fuse.iam.common.models.users.IamOrgCreation;
import net.bingosoft.fuse.iam.common.models.users.IamUserModel;
import net.bingosoft.fuse.iam.common.password.PasswordManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IamRequestOperationManager  extends AbstractEntityOperations<IamRequestOperationEntity, IamRequestOperationCreation> {
    @Autowired
    protected GfIamUserOrgManager gfIamUserOrgManager;
    @Autowired
    protected OrgAsyncManager orgAsyncManager;
    @Autowired
    protected OrgSyncManager orgSyncManager;
    @Autowired
    protected UserAsyncManager userAsyncManager;
    @Autowired
    protected UserSyncManager userSyncManager;
    @Autowired
    protected IamUserManager iamUserManager;
    @Autowired
    protected PasswordManager passwordManager;
    @Autowired
    protected GfIamUserManager iamUserManagerGF;
    @Autowired
    SendService sendService;
    @Autowired
    protected IamOrgManager iamOrgManager;
    @Autowired
    protected GfIamOrgManager iamOrgManagerGF;
    @Autowired
    protected IamUserOrgManager iamUserOrgManager;
    @Autowired
    protected AcmApiService acmApiService;
    @Autowired
    protected GfIamGroupManager iamGroupManagerGf;
    @Autowired
    protected IamGroupUserManager iamGroupUserManager;
    @Autowired
    protected AcmUserInfoGeninfoService acmUserInfoGeninfoService;
    @Autowired
    protected ErpInfoManager erpInfoManager;
    @Autowired
    protected AcmSendService acmSendService;
    @Autowired
    protected GfIamUserPasswordManager iamUserPasswordManager;

    public QueryResult<IamRequestOperationEntity> getList(IamRequestOperationQuery operationQuery, Query query, String objectType, String name){
        CriteriaQuery<IamRequestOperationEntity> criteriaQuery = this.dao.createQuery(IamRequestOperationEntity.class);
        // 查询日志
        if("log".equals(operationQuery.getListType())){
            criteriaQuery.where("#status_ =?",2).orWhere("#status_ =?",3).orWhere("#status_ =?",4);
        // 如果没传入操作类型，就是待复核列表，只查询status=1的
        } else if("toCheck".equals(operationQuery.getListType())){
            criteriaQuery.where("#status_ =?",1);
        // 有传入操作类型，就查询被驳回，撤回的
        }else{
            criteriaQuery.where("#status_ =?",0).orWhere("#status_ =?",2).orWhere("#status_ =?",4);
            criteriaQuery.where("#created_by = ?", Security.getCurrentUser().getIdAsString());
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getApprovalByName())){
            criteriaQuery.where("#approval_by_name like ? ",StrUtil.concat(true,"%",operationQuery.getApprovalByName(),"%"));
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getItType())){
            criteriaQuery.where("#it_type = ? ",operationQuery.getItType());
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getItUrl())) {
            criteriaQuery.where("#it_url = ? ",operationQuery.getItUrl());
        }
        // 用户类型有user和users两种
        if("user".equals(operationQuery.getObjectType())) {
            criteriaQuery.where("#object_type = ? or object_type = 'users'",operationQuery.getObjectType());
        } else if("org".equals(operationQuery.getObjectType())) {
            criteriaQuery.where("#object_type =?",operationQuery.getObjectType());
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getOperationType())){
            criteriaQuery.where("#operation_type = ? ",operationQuery.getOperationType());
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getStartTime())) {
            criteriaQuery.where("submit_at >= ? ", operationQuery.getStartTime().plusHours(8));
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getEndTime())) {
            criteriaQuery.where("submit_at <= ? ", operationQuery.getEndTime().plusHours(8));
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getCheckStartTime())) {
            criteriaQuery.where("approval_at >= ? ", operationQuery.getCheckStartTime().plusHours(8));
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getCheckEndTime())) {
            criteriaQuery.where("approval_at <= ? ", operationQuery.getCheckEndTime().plusHours(8));
        }
        if(ObjectUtil.isNotEmpty(operationQuery.getApprovalOpinion())){
            criteriaQuery.where("#approval_opinion like ? ",StrUtil.concat(true,"%",operationQuery.getApprovalOpinion(),"%"));
        }
        if(StringUtils.isNotEmpty(operationQuery.getSubmitBy())) {
            criteriaQuery.where("#submit_by like ?",StrUtil.concat(true,"%",operationQuery.getSubmitBy(),"%"));
        }
        if(StringUtils.isNotEmpty(operationQuery.getName())) {
            criteriaQuery.where("#request_object_old like ? or #request_object like ? ",'%'+operationQuery.getName()+'%','%'+operationQuery.getName()+'%');
        }
        criteriaQuery.where("#deleted_ =?", 0);
        criteriaQuery.queryable(query);
        if(!"log".equals(operationQuery.getListType())) {
            criteriaQuery.orderBy("submit_at desc");
        } else {
            criteriaQuery.orderBy("approval_at desc");
        }
        QueryResult<IamRequestOperationEntity> execute = criteriaQuery.execute();
        execute.list().forEach((k) -> {
            String str = "";
            if(k.getObjectType().equals("user")){
                if(!k.getOperationType().equals("add")&&!k.getOperationType().equals("batchAD")) {
                    // 操作对象：旧信息
                    IamUserVo entity = JSONUtil.toBean(k.getRequestObjectOld(), IamUserVo.class);
                    String erp = StringUtils.isNotEmpty(entity.getErpId()) ? entity.getErpId() : "";
                    str = "用户名:" + entity.getName() + ",登录名:" + entity.getUsername();
                    if(StrUtil.isNotEmpty(entity.getOrgId())){
                        IamOrgEntity iamOrgEntity = iamOrgManager.find(entity.getOrgId());
                        if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                            str += ",主部门:" + iamOrgEntity.getName();
                        }
                    }
                    k.setRequestObjectStr(str);
                }else if(k.getOperationType().equals("batchAD")){
                    UserBatchAdVo userBatchAdVo = JSONUtil.toBean(k.getRequestObject(), UserBatchAdVo.class);
                    if(ObjectUtil.isNotEmpty(userBatchAdVo)&&ObjectUtil.isNotEmpty(userBatchAdVo.getUserList())) {
                        for (IamUserVo iamUserVo : userBatchAdVo.getUserList()) {
                            str += "用户名:" + iamUserVo.getName() + ",登录名:" + iamUserVo.getUsername()+",主部门:" + iamUserVo.getOrgName();;
                        }
                    }
                    k.setRequestObjectStr(str);
                }else if(k.getOperationType().equals("add")) {
                    // 操作对象：旧信息
                    IamUserVo entity = JSONUtil.toBean(k.getRequestObject(), IamUserVo.class);
                    str = "用户名:" + entity.getName() + ",登录名:" + entity.getUsername();
                    if(StrUtil.isNotEmpty(entity.getOrgId())){
                        IamOrgEntity iamOrgEntity = iamOrgManager.find(entity.getOrgId());
                        if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                            str += ",主部门:" + iamOrgEntity.getName();
                        }
                    }
                    k.setRequestObjectStr(str);
                }
            }else if(k.getObjectType().equals("org")){
                if(!k.getOperationType().equals("add")) {
                    IamOrgVo entity = JSONUtil.toBean(k.getRequestObjectOld(), IamOrgVo.class);
                    String simpleSpell = StringUtils.isNotEmpty(entity.getSimpleSpell()) ? entity.getSimpleSpell() : "";
                    str = "群组名称:" + entity.getName() + ",组账户名:" + simpleSpell;
                    if (StrUtil.isNotEmpty(entity.getParentId())) {
                        IamOrgEntity iamOrgEntity = iamOrgManager.find(entity.getParentId());
                        if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                            str += ",上级群组:" + iamOrgEntity.getName();
                        }
                    }
                    k.setRequestObjectStr(str);
                }else if(k.getOperationType().equals("add")) {
                    IamOrgVo entity = JSONUtil.toBean(k.getRequestObject(), IamOrgVo.class);
                    String simpleSpell = StringUtils.isNotEmpty(entity.getSimpleSpell()) ? entity.getSimpleSpell() : "";
                    str = "群组名称:" + entity.getName() + ",组账户名:" + simpleSpell;
                    if (StrUtil.isNotEmpty(entity.getParentId())) {
                        IamOrgEntity iamOrgEntity = iamOrgManager.find(entity.getParentId());
                        if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                            str += ",上级群组:" + iamOrgEntity.getName();
                        }
                    }
                    k.setRequestObjectStr(str);
                }
            }else if(k.getObjectType().equals("users")){
                if(k.getOperationType().equals("move")){
                    IamUsersMoveVo moveVo = JSONUtil.toBean(k.getRequestObject(), IamUsersMoveVo.class);
                    List<String> addUserIds = moveVo.getAddUserIds();
                    for (String addUserId : addUserIds) {
                        IamUserEntity iamUserEntity = iamUserManager.find(addUserId);
                        if(ObjectUtil.isNotEmpty(iamUserEntity)) {
                            str += "用户名:" + iamUserEntity.getName() + ",登录名:" + iamUserEntity.getUsername();
                            if (StrUtil.isNotEmpty(iamUserEntity.getOrgId())) {
                                IamOrgEntity iamOrgEntity = iamOrgManager.find(iamUserEntity.getOrgId());
                                if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                                    str += ",主部门:" + iamOrgEntity.getName() + ";";
                                }
                            }
                        }
                    }
                    k.setRequestObjectStr(str);
                }else if(k.getOperationType().equals("delay")){
                    IamUserDelayVo moveVo = JSONUtil.toBean(k.getRequestObject(), IamUserDelayVo.class);
                    List<String> userIds = moveVo.getUserIds();
                    for (String users : userIds) {
                        IamUserEntity iamUserEntity = iamUserManager.find(users);
                        if(ObjectUtil.isNotEmpty(iamUserEntity)) {
                            str += "用户名:" + iamUserEntity.getName() + ",登录名:" + iamUserEntity.getUsername();
                            if (StrUtil.isNotEmpty(iamUserEntity.getOrgId())) {
                                IamOrgEntity iamOrgEntity = iamOrgManager.find(iamUserEntity.getOrgId());
                                if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                                    str += ",主部门:" + iamOrgEntity.getName() + ";";
                                }
                            }
                        }
                    }
                    k.setRequestObjectStr(str);
                }else if(k.getOperationType().equals("impDelay")){
                    IamUserImportDelayDto moveVo = JSONUtil.toBean(k.getRequestObject(), IamUserImportDelayDto.class);
                    List<IamUserEntity> userList = moveVo.getUserList();
                    for (IamUserEntity users : userList) {
                        if(ObjectUtil.isNotEmpty(users)) {
                            str += "用户名:" + users.getName() + ",登录名:" + users.getUsername();
                        }
                    }
                    k.setRequestObjectStr(str);
                }
            }
        });
        return execute;
    }


    @Transactional(rollbackFor = Exception.class)
    public IamRequestOperationEntity insertUserOperate(IamUserVo userVo,String operationType,Integer status,String objectId,Integer acmFlag,String requstId){
        operationType = StringUtils.isNotEmpty(operationType) ? operationType : userVo.getOperationType();
        status = status != null ? status : userVo.getStatus();
        objectId = objectId != null ? objectId : userVo.getObjectId();
        String itType = userVo!=null ? userVo.getItType() : null;
//        // 若不更改密码，不会传输原密码，需要补充密码信息，以便比较更改
//        if(operationType.equals("update") && StrUtil.isEmpty(userVo.getPassword())) {
//            IamUserEntity iamUserEntity = iamUserManager.find(IamUserEntity.class, objectId);
//            userVo.setPassword(iamUserEntity.getPassword());
//        }
        return insert("user",status,operationType,userVo,objectId,itType,userVo.getItUrl(),acmFlag,requstId);
    }


    public IamRequestOperationEntity batchInsert(Object userVo, String operationType, Integer status, String itType, String itUrl) {

        return insert("user",status,operationType,userVo,null,itType,itUrl,1,null);
    }


    public IamRequestOperationEntity insertBatchMoveUserOperate(IamUsersMoveVo moveVo, String operationType, Integer status) {
        return insert("users",status,operationType,moveVo,null,moveVo.getItType(),moveVo.getItUrl(),moveVo.getAcmFlag(),null);
    }

    @Transactional(rollbackFor = Exception.class)
    public IamRequestOperationEntity insertOrgOperate(IamOrgVo orgVo,String operationType,Integer status,String objectId,Integer acmFlag,String requestId){
        operationType = StringUtils.isNotEmpty(operationType) ? operationType : orgVo.getOperationType();
        status = status != null ? status : orgVo.getStatus();
        objectId = objectId != null ? objectId : orgVo.getObjectId();
        return insert("org",status,operationType,orgVo,objectId,orgVo.getItType(),orgVo.getItUrl(),orgVo.getAcmFlag(),requestId);
    }

    @Transactional(rollbackFor = Exception.class)
    public IamRequestOperationEntity insert(String type, Integer status, String operationType, Object requestObject, String objectId, String itType, String itUrl, Integer acmFlag, String requestId){
        IamRequestOperationEntity entity = new IamRequestOperationEntity();
        entity.setObjectType(type);
        entity.setStatus(status);
        entity.setOperationType(operationType);
        ObjectMapper mapper = new ObjectMapper();
        //对象转为JSON字符串
        if(ObjectUtil.isNotEmpty(requestObject)) {
            String valueAsString = JSONUtil.toJsonStr(requestObject);
            entity.setRequestObject(valueAsString);
        }
        entity.setObjectId(objectId);
        entity.setId(IdUtils.simpleUUID());
        entity.setItType(itType);
        entity.setItUrl(itUrl);
        entity.setAcmFlag(acmFlag);
        // 用户
        if("user".equals(type)){
            if(!"add".equals(operationType)&&!"batchAD".equals(operationType)) {
                IamUserEntity iamUserEntity = iamUserManagerGF.findDetail(objectId, false, null);
                IamUserVo iamUserVo = new IamUserVo();
                BeanUtil.copyProperties(iamUserEntity,iamUserVo,"expiredAt");
                BeanUtil.copyProperties(iamUserEntity.getProperties(),iamUserVo);
                iamUserVo.setExpiredAt(iamUserEntity.getExpiredAt());
                entity.setRequestObjectOld(JSONUtil.toJsonStr(iamUserVo));
                if("update".equals(operationType)){
                    entity.setDescription("更新用户信息:"+iamUserEntity.getName());
                }else if("delete".equals(operationType)){
                    entity.setDescription("删除用户:"+iamUserEntity.getName());
                }else if("move".equals(operationType)){
                    entity.setDescription("移动用户:"+iamUserEntity.getName());
                }else if("disable".equals(operationType)){
                    entity.setDescription("禁用用户:"+iamUserEntity.getName());
                }else if("enable".equals(operationType)){
                    entity.setDescription("启用用户:"+iamUserEntity.getName());
                }else if("enaDel".equals(operationType)){
                    entity.setDescription("启用注销用户:"+iamUserEntity.getName());
                }else if("updatePass".equals(operationType)){
                    entity.setDescription("修改用户密码:"+iamUserEntity.getName());
                }else if("resetPass".equals(operationType)){
                    entity.setDescription("置空用户密码:"+iamUserEntity.getName());
                }
            }
            else if("add".equals(operationType)){
                IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(),IamUserVo.class);
                entity.setAcmFlag(creation.getAcmFlag());
                entity.setDescription("添加用户"+creation.getName());
            }else if("batchAD".equals(operationType)){
                entity.setDescription("批量添加AD用户");
            }
        }else if("org".equals(type)){
        // 组织
            if(!"add".equals(operationType)){
                IamOrgVo orgEntity = iamOrgManagerGF.getDetails(objectId);
                entity.setRequestObjectOld(JSONUtil.toJsonStr(orgEntity));
                if("update".equals(operationType)){
                    entity.setDescription("更新组织信息:"+orgEntity.getName());
                }else if("delete".equals(operationType)){
                    entity.setDescription("删除组织:"+orgEntity.getName());
                }else if("move".equals(operationType)){
                    entity.setDescription("移动组织:"+orgEntity.getName());
                }
            }else {
                IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(),IamOrgVo.class);
                entity.setDescription("添加组织"+creation.getName());
                // 是否发送acm
                entity.setAcmFlag(creation.getAcmFlag());
            }
        }else if("users".equals(type)){
            if("move".equals(operationType)){
                IamUsersMoveVo usersMoveVo = (IamUsersMoveVo) requestObject;
                Boolean moveMainDepartment = usersMoveVo.getMoveMainDepartment();
                List<IamUserEntity> oldUserList = iamUserManager.createQuery()
                        .alias("u")
                        .in("u.id_", "id_", usersMoveVo.getAddUserIds())
                        .leftJoin(IamOrgEntity.class,"o","o.id_=u.org_id")
                        .selectExtra("o.name_ org_name")
                        .list();
                String[] names = usersMoveVo.getUserNewOANames().split("\n");
                List<String> list = iamUserManagerGF.getUserIdByOANameOrName(names);
                String idsStr = list.stream().collect(Collectors.joining(","));
                entity.setObjectId(idsStr);
                String str = moveMainDepartment?"移动用户主部门：":"移动用户：";
                for (IamUserEntity user : oldUserList) {
                    str += user.getName()+";";
                }
                // 批量移动，是否抄送acm由用户属性独立决定
                entity.setRequestObjectOld(JSONUtil.toJsonStr(oldUserList));
                entity.setDescription(str);
            }else if ("impDelay".equals(operationType)){
                IamUserImportDelayDto usersMoveVo = (IamUserImportDelayDto) requestObject;
                List<IamUserEntity> list = usersMoveVo.getUserList();
                List<String> ids = usersMoveVo.getUserList().stream().map(e -> e.getId()).collect(Collectors.toList());
                List<IamUserEntity> oldUserList = iamUserManager.createQuery()
                        .alias("u")
                        .in("u.id_", "id_", ids)
                        .leftJoin(IamOrgEntity.class,"o","o.id_=u.org_id")
                        .selectExtra("o.name_ org_name")
                        .list();
                String str = "用户批量延期：";
                for (IamUserEntity usersInfoById : list) {
                    str += usersInfoById.getName()+";";
                }
                String idsStr = StrUtil.join(",",ids);
                entity.setRequestObjectOld(JSONUtil.toJsonStr(oldUserList));
                entity.setObjectId(idsStr);
                // 批量移动，是否抄送acm由用户属性独立决定
                entity.setDescription(str);
                entity.setAcmFlag(0);
            }else{
                IamUserDelayVo usersMoveVo = (IamUserDelayVo) requestObject;
                List<String> userIds = usersMoveVo.getUserIds();
                List<IamUserEntity> list = iamUserManager.createQuery().in("id_", "id_", userIds).list();
                entity.setObjectId(String.join(",",userIds));
                entity.setRequestObjectOld(JSONUtil.toJsonStr(list));
                String str = "用户批量延期：";
                for (IamUserEntity usersInfoById : list) {
                    str += usersInfoById.getName()+";";
                }
                // 批量移动，是否抄送acm由用户属性独立决定
                entity.setDescription(str);
            }
        }
        if(StrUtil.isNotEmpty(requestId)) {
            entity.setId(requestId);
            dao.updateSettable(IamRequestOperationEntity.class, entity);
            return entity;
        }
        return dao.insert(entity);
    }

    /**
     * 更新复核单状态信息
     * @param logId 日志id，用于作为异步线程中参数，更新发送acm状态
     */
    @Transactional(rollbackFor = Exception.class)
    public AcmResultMsgVO updateEntity(Integer status, String id, String approvalOpinion,String oper,String logId) throws Exception {
        int errorCount = 0;
        int successCount = 0;
        // 更新状态
        IamRequestOperationEntity entity = (IamRequestOperationEntity)((CriteriaQuery)this.dao.createQuery(IamRequestOperationEntity.class).id(id).forUpdate()).singleOrNull();
        entity.setStatus(status);
        entity.setApprovalOpinion(approvalOpinion);
        if("reset".equals(oper)) {
            entity.setApprovalAt(null);
            entity.setApprovalByName(null);
            entity.setApprovalBy(null);
        }
        int update = dao.updateSettable(entity);
        AcmResultMsgVO success = AcmResultMsgVO.success(null);
        if(status == 3){
            entity.setApprovalAt(new java.util.Date());
            UserInfo currentUser = Security.getCurrentUser();
            entity.setApprovalByName(currentUser.getName());
            entity.setApprovalBy(currentUser.getIdAsString());
            if("user".equals(entity.getObjectType())){
                log.info("【复核过程】用户复核");
                if(entity.getOperationType().equals("add")){
                    log.info("【复核过程】用户实体转换");
                    IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(),IamUserVo.class);
                    if(ObjectUtil.isNotEmpty(creation.getExpiredAt())) {
                        creation.setExpiredAt(creation.getExpiredAt().plusHours(23).plusMinutes(59).plusSeconds(59));
                    }
                    log.info("【复核过程】添加用户：{} ",creation.getName());
                    // 将排序的组织id放入creation的org_id中
                    creation.setOrgId(creation.getIamUserOrg().getOrgId());
                    // 其余所属组织不为空
                    if(ObjectUtil.isNotEmpty(creation.getIamUserOtherOrg())) {
                        creation.setOtherOrgIds(creation.getIamUserOtherOrg().stream().map(v -> v.getOrgId()).collect(Collectors.toList()));
                    }
                    GfIamUserCreation userCreation = iamUserManagerGF.initUser(creation);
                    // 创建用户
                    // 通知方式
                    userCreation.setNotifyType(creation.getNotifyType());
                    userCreation.setProperty("sendUsername",creation.getSendUsername());
                    log.info("【复核过程】新增用户");
                    IamUserModel user = iamUserManagerGF.createUser(userCreation);
                    log.info("【复核过程】更新用户类型");
                    iamUserManagerGF.updateUserStaffType(creation.getStaffType(),user.getId());
                    log.info("【复核过程】发送邮件");
                    sendService.send(creation.getSendUsername(),creation.getNotifyType(),user.getId(), userCreation);
                    // acm 调用
                    log.info("【复核过程】新增用户-acm调用");
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1) {
                        acmSendService.addAcmUser(false,creation,success,logId);
                    }
                    // 其余所属组织信息添加
                    log.info("【复核过程】新增用户-其余所属组织信息添加");
                    if(ObjectUtil.isNotEmpty(creation.getOtherOrgIds())&&creation.getOtherOrgIds().size() > 0){
                        for (String key : creation.getOtherOrgIds()) {
                            GfIamUserOrgEntity userOrgInfo = new GfIamUserOrgEntity();
                            userOrgInfo.setPrimary(false);
                            userOrgInfo.setUserId(user.getId());
                            userOrgInfo.setOrgId(key);
                            iamUserManager.dao().insert(GfIamUserOrgEntity.class,userOrgInfo);
                        }
                    }
                    // 向组织-用户关系表中添加关系
                    log.info("【复核过程】新增用户-主组织信息添加");
                    GfIamUserOrgEntity userOrgInfo = new GfIamUserOrgEntity();
                    userOrgInfo.setPrimary(true);
                    userOrgInfo.setUserId(user.getId());
                    userOrgInfo.setOrgId(user.getOrgId());
                    iamUserManager.dao().insert(GfIamUserOrgEntity.class,userOrgInfo);
                    // 移动用户排序
                    log.info("【复核过程】新增用户-移动用户排序");
                    IamUserOrgOrderVo iamUserOrg = creation.getIamUserOrg();
                    if(ObjectUtil.isNotEmpty(iamUserOrg)) {
                        updataUserSort(user.getId(), iamUserOrg.getOrgId(),iamUserOrg.getSortOptions(),iamUserOrg.getSortUserId(),false,true);
                    }
                    if(ObjectUtil.isNotEmpty(creation.getIamUserOtherOrg())) {
                        creation.getIamUserOtherOrg().forEach(e->{
                            this.updataUserSort(user.getId(),e.getOrgId(),e.getSortOptions(),e.getSortUserId(),false,false);
                        });
                    }
                }
                else if (entity.getOperationType().equals("update")||entity.getOperationType().equals("enaDel")) {
                    IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);
                    if(ObjectUtil.isNotEmpty(user.getExpiredAt())) {
                        user.setExpiredAt(user.getExpiredAt().plusHours(23).plusMinutes(59).plusSeconds(59));
                    }
                    log.info("【复核过程】更新用户 {}",user.getName());
                    //设置主组织与其余所属组织信息
                    log.info("【复核过程】更新用户-初始化主组织与其余所属组织信息");
                    user.setOrgId(user.getIamUserOrg().getOrgId());
                    if(ObjectUtil.isNotEmpty(user.getIamUserOtherOrg())) {
                        user.setOtherOrgIds(user.getIamUserOtherOrg().stream().map(e->e.getOrgId()).filter(s->StrUtil.isNotEmpty(s)).collect(Collectors.toList()));
                    }else{
                        user.setOtherOrgIds(new ArrayList<>());
                    }
                    IamUserVo userOld = JSONUtil.toBean(entity.getRequestObjectOld(), IamUserVo.class);
                    user.setStaffType(Arrays.asList(user.getStaffType().stream().collect(Collectors.joining("|"))));
                    log.info("【复核过程】更新用户-更新用户信息");
                    // 密码为空，说明用户没有传递新密码，将旧密码设置到更新信息中
                    IamUserEntity iamUserEntity = iamUserManager.find(entity.getObjectId());
                    if(StrUtil.isEmpty(user.getPassword())){
                        if(ObjectUtil.isNotEmpty(iamUserEntity)){
                            user.setPassword(iamUserEntity.getPassword());
                        }
                    }
                    iamUserManager.dao().update(IamUserEntity.class,entity.getObjectId(),user);
                    log.info("【复核过程】更新用户类型");
                    iamUserManagerGF.updateUserStaffType(Arrays.asList(user.getStaffType().get(0).split("\\|")),entity.getObjectId());
                    // 假如密码为空
                    if(StrUtil.isEmpty(user.getPassword())) {
                        iamUserManagerGF.clearPassword(user.getId());
                    }
                    log.info("【复核过程】更新用户-扩展ldap属性");
                    iamUserManagerGF.updateUserLdapProperties(user.getUsername());
                    // 将删除用户变为启用
                    if(entity.getOperationType().equals("enaDel")){
                        log.info("【复核过程】更新用户-启用用户");
                        iamUserManager.dao().executeUpdate("update iam_user set deleted_date = null , deleted_ = false ,enabled_ = true where id_ = ?",entity.getObjectId());
                    }
                    // 主部门信息在组织用户表信息更新
                    log.info("【复核过程】更新用户-主组织信息更新");
                    iamUserManagerGF.updateUserPrimaryOrgs(user.getOrgId(),entity.getObjectId());
                    // 其余组织信息更新
                    log.info("【复核过程】更新用户-其余组织信息更新");
                    iamUserManagerGF.updateUserOtherOrgs(user.getOtherOrgIds(),userOld.getOtherOrgIds(),entity.getObjectId());


                    // 更新排序
                    // 更新主部门排序

                    IamUserOrgOrderVo iamUserOrg = user.getIamUserOrg();
                    if(ObjectUtil.isNotEmpty(iamUserOrg)&&ObjectUtil.isNotEmpty(iamUserOrg.getSortOptions())) {
                        log.info("【复核过程】更新用户-主组织排序");
                        updataUserSort(entity.getObjectId(),user.getOrgId(), iamUserOrg.getSortOptions(),user.getIamUserOrg().getSortUserId(),false,true);
                    }
                    // 更新其余所属组织排序
                    if(ObjectUtil.isNotEmpty(user.getIamUserOtherOrg())) {
                        log.info("【复核过程】更新用户-其余所属组织排序");
                        for (IamUserOrgOrderVo orderVo : user.getIamUserOtherOrg()) {
                            if(ObjectUtil.isNotEmpty(orderVo.getSortOptions())) {
                                updataUserSort(entity.getObjectId(),orderVo.getOrgId(),orderVo.getSortOptions(), orderVo.getSortUserId(), false,false);
                            }
                        }
                    }
                    // acm 异步调用
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1) {
                        if(entity.getOperationType().equals("enaDel")) {
                            log.info("【复核过程】启用用户-发送acm");
                            acmSendService.enableAcmUser(false, userOld.getAcmUserId(),user,success,logId);
                        } else if(entity.getOperationType().equals("update")) {
                            log.info("【复核过程】更新用户-发送acm");
                            acmSendService.updateAcmUser(false,user,success,logId);
                        }
                    }
                    // 更新密码
                    // acm 异步调用
                    if (StrUtil.isNotEmpty(user.getPassword()) && !user.getPassword().startsWith("{bcrypt}")
                            && !user.getPassword().startsWith("{MD5-BASE64}")) {
                        log.info("【复核过程】更新用户{}-更新密码",user.getName());
                        if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1) {
                            log.info("【复核过程】更新用户{}-更新密码(发送oa、ad)", user.getName());
                            iamUserPasswordManager.resetPassword(user.getUsername(), user.getPassword(),logId,success);
                        }else {
                            log.info("【复核过程】更新用户{}-更新密码(不发送oa、ad)", user.getName());
                            passwordManager.updatePassword(entity.getObjectId(),user.getPassword());
                        }
                    }
                }
                else if (entity.getOperationType().equals("delete")) {
                    IamUserEntity iamUserVo = iamUserManager.find(entity.getObjectId());
                    log.info("【复核过程】删除用户{}",iamUserVo.getName());
                    SimpleDateFormat formatter= new SimpleDateFormat("yyyyMMdd");
                    long date1 = System.currentTimeMillis();
                    Date date = new Date(date1);
                    log.info("【复核过程】删除用户-更新用户信息");
                    iamUserManager.dao().executeUpdate("update iam_user set deleted_date = ? , name_ = ? , enabled_ = false where id_ = ?",date,(iamUserVo.getName()+ formatter.format(date)),entity.getObjectId());
                    iamUserManager.dao().softDelete(IamUserEntity.class,entity.getObjectId());
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1){
                        log.info("【复核过程】删除用户-发送acm");
                        acmSendService.deleteAcmUser(true,(String) iamUserVo.getProperty("acmUserId"),null,logId);
                    }
                }
                else if (entity.getOperationType().equals("move")) {
                    IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);
                    log.info("【复核过程】移动用户：{}",user.getName());
                    // 包括判断同步acm
                    updataUserSort(entity.getObjectId(),user.getOrgId(),user.getSortOptions(),user.getSortUserId(),true,true);
                    IamUserEntity iamUserEntity = iamUserManager.find(entity.getObjectId());
                    String acmUserId = (String) iamUserEntity.getProperty("acmUserId");
                    String username = iamUserEntity.getUsername();
                    user.setAcmUserId(acmUserId);
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1){
                        log.info("【复核过程】移动用户-发送acm",user.getName());
                        acmSendService.moveAcmUser(false,user,success,logId);
                    }
                    log.info("【复核过程】移动用户-扩展ldap属性");
                    iamUserManagerGF.updateUserLdapProperties(username);
                }
                else if(entity.getOperationType().equals("disable")){
                    String userId = entity.getObjectId();
                    IamUserVo userEntity = iamUserManager.find(IamUserVo.class,userId);
                    log.info("【复核过程】禁用用户：{}",userEntity.getName());
                    userEntity.setEnabled(false);
                    iamUserManager.dao().update(IamUserEntity.class,userId,userEntity);
                    // acm 同步(禁用不同步）
//                    if(StrUtil.isNotEmpty(userEntity.getAcmUserId())){
//                        userAsyncManager.disableAcmUser(userEntity.getAcmUserId(),null);
//                        AcmUserEmpDto acmUserEmpDto = new AcmUserEmpDto();
//                        acmUserEmpDto.setId(userEntity.getAcmUserId());
//                        AcmResultMsgVO acmResultMsgVO = acmApiService.removeEmp(acmUserEmpDto);
//                        syncDataResultVo.set(makeSyncDateResultVo(null, acmResultMsgVO));
//                    }
                }
                else if(entity.getOperationType().equals("enable")){
                    String userId = entity.getObjectId();
                    IamUserVo userEntity = iamUserManager.find(IamUserVo.class,userId);
                    log.info("【复核过程】启用用户：{}",userEntity.getName());
                    userEntity.setEnabled(true);
                    iamUserManager.dao().update(IamUserEntity.class,userId,userEntity);

//                    // acm 同步(启用不同步）
//                    if(StrUtil.isNotEmpty(userEntity.getAcmUserId())){
//                        userAsyncManager.enableAcmUser(userEntity.getAcmUserId(),null);
//                    }
                }
                else if(entity.getOperationType().equals("updatePass")){
                    IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);

                    String password = user.getPassword();
                    String userId = entity.getObjectId();
//                    boolean b = passwordManager.updatePassword(userId, password);
//                    if(!b) {
//                        throw new RuntimeException("更新密码失败");
//                    }
                    IamUserEntity iamUserEntity = iamUserManager.find(userId);
                    // 发送oa、ad
                    if(entity.getAcmFlag()==1) {
                        log.info("【复核过程】更新用户密码（发送ad，oa）：{}",user.getName());
                        iamUserPasswordManager.resetPassword(iamUserEntity.getUsername(), password, logId, success);
                    }else {
                        log.info("【复核过程】更新用户密码（不发送ad，oa）：{}",user.getName());
                        passwordManager.updatePassword(entity.getObjectId(),password);
                    }
                }
                else if(entity.getOperationType().equals("resetPass")){
                    log.info("【复核过程】重置用户密码");
                    String userId = entity.getObjectId();
                    IamUserEntity iamUserEntity = iamUserManager.find(userId);
                    boolean b = iamUserManagerGF.clearPassword(userId);
                    log.info("【复核过程】重置用户{}密码成功",iamUserEntity.getName());
                    if(!b) {
                        throw new RuntimeException("置空"+iamUserEntity.getName()+"密码失败");
                    }
                }
                else if(entity.getOperationType().equals("batchAD")){
                    UserBatchAdVo userBatchAdVo = JSONUtil.toBean(entity.getRequestObject(), UserBatchAdVo.class);

                    for (IamUserVo iamUserVo : userBatchAdVo.getUserList()) {
                        AcmResultMsgVO iamUserSendAcm = createIamUserSendAcm(iamUserVo, success, logId);
                        if(iamUserSendAcm.getCode()==1){
                            errorCount++;
                        }else {
                            successCount++;
                        }
                    }
                    // 最后在log前后加上[]
                    dao().executeUpdate("update iam_request_operation_log \n" +
                            "set request_obj = concat('[',request_obj,']')\n" +
                            "where id_ = ?",logId);
                }
            }
            else if("org".equals(entity.getObjectType())){
                if(entity.getOperationType().equals("add")){
                    // 从请求体中获取id
                    IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(),IamOrgVo.class);
                    // 初始化
                    IamOrgCreation orgCreation = iamOrgManagerGF.initOrg(creation);
                    log.info("【复核过程】添加组织信息{}",orgCreation.getName());
                    // 创建组织
                    IamOrgEntity iamOrgEntity = iamOrgManagerGF.create(orgCreation);
                    creation.setId(iamOrgEntity.getId());
                    // 异步发送acm
                    if(ObjectUtil.isNotEmpty(creation.getAcmFlag())&&creation.getAcmFlag() == 1) {
                        log.info("【复核过程】发送acm");
                        acmSendService.addAcmOrg(false,creation,success,logId);
                    }
                    log.info("【复核过程】添加用户成员");
                    List<String> member = creation.getMember();
                    // 用户成员
                    if(ObjectUtil.isNotEmpty(member)) {
                        gfIamUserOrgManager.addUserOrg(member,iamOrgEntity.getId(),false);
                    }
                    log.info("【复核过程】更新组织bindDN");
                    iamOrgManagerGF.updateLdapBindDn(iamOrgEntity.getId());

                    log.info("【复核过程】添加组织成员");
                    List<String> groupMember = creation.getGroupMember();
                    // 组织成员
                    if(ObjectUtil.isNotEmpty(groupMember)) {
                        iamOrgManagerGF.updateOrgGroup(null, groupMember,iamOrgEntity.getId());
                    }
                    log.info("【复核过程】更新所有组织下（包括子组织）的用户的bindDn");
                    iamOrgManagerGF.updateOrgMemberBindDn(iamOrgEntity.getId());
                    log.info("【复核过程】更新组织ad排序号");
                    if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&StrUtil.isNotEmpty(creation.getParentOrg().getSortOptions())){
                        iamOrgManagerGF.updateOrgADOrder(creation.getParentOrg().getSortOptions(),creation.getParentOrg().getSortOrgId(),iamOrgEntity.getId(),iamOrgEntity.getParentId());
                    }
                } else if (entity.getOperationType().equals("update")) {
                    IamOrgVo orgVoOld = JSONUtil.toBean(entity.getRequestObjectOld(), IamOrgVo.class);
                    IamOrgVo orgVo = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                    log.info("【复核过程】{} 更新组织信息",orgVo.getName());
                    iamOrgManager.dao().updateSettable(IamOrgEntity.class,orgVo);
                    // 移动组织，并且修改code
                    if(ObjectUtil.isNotEmpty(orgVo.getParentOrg())&&!StrUtil.equals(orgVoOld.getParentId(),orgVo.getParentOrg().getOrgId())) {
                        iamOrgManagerGF.transferOrg(entity.getObjectId(),orgVo.getParentOrg().getOrgId());
                    }
                    log.info("【复核过程】更新用户成员");
                    //更新用户成员
                    iamOrgManagerGF.updateOrgMember(orgVoOld.getMember(),orgVo.getMember(),entity.getObjectId());
                    log.info("【复核过程】更新下属组织");
                    // 移除旧下属组织
                    iamOrgManagerGF.updateOrgGroup(orgVoOld.getGroupMember(),orgVo.getGroupMember(),entity.getObjectId());
                    log.info("【复核过程】更新组织bindDN");
                    iamOrgManagerGF.updateLdapBindDn(entity.getObjectId());
                    log.info("【复核过程】更新组织下所有用户的bindDn");
                    iamOrgManagerGF.updateOrgMemberBindDn(entity.getObjectId());
                    // acm 异步调用
                    if(orgVo.getAcmFlag()==1){
                        log.info("【复核过程】发送acm");
                        acmSendService.updateAcmOrg(false,orgVo,"update",success,logId);
                    }
                    if(ObjectUtil.isNotEmpty(orgVo.getParentOrg())&&StrUtil.isNotEmpty(orgVo.getParentOrg().getSortOptions())){
                        iamOrgManagerGF.updateOrgADOrder(orgVo.getParentOrg().getSortOptions(),orgVo.getParentOrg().getSortOrgId(),orgVo.getId(),orgVo.getParentOrg().getOrgId());
                    }
                }else if("move".equals(entity.getOperationType())){
                    IamOrgVo newOrgVo = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                    log.info("【复核过程】{} 移动组织",newOrgVo.getName());
                    IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, entity.getObjectId());
                    iamOrgVo.setParentId(newOrgVo.getParentId());
                    iamOrgManager.dao().update(IamOrgEntity.class,entity.getObjectId(),iamOrgVo);
                    // 移动组织，并且修改code,bindDn
                    iamOrgManagerGF.transferOrg(iamOrgVo.getId(),newOrgVo.getParentId());
                    log.info("【复核过程】更新组织下所有用户的bindDn");
                    iamOrgManagerGF.updateOrgMemberBindDn(entity.getObjectId());
                    // acm 异步调用
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1){
                        log.info("【复核过程】发送acm");
                        acmSendService.updateAcmOrg(false,iamOrgVo,"move",success,logId);
                    }
                } else{
//                    IamOrgVo orgVo = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                    iamOrgManager.softDelete(entity.getObjectId());
                    IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, entity.getObjectId());
                    // acm 调用
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1){
                        acmSendService.removeOrg(false,iamOrgVo, iamOrgVo.getSysType(),success,logId);
                    }
                }
            }
            else if("users".equals(entity.getObjectType())){
                if(entity.getOperationType().equals("move")){
                    log.info("【复核过程】用户批量移动");
                    IamUsersMoveVo iamUsersMoveVo = JSONUtil.toBean(entity.getRequestObject(),IamUsersMoveVo.class);
                    String orgId = iamUsersMoveVo.getOrgId();
                    log.info("【复核过程】获取用户ids");
                    // userIds 新加进来的用户
                    List<String> userIds = iamUserManagerGF.getUserIdByOaNameOrUsernames(iamUsersMoveVo.getUserNames());
                    // 更新主部门
                    // 更新组织-用户表
                    log.info("【复核过程】更新组织-用户表");
                    gfIamUserOrgManager.moveUsers(userIds,orgId);
                    log.info("【复核过程】更新用户的bindDN");
                    iamUserManagerGF.updateBindDnByIds(userIds);
                    // 修改排列
                    String userNewOANames = iamUsersMoveVo.getUserNewOANames();
                    List<String> allUserOANames = Arrays.asList(userNewOANames.split("\n"));
                    List<String> allUserIds = iamUserManagerGF.getUserIdByOaNameOrUsernames(allUserOANames);
                    AcmResultMsgVO syncDataResultVo1 = iamUserManagerGF.changeOrder(allUserIds,orgId);
                    // 发送acm --如果是修改主部门还需要发送更新用户信息的到acm
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1) {
                        acmSendService.updateAcmUsersSort(false,orgId, allUserIds, success,logId);
                    }
                }else if (entity.getOperationType().equals("delay")) {
                    IamUserDelayVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserDelayVo.class);

                    List<String> userIds = user.getUserIds();
                    LocalDateTime expireAt = user.getExpireAt().plusHours(23).plusMinutes(59).plusSeconds(59);
                    iamUserManager.createQuery().in("id_","id_",userIds).update("expiredAt",expireAt);
                    // 包括判断同步acm
                    // acm 调用
                    if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1){
                        acmSendService.updateUserDelayTime(false,userIds,expireAt,success,logId);
                    }
                }else if (entity.getOperationType().equals("impDelay")) {
                    IamUserImportDelayDto user = JSONUtil.toBean(entity.getRequestObject(), IamUserImportDelayDto.class);
                    List<IamUserEntity> userList = user.getUserList();
                    for (IamUserEntity iamUserEntity : userList) {
                        Integer acmFlag = (Integer) iamUserEntity.getProperty("acmFlag");
                        LocalDateTime expireAt = iamUserEntity.getExpiredAt().plusHours(23).plusMinutes(59).plusSeconds(59);
                        if(ObjectUtil.isNotEmpty(acmFlag)&&acmFlag==1){
                            acmSendService.updateUserDelayTime(false,Arrays.asList(iamUserEntity.getId()), expireAt,success,logId);
                        }
                        iamUserManager.createQuery().where("id_ = ?",iamUserEntity.getId()).update("expiredAt",expireAt);
                    }
                    // 包括判断同步acm
                    // acm 调用

                }
            }
        }
        if(success.getCode()==1&&entity.getOperationType().equals("batchAD")){
            // 批量发送AD用户的部分单独处理
            String msg = "批量发送AD用户,"+successCount+"成功,"+errorCount+"失败，错误信息："+success.getMessage();
            return AcmResultMsgVO.error(msg);
        }else if(success.getCode()==1){
            throw new RuntimeException(success.getMessage());
        }
        return AcmResultMsgVO.success(null);
    }

    public AcmResultMsgVO createIamUserSendAcm(IamUserVo e,AcmResultMsgVO success,String logId) {
        Object savePoint = TransactionAspectSupport.currentTransactionStatus().createSavepoint();
        e.setStaffType(Arrays.asList("AD"));
        log.info("【批量添加AD用户】初始化基本信息：{}",e.getName());
        GfIamUserCreation creation = iamUserManagerGF.initUser(e);
        // 创建用户
        log.info("【批量添加AD用户】创建用户");
        IamUserModel user = iamUserManagerGF.createUser(creation);
        AcmResultMsgVO acmResultMsgVO = AcmResultMsgVO.success(null);
        if(e.getAcmFlag() == 1) {
            log.info("【批量添加AD用户】发送acm");
            acmResultMsgVO = acmSendService.addAcmUser(false, e, success, logId);
//                            userAsyncManager.addAcmUser(e,null,logId);
        }
        // 其余所属组织信息添加
        if(ObjectUtil.isNotEmpty(e.getOtherOrgIds())&&e.getOtherOrgIds().size() > 0){
            log.info("【批量添加AD用户】添加其余所属组织信息");
            for (String key : e.getOtherOrgIds()) {
                GfIamUserOrgEntity userOrgInfo = new GfIamUserOrgEntity();
                userOrgInfo.setPrimary(false);
                userOrgInfo.setUserId(user.getId());
                userOrgInfo.setOrgId(key);
                iamUserManager.dao().insert(GfIamUserOrgEntity.class,userOrgInfo);
            }
        }
        // 向组织-用户关系表中添加关系
        log.info("【批量添加AD用户】添加主组织信息");
        GfIamUserOrgEntity userOrgInfo = new GfIamUserOrgEntity();
        userOrgInfo.setPrimary(true);
        userOrgInfo.setUserId(user.getId());
        userOrgInfo.setOrgId(user.getOrgId());
        iamUserManager.dao().insert(GfIamUserOrgEntity.class,userOrgInfo);
        log.info("【复核过程】新增用户-扩展ldap属性");
        iamUserManagerGF.updateUserLdapProperties(user.getUsername());
        if(acmResultMsgVO.getCode()==1){
            log.info("【复核过程】回滚新增{}用户",user.getUsername());
            TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savePoint);
        }
        return acmResultMsgVO;
    }


    public IamRequestOperationVo getDetail(String id)  {
        IamRequestOperationEntity entity = dao.find(IamRequestOperationEntity.class,id);
        IamRequestOperationVo result = new IamRequestOperationVo();
        BeanUtils.copyProperties(entity,result);
        IamUserVo iamUser = new IamUserVo();
        IamOrgVo iamOrg = new IamOrgVo();
        List<String> oldList = new ArrayList<>();
        List<String> newList = new ArrayList<>();
        if("updatePass".equals(entity.getOperationType())) {
            oldList.add("修改密码");
            newList.add("修改密码:******");
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else if("resetPass".equals(entity.getOperationType())) {
            oldList.add("置空密码");
            newList.add("置空密码");
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else if("disable".equals(entity.getOperationType())) {
            oldList.add("启用");
            newList.add("禁用");
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else if("enable".equals(entity.getOperationType())){
            oldList.add("禁用");
            newList.add("启用");
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else if("batchAD".equals(entity.getOperationType())){
            UserBatchAdVo creation = JSONUtil.toBean(entity.getRequestObject(),UserBatchAdVo.class);
            result.setUserBatchAdVo(creation);
        }else if("impDelay".equals(entity.getOperationType())){
            // 新添加的用户列表
            IamUserImportDelayDto moveVo = JSONUtil.toBean(entity.getRequestObject(),IamUserImportDelayDto.class);
            List<IamUserEntity> userEntityList = moveVo.getUserList();
            List<IamUserEntity> oldUserEntity = JSONObject.parseArray(entity.getRequestObjectOld(), IamUserEntity.class);
            for (int i = 0; i < userEntityList.size(); i++) {
                IamUserEntity user = userEntityList.get(i);
                LocalDate expiredAt = ObjectUtil.isNotEmpty(user.getExpiredAt())?user.getExpiredAt().toLocalDate():null;
                LocalDate oldExpire = ObjectUtil.isNotEmpty(oldUserEntity.get(i).getExpiredAt())?oldUserEntity.get(i).getExpiredAt().toLocalDate():null;
                oldList.add("["+user.getName()+"] 过期时间:"+ (ObjectUtil.isNotEmpty(oldExpire)?oldExpire.toString():""));
                newList.add("["+user.getName()+"] 过期时间:"+(ObjectUtil.isNotEmpty(expiredAt)?expiredAt.toString():""));
            }
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else if("delay".equals(entity.getOperationType())){
            // 新添加的用户列表
            IamUserDelayVo moveVo = JSONUtil.toBean(entity.getRequestObject(),IamUserDelayVo.class);
            List<String> addUserIds = moveVo.getUserIds();
            LocalDate expireTime = moveVo.getExpireAt().toLocalDate();
            List<IamUserEntity> list = JSONObject.parseArray(entity.getRequestObjectOld(), IamUserEntity.class);

            for (IamUserEntity user : list) {
                LocalDateTime expiredAt = user.getExpiredAt();
                oldList.add("["+user.getName()+"] 过期时间:"+(ObjectUtil.isNotEmpty(expiredAt)?expiredAt.toLocalDate().toString():""));
                newList.add("["+user.getName()+"] 过期时间:"+(ObjectUtil.isNotEmpty(expireTime)?expireTime.toString():""));
            }
            result.setOldStr(oldList);
            result.setNewStr(newList);
        } else if("add".equals(entity.getOperationType())){
            if(entity.getObjectType().equals("org")){
                IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(),IamOrgVo.class);
                result.setNewIamOrg(creation);
            }else{
                IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(),IamUserVo.class);
                creation.setOrgId(creation.getIamUserOrg().getOrgId());
                // 其余所属组织为空的情况
                if(ObjectUtil.isNotEmpty(creation.getIamUserOtherOrg())) {
                    creation.setOtherOrgIds(creation.getIamUserOtherOrg().stream().map(v->v.getOrgId()).collect(Collectors.toList()));
                }
//                IamUserOrgOrderVo iamUserOrg = creation.getIamUserOrg();
//                // 将用户id翻译为名称回显
//                List<IamUserEntity> userOrg = iamUserManagerGF.getInfo(iamUserOrg.getUserId(),iamUserOrg.getOrgId());
//                creation.getIamUserOrg().setUserNames(userOrg);
//                List<IamUserOrgOrderVo> iamUserOtherOrg = creation.getIamUserOtherOrg();
//                if(ObjectUtil.isNotEmpty(iamUserOtherOrg))
//                    iamUserOtherOrg.forEach(e->{
//                        List<IamUserEntity> info = iamUserManagerGF.getInfo(e.getUserId(), e.getOrgId());
//                        e.setUserNames(info);
//                    });
                result.setNewIamUser(creation);
            }
        // 移动类型
        }else if(entity.getOperationType().equals("move")) {
            if(entity.getObjectType().equals("org")){
                IamOrgVo oldOrg = JSONUtil.toBean(entity.getRequestObjectOld(),IamOrgVo.class);
                IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(),IamOrgVo.class);
                IamOrgVo oldIamOrg = new IamOrgVo();
                if(isFlag(oldOrg.getParentId(),creation.getParentId())){
                    // 获取旧上级组织名称
                    List<IamOrgVo> list = dao.createSQLQuery("select name_ from iam_org where id_ = ? ", IamOrgVo.class).args(oldOrg.getParentId()).list();
                    String oldOrgName = (ObjectUtil.isEmpty(list)?"":list.get(0).getName());
                    // 获取新上级组织名称
                    List<IamOrgVo> newOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamOrgVo.class).args(creation.getParentId()).list();
                    String newOrgName = (ObjectUtil.isEmpty(newOrg)?"":newOrg.get(0).getName());
                    iamOrg.setParentId(creation.getParentId());
                    oldIamOrg.setParentId(oldOrg.getParentId());
                    oldList.add("上级组织："+(ObjectUtil.isNotEmpty(oldOrgName)?oldOrgName:""));
                    newList.add("上级组织："+(ObjectUtil.isNotEmpty(newOrgName)?newOrgName:""));

                }
                result.setNewIamOrg(iamOrg);
                result.setOldIamOrg(oldIamOrg);
            }else if(entity.getObjectType().equals("user")){
                IamUserEntity userEntity = iamUserManager.find(entity.getObjectId());
                IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(),IamUserVo.class);
                IamUserVo oldIamUser = new IamUserVo();
                if(isFlag(userEntity.getOrgId(),creation.getOrgId())){
                    // 获取旧组织名称
                    String oldOrgName = "";
                    if(StrUtil.isNotEmpty(userEntity.getOrgId())){
                        List<IamOrgVo> oldOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ", IamOrgVo.class).args(userEntity.getOrgId()).list();
                        oldOrgName = ObjectUtil.isNotEmpty(oldOrg)?oldOrg.get(0).getName():"";
                    }
                    // 获取新组织名称
                    SQLQuery<IamOrgVo> newOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamOrgVo.class).args(creation.getOrgId());
                    String newOrgName = ObjectUtil.isNotEmpty(newOrg)?newOrg.first().getName():"";
                    oldIamUser.setOrgId(userEntity.getOrgId());
                    iamUser.setOrgId(creation.getOrgId());
                    oldList.add("所属组织："+(ObjectUtil.isNotEmpty(oldOrgName)?oldOrgName:""));
                    newList.add("所属组织："+(ObjectUtil.isNotEmpty(newOrgName)?newOrgName:""));
                }
                if(ObjectUtil.isNotNull(creation.getSortOptions())||StrUtil.isNotEmpty(creation.getSortUserId())){
                    oldList.add("排序");
                    if(creation.getSortOptions()==2) {
                        IamUserEntity user = iamUserManager.find(creation.getSortUserId());
                        newList.add("排序：排在" + user.getName() + "之后");
                    }
                    else if(creation.getSortOptions()==0) {
                        newList.add("排序：排在最前");
                    } else {
                        newList.add("排序：排在最后");
                    }
                }
                result.setNewIamUser(iamUser);
                result.setOldIamUser(oldIamUser);
            }else if(entity.getObjectType().equals("users")){
                // 新添加的用户列表
                IamUsersMoveVo moveVo = JSONUtil.toBean(entity.getRequestObject(),IamUsersMoveVo.class);
                result.setIamUsersMoveVo(moveVo);
                List<String> addUserIds = moveVo.getAddUserIds();
                List<IamUserEntity> userDetails = JSONObject.parseArray(entity.getRequestObjectOld(),IamUserEntity.class);
                // 新组织id
                String orgId = moveVo.getOrgId();
                String newOrgNames= dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamOrgVo.class).args(orgId).first().getName();
                Boolean moveMainDepartment = moveVo.getMoveMainDepartment();
                for (IamUserEntity user : userDetails) {
//                    String string = user.getProperty("orgName","");
                    oldList.add("["+user.getName()+"] 添加其余所属组织");
                    newList.add("["+user.getName()+"] 添加其余所属组织:"+(ObjectUtil.isNotEmpty(newOrgNames)?newOrgNames:""));
                }
                // 排序区别
                oldList.add("排序:\n"+moveVo.getUserOldOANames());
                newList.add("排序：\n"+moveVo.getUserNewOANames());
            }
            result.setOldStr(oldList);
            result.setNewStr(newList);
        } else if (entity.getOperationType().equals("update")||entity.getOperationType().equals("enaDel")) {
            if(entity.getObjectType().equals("org")){
                IamOrgVo orgEntity = JSONUtil.toBean(entity.getRequestObjectOld(), IamOrgVo.class);
                IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(),IamOrgVo.class);
                iamOrg = creation;
                IamOrgVo oldIamOrg = new IamOrgVo();
                //当都不为空或者有一个为空时需要返回
                if(isFlag(orgEntity.getName(),creation.getName())){
                    makeDiffList(oldList,newList,"组织名称：",orgEntity.getName(),"组织名称：",creation.getName());
                }
                if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&isFlag(orgEntity.getParentId(),creation.getParentOrg().getOrgId())){
                    // 获取旧上级组织名称
                    String oldOrgName = "";
                    // 某些可能没有上级组织
                    if(StrUtil.isNotEmpty(orgEntity.getParentId())){
                        SQLQuery<IamOrgVo> oldOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamOrgVo.class).args(orgEntity.getParentId());
                        oldOrgName = oldOrg.first().getName();
                    }
                    // 获取新上级组织名称
                    String newOrgName = "";
                    if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&ObjectUtil.isNotEmpty(creation.getParentOrg().getOrgId())){
                        SQLQuery<IamOrgVo> newOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamOrgVo.class).args(creation.getParentOrg().getOrgId());
                        newOrgName = newOrg.first().getName();
                    }
                    makeDiffList(oldList,newList,"上级组织：",oldOrgName,"上级组织：",newOrgName);
                }
                // 排序信息比较
                if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&ObjectUtil.isNotEmpty(creation.getParentOrg().getSortOptions())){
                    oldList.add("排序");
                    if("2".equals(creation.getParentOrg().getSortOptions())) {
                        IamOrgEntity org = iamOrgManager.find(creation.getParentOrg().getSortOrgId());
                        newList.add("排序：排在" + org.getName() + "之后");
                    }
                    else if("0".equals(creation.getParentOrg().getSortOptions())) {
                        newList.add("排序：排在最前");
                    } else {
                        newList.add("排序：排在最后");
                    }
                }
                if(isFlag(orgEntity.getEmail(),creation.getEmail())){
                    makeDiffList(oldList,newList,"邮箱：",orgEntity.getEmail(),"邮箱：",creation.getEmail());
                }
                if(isFlag(orgEntity.getSortOrder2(),creation.getSortOrder2())){
                    makeDiffList(oldList,newList,"序号：",orgEntity.getSortOrder2(),"序号：",creation.getSortOrder2());
                }
                if(isFlag(orgEntity.getSimpleSpell(),creation.getSimpleSpell())){
                    makeDiffList(oldList,newList,"组账户名：", orgEntity.getSimpleSpell(),"组账户名：",""+creation.getSimpleSpell());
                }
                if(isFlag(orgEntity.getOaSimpleSpell(),creation.getOaSimpleSpell())){
                    makeDiffList(oldList,newList,"OA组简拼：", orgEntity.getOaSimpleSpell(),"OA组简拼：",creation.getOaSimpleSpell());
                }
                if(isFlag(orgEntity.getSelfBusinessCode(),creation.getSelfBusinessCode())){
                    makeDiffList(oldList,newList,"上级业务代码：", orgEntity.getSelfBusinessCode(),"上级业务代码：",creation.getSelfBusinessCode());
                }
                if(isFlag(orgEntity.getEmailInternet(),creation.getEmailInternet())){
                    makeDiffList(oldList,newList,"因特网地址：", orgEntity.getEmailInternet(),"因特网地址：",creation.getEmailInternet());
                }
                if(isFlag(orgEntity.getOtherEmail(),creation.getOtherEmail())){
                    makeDiffList(oldList,newList,"其他邮箱地址：",orgEntity.getOtherEmail(),"其他邮箱地址：",creation.getOtherEmail());
                }
                if(isFlag(orgEntity.getEmailDomain(),creation.getEmailDomain())){
                    makeDiffList(oldList,newList,"邮件域：",orgEntity.getEmailDomain(),"邮件域：",creation.getEmailDomain());
                }
                if(isFlag(orgEntity.getDeptType(),creation.getDeptType())){
                    String oldDeptType = orgEntity.getDeptType();
                    String newDeptType = creation.getDeptType();
                    makeDiffList(oldList,newList,"部门类型：",(StrUtil.isEmpty(oldDeptType)?"":DeptType.findEnumByValue(oldDeptType).getTitle()),"部门类型：",(StrUtil.isEmpty(newDeptType)?"":DeptType.findEnumByValue(newDeptType).getTitle()));
                }
                if(isFlag(orgEntity.getGroupType(),creation.getGroupType())){
                    String oldGroupType = orgEntity.getGroupType();
                    String newGroupType = creation.getGroupType();
                    makeDiffList(oldList,newList,"群组类型：",(StrUtil.isEmpty(oldGroupType)?"": GroupType.findEnumByValue(oldGroupType).getTitle()),"群组类型：",(StrUtil.isEmpty(newGroupType)?"": GroupType.findEnumByValue(newGroupType).getTitle()));
                }
                if(isFlag(orgEntity.getManager(),creation.getManager())){
                    makeDiffList(oldList,newList,"上级经理：",orgEntity.getManager(),"上级经理：",creation.getManager());
                }
                if(isFlag(orgEntity.getDescription(),creation.getDescription())){
                    makeDiffList(oldList,newList,"组织描述：",orgEntity.getDescription(),"组织描述：",creation.getDescription());
                }

                // 比较前后组织成员是否改变
                // 旧 iam_request_operation 中的 request_object_old
                List<String> oldUserIdList = orgEntity.getMember();
                List<IamUserEntity> oldUserList = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(oldUserIdList)) {
                    oldUserList = iamUserManagerGF.getUserListByOrder(orgEntity.getId(), oldUserIdList);
                }
//                List<IamUserEntity> oldUserList = iamUserManagerGF.getUserListByOrder(orgEntity.getId(), orgEntity.getMember());

//                List<String> oldUserIdList = gfIamUserOrgManager.createQuery().filter("orgId", orgEntity.getId()).orderBy("oa_order desc").list().stream().map(e -> e.getUserId()).collect(Collectors.toList());
//                List<IamUserEntity> oldUserList = new ArrayList<>();
                if(ObjectUtil.isNotEmpty(oldUserIdList)) {
                    oldUserList = iamUserManagerGF.getUserListByOrder(orgEntity.getId(), oldUserIdList);
                }
                Map<String,String> oldUserMap = oldUserList.stream().collect(Collectors.toMap(IamUserEntity::getId,IamUserEntity::getName));
                long oldSize = ObjectUtil.isEmpty(oldUserList) ? 0 : oldUserList.size();
                // 新
                List<String> newMemberIds = creation.getMember();
                long newSize = ObjectUtil.isEmpty(newMemberIds) ? 0 : newMemberIds.size();
                // 如果旧的大小==新的大小，并且新的id都在旧的中则不变
                boolean b = ObjectUtil.isNotEmpty(oldUserList)&& ObjectUtil.isNotEmpty(newMemberIds)?newMemberIds.stream().allMatch(i -> oldUserMap.containsKey(i)):true;
                // 判断排序是否变更
                if(CollectionUtil.isNotEmpty(newMemberIds)) {
                    for (int i = 0; i < newMemberIds.size(); i++) {
                        if (i < oldUserList.size() && !oldUserList.get(i).getId().equals(newMemberIds.get(i))) {
                            b = false;
                        }
                    }
                }
                if(!b || oldSize!=newSize)  {
                    // 旧的用户成员的名称
                    String oldUserNames = oldUserList.stream().map(e->e.getName()).collect(Collectors.toList()).toString();
                    // 查询新的用户成员的名称
                    String newUserNames = "";
                    if(ObjectUtil.isNotEmpty(creation.getMember())) {
                        List<IamUserEntity> newUserList = iamUserManagerGF.getUserListByOrder(orgEntity.getId(), creation.getMember());
                        newUserNames = newUserList.stream().map(e -> e.getName()).collect(Collectors.toList()).toString();
                    }
//                    oldIamOrg.setMember(new ArrayList<>(oldUserMap.keySet()));
//                    iamOrg.setMember(creation.getMember());
                    newList.add("用户成员：" + newUserNames);
                    oldList.add("用户成员：" + oldUserNames);
                }

                // 比较前后群组成员的变化
                // 旧
                QueryResult<IamOrgEntity> oldOrgChildQuery = iamOrgManager.dao().createQuery(IamOrgEntity.class)
                        .where("#parent_id = ?", orgEntity.getId())
                        .where("#deleted_ = ?", false)
                        .execute();
                Map<String, String> oldOrgGroup = oldOrgChildQuery.list().stream().collect(Collectors.toMap(IamOrgEntity::getId, IamOrgEntity::getName));
                ArrayList<String> oldGroupIds = new ArrayList<>(oldOrgGroup.keySet());
                long oldGroupSize = ObjectUtil.isNotEmpty(oldGroupIds)?oldGroupIds.size():0;
                // 新
                List<String> newGroupIds = creation.getGroupMember();
                int newGroupSize = ObjectUtil.isNotEmpty(newGroupIds)?newGroupIds.size():0;
                // 是否旧的id都在新的id中
                boolean contain = true;
                if(ObjectUtil.isNotEmpty(newGroupIds)) {
                    contain = newGroupIds.stream().allMatch(i -> oldOrgGroup.containsKey(i));
                }
                // 如果大小不同或者不包含在其中
                if(!contain || newGroupSize!=oldGroupSize){
                    // 旧的子群组名称
                    String oldGroupNames = oldOrgGroup.values().toString();
                    // 新的子群组名称
                    String newGroupNames = "";
                    if(ObjectUtil.isNotEmpty(newGroupIds)){
                        String join = String.join("','", newGroupIds);
                        QueryResult<IamOrgEntity> query = iamOrgManager.dao().createQuery(IamOrgEntity.class)
                                .where("id_ in ('" + join + "')")
                                .where("deleted_ = false")
                                .execute();
                        newGroupNames = query.list().stream().map(e -> e.getName()).collect(Collectors.toList()).toString();
                    }
//                    oldIamOrg.setGroupMember(oldGroupIds);
//                    iamOrg.setGroupMember(creation.getGroupMember());
                    newList.add("群组成员：" + newGroupNames);
                    oldList.add("群组成员：" + oldGroupNames);
                }
                result.setNewIamOrg(iamOrg);
                result.setOldIamOrg(oldIamOrg);
            }else{
                IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(),IamUserVo.class);
                IamUserVo userEntity = JSONUtil.toBean(entity.getRequestObjectOld(),IamUserVo.class);
                iamUser = creation;
                if(entity.getOperationType().equals("enaDel")){
//                    oldIamUser.setEnabled(false);
//                    iamUser.setEnabled(true);
                    makeDiffList(oldList,newList,"启用注销用户:",userEntity.getName(),"","");
                }
                if(isFlag(userEntity.getName(),creation.getName())){
//                    oldIamUser.setName(userEntity.getName());
//                    iamUser.setName(creation.getName());
                    makeDiffList(oldList,newList,"用户名称:",userEntity.getName(),"用户名称:",creation.getName());
                }

                if(isFlag(userEntity.getEmail(),creation.getEmail())){
//                    oldIamUser.setEmail(userEntity.getEmail());
//                    iamUser.setEmail(creation.getEmail());
                    makeDiffList(oldList,newList,"邮箱:",userEntity.getEmail(),"邮箱:",creation.getEmail());
                }
//                if(isFlag(userEntity.getOrgId(),creation.getOrgId())){
//                    // 获取旧组织名称
//                    SQLQuery<IamUserVo> oldOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamUserVo.class).args(userEntity.getOrgId());
//                    String oldOrgName = oldOrg.first().getName();
//                    // 获取新组织名称
//                    SQLQuery<IamUserVo> newOrg = dao.createSQLQuery("select name_ from iam_org where id_ = ? ",IamUserVo.class).args(creation.getOrgId());
//                    String newOrgName = newOrg.first().getName();
////                    oldIamUser.setOrgId(userEntity.getOrgId());
////                    iamUser.setOrgId(creation.getOrgId());
//                    makeDiffList(oldList,newList,"所属组织:",oldOrgName,"所属组织:",newOrgName);
//                }
                IamUserOrgOrderVo iamUserOrg = creation.getIamUserOrg();
                List<IamUserOrgOrderVo> iamUserOtherOrg = creation.getIamUserOtherOrg()==null?new ArrayList<>():creation.getIamUserOtherOrg();
                if(ObjectUtil.isNotEmpty(iamUserOrg)&&isFlag(userEntity.getOrgId(),iamUserOrg.getOrgId())){
                    // 获取旧组织名称
                    String oldOrgName = "";
                    if(StrUtil.isNotEmpty(userEntity.getOrgId())) {
                        IamOrgEntity oldOrg = iamOrgManager.find(userEntity.getOrgId());
                        if(ObjectUtil.isNotEmpty(oldOrg)){
                            oldOrgName= oldOrg.getName();
                        }
                    }
                    // 获取新组织名称
                    IamOrgEntity newOrg = iamOrgManager.find(iamUserOrg.getOrgId());
                    String newOrgName = "";

                    if(ObjectUtil.isNotEmpty(newOrg)){
                        newOrgName = newOrg.getName();
                    }
//                    oldIamUser.setOrgId(userEntity.getOrgId());
//                    iamUser.setOrgId(creation.getOrgId());
                    makeDiffList(oldList,newList,"所属组织:",oldOrgName,"所属组织:",newOrgName);
                }
                if(ObjectUtil.isNotEmpty(iamUserOrg)){
                    if(ObjectUtil.isNotNull(iamUserOrg.getSortOptions())||StrUtil.isNotEmpty(iamUserOrg.getSortUserId())){
                        oldList.add("修改主部门排序");
                        if(iamUserOrg.getSortOptions()==2) {
                            IamUserEntity user = iamUserManager.find(iamUserOrg.getSortUserId());
                            newList.add("主部门排序：排在" + user.getName() + "之后");
                        }
                        else if(iamUserOrg.getSortOptions()==0) {
                            newList.add("主部门排序：排在最前");
                        }
                        else {
                            newList.add("主部门排序：排在最后");
                        }
                    }
                }

                // 其余组织列表是否改变
                List<String> oldOtherOrgIds = userEntity.getOtherOrgIds()==null?new ArrayList<>():userEntity.getOtherOrgIds();

                Set<String> oldOtherOrgIdsSet = new HashSet<String>(oldOtherOrgIds);
                List<String> newOtherOrgIds = iamUserOtherOrg.stream().map(e->e.getOrgId()).filter(s->StrUtil.isNotEmpty(s)).collect(Collectors.toList());
                // 区分两者之间是否有区别
                // 大小不同，直接不同
                boolean flag = true;
                if(oldOtherOrgIds.size() != newOtherOrgIds.size()){
                    flag = false;
                }else {
                    flag = newOtherOrgIds.stream().allMatch(e->oldOtherOrgIdsSet.contains(e));
                }
                // 不同，查询出来各自的名称
                if(!flag){
                    String oldOrgNames = "";
                    if(ObjectUtil.isNotEmpty(oldOtherOrgIds)){
                        String oldJoin = String.join("','",oldOtherOrgIds);
                        oldOrgNames = iamOrgManager.dao().createQuery(IamOrgEntity.class)
                                .where("id_ in ('" + oldJoin + "')")
                                .where("deleted_ = false")
                                .execute()
                                .list()
                                .stream().map(e->e.getName())
                                .collect(Collectors.toList())
                                .toString();
                    }
                    String newOrgNames = "";
                    if(ObjectUtil.isNotEmpty(newOtherOrgIds)){
                        String newJoin = String.join("','",newOtherOrgIds);
                        newOrgNames = iamOrgManager.dao().createQuery(IamOrgEntity.class)
                                .where("id_ in ('" + newJoin + "')")
                                .where("deleted_ = false")
                                .execute()
                                .list()
                                .stream().map(e->e.getName())
                                .collect(Collectors.toList())
                                .toString();
                    }
                    oldList.add("其他所属组织："+oldOrgNames);
                    newList.add("其他所属组织："+newOrgNames);
                    userEntity.setOtherOrgIds(oldOtherOrgIds);
                    iamUser.setOtherOrgIds(newOtherOrgIds);
                }
                for (IamUserOrgOrderVo iamUserOrgOrderVo : iamUserOtherOrg) {
                    if(ObjectUtil.isNotNull(iamUserOrgOrderVo.getSortOptions())||StrUtil.isNotEmpty(iamUserOrgOrderVo.getSortUserId())){
                        oldList.add("修改部门["+iamUserOrgOrderVo.getOrgName()+"]排序");
                        if(iamUserOrgOrderVo.getSortOptions()==2) {
                            IamUserEntity user = iamUserManager.find(iamUserOrgOrderVo.getSortUserId());
                            newList.add("部门["+iamUserOrgOrderVo.getOrgName()+"]排序：排在" + user.getName() + "之后");
                        }
                        else if(iamUserOrgOrderVo.getSortOptions()==0) {
                            newList.add("部门[" + iamUserOrgOrderVo.getOrgName() + "]排序：排在最前");
                        }
                        else {
                            newList.add("部门["+iamUserOrgOrderVo.getOrgName()+"]排序：排在最后");
                        }
                    }
                }
//                if(ObjectUtil.isNotEmpty(iamUserOtherOrg)){
//                    for (IamUserOrgOrderVo iamUserOrgOrderVo : iamUserOtherOrg) {
//                        if(ObjectUtil.isNotNull(iamUserOrgOrderVo.getSortOptions())||StrUtil.isNotEmpty(iamUserOrgOrderVo.getSortUserId())){
//                            oldList.add("修改部门["+iamUserOrgOrderVo.getOrgName()+"]排序");
//                            if(iamUserOrgOrderVo.getSortOptions()==2) {
//                                IamUserEntity user = iamUserManager.find(iamUserOrgOrderVo.getSortUserId());
//                                newList.add("部门["+iamUserOrgOrderVo.getOrgName()+"]排序：排在" + user.getName() + "之后");
//                            }
//                            else if(iamUserOrgOrderVo.getSortOptions()==0) {
//                                newList.add("部门[" + iamUserOrgOrderVo.getOrgName() + "]排序：排在最前");
//                            }
//                            else {
//                                newList.add("部门["+iamUserOrgOrderVo.getOrgName()+"]排序：排在最后");
//                            }
//                        }
//                    }
//                }


                if(StrUtil.isNotEmpty(creation.getPassword())){
                    oldList.add("修改密码");
                    newList.add("修改密码：******");
                }

                if(isFlag(userEntity.getMobile(),creation.getMobile())){
                    makeDiffList(oldList,newList,"电话:",userEntity.getMobile(),"电话:",creation.getMobile());
                }

                if(isFlag(userEntity.getPinyin(),creation.getPinyin())){
                    makeDiffList(oldList,newList,"全拼:",userEntity.getPinyin(),"全拼:",creation.getPinyin());
                }

                if(isFlag(userEntity.getPy(),creation.getPy())){
                    makeDiffList(oldList,newList,"简拼:",userEntity.getPy(),"简拼:",creation.getPy());
                }

                if(isFlag(userEntity.getOaName(),creation.getOaName())){
                    makeDiffList(oldList,newList,"OA用户名:",userEntity.getOaName(),"OA用户名:",creation.getOaName());
                }

                if(isFlag(userEntity.getEmailInternet(),creation.getEmailInternet())){
                    makeDiffList(oldList,newList,"因特网地址:",userEntity.getEmailInternet(),"因特网地址:",creation.getEmailInternet());
                }

                if(isFlag(userEntity.getOtherEmail(),creation.getOtherEmail())){
                    makeDiffList(oldList,newList,"其他邮箱地址:",userEntity.getOtherEmail(),"其他邮箱地址:",creation.getOtherEmail());
                }

                if(isFlag(userEntity.getAdShowName(),creation.getAdShowName())){
                    makeDiffList(oldList,newList,"AD显示名:",userEntity.getAdShowName(),"AD显示名:",creation.getAdShowName());
                }
                if(isFlag(userEntity.getUsername(),creation.getUsername())){
                    makeDiffList(oldList,newList,"登录名:",userEntity.getUsername(),"登录名:",creation.getUsername());
                }

                if (isFlag(creation.getEnableDate(),userEntity.getEnableDate())) {
                    LocalDate oldEnableDate = userEntity.getEnableDate();
                    LocalDate newEnableDate = creation.getEnableDate();
                    makeDiffList(oldList,newList,"生效时间:", ObjectUtil.isEmpty(oldEnableDate)?"":oldEnableDate.toString(),"生效时间:",ObjectUtil.isEmpty(newEnableDate)?"":newEnableDate.toString());
                }

                if(isFlag(userEntity.getCompany(),creation.getCompany())){
                    makeDiffList(oldList,newList,"公司:",userEntity.getCompany(),"公司:",creation.getCompany());
                }

                if(isFlag(userEntity.getInterfaceBy(),creation.getInterfaceBy())){
                    makeDiffList(oldList,newList,"接口人:",userEntity.getInterfaceBy(),"接口人:",creation.getInterfaceBy());
                }

                if(isFlag(userEntity.getEmailUserServiceLevel(),creation.getEmailUserServiceLevel())){
                    makeDiffList(oldList,newList,"邮箱用户服务级别:",userEntity.getEmailUserServiceLevel(),"邮箱用户服务级别:",creation.getEmailUserServiceLevel());
                }

                if(isFlag(userEntity.getEmployeeRoleName(),creation.getEmployeeRoleName())){
                    makeDiffList(oldList,newList,
                            "角色:",
                            DictUtil.getValueOrTitle(userEntity.getEmployeeRoleName(),null,"EMPLOYEE_ROLE_NAME_DICT"),
                            "角色:",DictUtil.getValueOrTitle(creation.getEmployeeRoleName(),null,"EMPLOYEE_ROLE_NAME_DICT")
                            );
                }

                if(isFlag((String)userEntity.getDescription(),creation.getDescription())){
                    makeDiffList(oldList,newList,"备注:",userEntity.getDescription(),"备注:",creation.getDescription());
                }
                if(isFlag(userEntity.getOaHide(),creation.getOaHide())){
                    makeDiffList(oldList,newList," 门户通讯录是否隐藏:",ObjectUtil.isNotEmpty(userEntity.getOaHide())?(userEntity.getOaHide()?"是":"否"):"","门户通讯录是否隐藏:",ObjectUtil.isNotEmpty(creation.getOaHide())?(creation.getOaHide()?"是":"否"):"");
                }
                LocalDateTime oldDate = userEntity.getExpiredAt();
                LocalDateTime newDate = creation.getExpiredAt();
                String oldDateStr = ObjectUtil.isNotEmpty(oldDate)?oldDate.toLocalDate().toString():"";
                String newDateStr = ObjectUtil.isNotEmpty(newDate)?newDate.toLocalDate().toString():"";

                if(isFlag(oldDateStr,newDateStr)){

                    makeDiffList(oldList,newList,"失效时间:", oldDateStr,"失效时间:", newDateStr);
                }


                if(isFlag(userEntity.getPhone(),creation.getPhone())) {
                    makeDiffList(oldList, newList, "座机:", userEntity.getPhone(), "座机:", creation.getPhone());
                }

                if(isFlag(userEntity.getErpId(),creation.getErpId())) {
                    makeDiffList(oldList, newList, "erp号:", userEntity.getErpId(), "erp号:", creation.getErpId());
                }

                if(isFlag(userEntity.getCode(),creation.getCode())) {
                    makeDiffList(oldList, newList, "员工编号:", userEntity.getCode(), "员工编号:", creation.getCode());
                }

                if(isFlag(userEntity.getPosition(),creation.getPosition())){
                    makeDiffList(oldList,newList,"岗位:",userEntity.getPosition(),"岗位:",creation.getPosition());
                }

                if(isFlag(userEntity.getWorkPlace(),creation.getWorkPlace())){
                    makeDiffList(oldList,newList,"工作地址:",userEntity.getWorkPlace(),"工作地址:",creation.getWorkPlace());
                }

                if(isFlag(userEntity.getAdmin(),creation.getAdmin())){
                    makeDiffList(oldList,newList,"管理者:",userEntity.getAdmin(),"管理者:",creation.getAdmin());
                }

                if(isFlag(userEntity.getAdminId(),creation.getAdminId())){
                    makeDiffList(oldList,newList,"管理者账号:",userEntity.getAdminId(),"管理者账号:",creation.getAdminId());
                }

                if(isFlag(userEntity.getPostalCode(),creation.getPostalCode())){
                    makeDiffList(oldList,newList,"邮政编码:",userEntity.getPostalCode(),"邮政编码:",creation.getPostalCode());
                }

                if(isFlag(userEntity.getDirectPhone(),creation.getDirectPhone())){
                    makeDiffList(oldList,newList,"分机号:",userEntity.getDirectPhone(),"分机号:",creation.getDirectPhone());
                }


                if(ObjectUtil.isNotEmpty(userEntity.getStaffType())&&ObjectUtil.isNotEmpty(creation.getStaffType())){
                    boolean change = false;
                    List<String> oldStaffType = Arrays.asList(userEntity.getStaffType().get(0).split("\\|"));
                    List<String> newStaffType = creation.getStaffType();
                    if((oldStaffType.size()==newStaffType.size())) {
                        for (String s : oldStaffType) {
                            if(!oldStaffType.contains(s)){
                                change = true;
                                break;
                            }
                        }
                    }else {
                        change = true;
                    }
                    if(change){
                        makeDiffList(oldList,newList,"用户类型:",StrUtil.join(",",oldStaffType)
                                ,"用户类型:",StrUtil.join(",",newStaffType));
                    }
                }



//                // 其余组织列表是否改变
//                List<String> oldOtherOrgIds = this.dao.createQuery(GfIamUserOrgEntity.class).filter("userId",entity.getObjectId()).filter("primary",false)
//                        .list().stream().map(e->e.getOrgId())
//                        .collect(Collectors.toList());
//                Set<String> oldOtherOrgIdsSet = new HashSet<String>(oldOtherOrgIds);
//                List<String> newOtherOrgIds = creation.getOtherOrgIds()==null?new ArrayList<>():creation.getOtherOrgIds();
//                // 区分两者之间是否有区别
//                // 大小不同，直接不同
//                boolean flag = true;
//                if(oldOtherOrgIds.size() != newOtherOrgIds.size()){
//                    flag = false;
//                }else {
//                    flag = newOtherOrgIds.stream().allMatch(e->oldOtherOrgIdsSet.contains(e));
//                }



                result.setNewIamUser(iamUser);
                result.setOldIamUser(userEntity);
            }
            result.setOldStr(oldList);
            result.setNewStr(newList);
        }else {
            if(entity.getObjectType().equals("org")){
                IamOrgEntity orgEntity = iamOrgManager.find(entity.getObjectId());
                BeanUtils.copyProperties(orgEntity,iamOrg);
                iamOrg.setSimpleSpell((String) orgEntity.getProperty("simpleSpell"));
                iamOrg.setOaSimpleSpell((String) orgEntity.getProperty("oaSimpleSpell"));
                iamOrg.setSelfBusinessCode((String) orgEntity.getProperty("selfBusinessCode"));
                iamOrg.setEmailInternet((String) orgEntity.getProperty("emailInternet"));
                iamOrg.setOtherEmail((String) orgEntity.getProperty("otherEmail"));
                iamOrg.setEmailDomain((String) orgEntity.getProperty("emailDomain"));
                iamOrg.setDeptType((String) orgEntity.getProperty("deptType"));
                iamOrg.setGroupType((String) orgEntity.getProperty("groupType"));
                iamOrg.setManager((String) orgEntity.getProperty("manager"));
                iamOrg.setDescription((String) orgEntity.getProperty("description"));
                result.setNewIamOrg(iamOrg);
            }else{
                IamUserEntity userEntity = iamUserManager.find(entity.getObjectId());
                BeanUtils.copyProperties(userEntity,iamUser);
                iamUser.setOaName((String) userEntity.getProperty("oaName"));
                iamUser.setMailboxCapacity((String) userEntity.getProperty("mailboxCapacity"));
                iamUser.setEmailInternet((String) userEntity.getProperty("emailInternet"));
                iamUser.setOtherEmail((String) userEntity.getProperty("otherEmail"));
                iamUser.setAdShowName((String) userEntity.getProperty("adShowName"));
                if(ObjectUtil.isNotEmpty(userEntity.getProperty("enableDate"))) {
                    iamUser.setEnableDate(LocalDate.parse((String) userEntity.getProperty("enableDate")));
                }
                iamUser.setCompany((String) userEntity.getProperty("company"));
                iamUser.setInterfaceBy((String) userEntity.getProperty("interfaceBy"));
                iamUser.setEmailUserServiceLevel((String) userEntity.getProperty("emailUserServiceLevel"));
                iamUser.setDisableAccessToOa((String) userEntity.getProperty("disableAccessToOa"));
                iamUser.setEmployeeRoleName((String) userEntity.getProperty("employeeRoleName"));
                iamUser.setDescription((String) userEntity.getProperty("description"));
                iamUser.setPhone((String) userEntity.getProperty("phone"));
                iamUser.setErpId((String) userEntity.getProperty("erpId"));
                iamUser.setPosition((String) userEntity.getProperty("position"));
                iamUser.setWorkPlace((String) userEntity.getProperty("workPlace"));
                iamUser.setManager((String) userEntity.getProperty("manager"));
                iamUser.setPostalCode((String) userEntity.getProperty("postalCode"));
                iamUser.setDirectPhone((String) userEntity.getProperty("directPhone"));
                result.setNewIamUser(iamUser);
            }
        }
        if(entity.getObjectType().equals("user")){
            String str = "";
            if(!entity.getOperationType().equals("add")&&!entity.getOperationType().equals("batchAD")) {
                // 操作对象：旧信息
                IamUserVo userVo = JSONUtil.toBean(entity.getRequestObjectOld(), IamUserVo.class);
                String erp = StringUtils.isNotEmpty(userVo.getErpId()) ? userVo.getErpId() : "";
                str = "用户名:" + userVo.getName() + ",登录名:" + userVo.getUsername();
                if(StrUtil.isNotEmpty(userVo.getOrgId())){
                    IamOrgEntity iamOrgEntity = iamOrgManager.find(userVo.getOrgId());
                    if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                        str += ",主部门:" + iamOrgEntity.getName();
                    }
                }
                result.setRequestObjectStr(str);
            }else if(entity.getOperationType().equals("batchAD")){
                UserBatchAdVo userBatchAdVo = JSONUtil.toBean(entity.getRequestObject(), UserBatchAdVo.class);
                if(ObjectUtil.isNotEmpty(userBatchAdVo)&&ObjectUtil.isNotEmpty(userBatchAdVo.getUserList())) {
                    for (IamUserVo iamUserVo : userBatchAdVo.getUserList()) {
                        str += "用户名:" + iamUserVo.getName() + ",登录名:" + iamUserVo.getUsername() + ",主部门:" + iamUserVo.getOrgName();
                        ;
                    }
                }
                result.setRequestObjectStr(str);
            }
        }else if(entity.getObjectType().equals("org")){
            String str = "";
            if(!entity.getOperationType().equals("add")) {
                IamOrgVo orgVo = JSONUtil.toBean(entity.getRequestObjectOld(), IamOrgVo.class);
                String simpleSpell = StringUtils.isNotEmpty(orgVo.getSimpleSpell()) ? orgVo.getSimpleSpell() : "";
                str = "群组名称:" + orgVo.getName() + ",组账户名:" + simpleSpell;
                if (StrUtil.isNotEmpty(orgVo.getParentId())) {
                    IamOrgEntity iamOrgEntity = iamOrgManager.find(orgVo.getParentId());
                    str += ",上级群组:" + (ObjectUtil.isEmpty(iamOrgEntity)?"":iamOrgEntity.getName());
                }
                result.setRequestObjectStr(str);
            }
        }else if(entity.getObjectType().equals("users")){
            if(entity.getOperationType().equals("move")){
                String str = "";
                IamUsersMoveVo moveVo = JSONUtil.toBean(entity.getRequestObject(), IamUsersMoveVo.class);
                List<String> addUserIds = moveVo.getAddUserIds();
                List<RowMap> usersInfoByIds = iamUserManagerGF.getUsersInfoByIds(addUserIds);
                for (RowMap user : usersInfoByIds) {
                    str += "用户名:" + user.getString("name") + ",登录名:" + user.getString("username")+",主部门:" + user.getString("orgname")+";\n";
                }
                result.setRequestObjectStr(str);
            }else if(entity.getOperationType().equals("impDelay")){
                String str = "";
                IamUserImportDelayDto moveVo = JSONUtil.toBean(entity.getRequestObject(), IamUserImportDelayDto.class);
                List<IamUserEntity> addUserIds = moveVo.getUserList();
                for (IamUserEntity user : addUserIds) {
                    str += "用户名:" + user.getName() + ",登录名:" + user.getUsername()+";";
                }
                result.setRequestObjectStr(str);
            }
        }
        return result;
    }

    private void makeDiffList(List<String> oldList, List<String> newList, String oldTitle, String oldValue, String newTitle, String newValue) {
        oldValue = StrUtil.isNotEmpty(oldValue)?oldValue:"";
        newValue = StrUtil.isNotEmpty(newValue)?newValue:"";
        oldList.add(oldTitle + oldValue);
        newList.add(newTitle + newValue);
    }

    private boolean isFlag(Object str1,Object str2){
        boolean flag = false;
        if((ObjectUtil.isNotEmpty(str1) && ObjectUtil.isNotEmpty(str2))){
            if(!str1.equals(str2)){
                flag = true;
            }
        } else if (ObjectUtil.isNotEmpty(str1) || ObjectUtil.isNotEmpty(str2)) {
            flag = true;
        }
        return flag;
    }



    @Transactional(rollbackFor = Exception.class)
    public int delRecord(String id){
        return dao.softDelete(IamRequestOperationEntity.class, id);
    }

    public List<IamUserEntity> getUserList(Query query,String orgId){
        List<IamUserEntity> list = iamUserManager.createQuery().filter("orgId", orgId).orderBy("sort_order").queryable(query).list();
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public AcmResultMsgVO updataUserSort(String userId, String orgId, Integer type, String sortUserId,boolean changeOrgId,boolean isPrimary){
        AcmResultMsgVO syncDataResultVo = AcmResultMsgVO.success(null);
        IamUserVo updateEntity = iamUserManager.find(IamUserVo.class,userId);
        // 是否修改主部门
        if(changeOrgId) {
            updateEntity.setOrgId(orgId);
        }
        long finalOaOrderSort = 0;
        long finalAdOrderSort = 0;
        // 移到最上或者最下
        if(ObjectUtil.isEmpty(type)){
            type = 1;
        }
        if(type == 0 || type == 1){
            IamUserOrgOrderVo iamUserOrgOrderVo = new IamUserOrgOrderVo();
            String sql = "MAX";
            if(type == 1){
                sql = "MIN";
            }
            // 查询oa,ad排序
            SQLQuery<GfIamUserOrgEntity> args = dao.createSQLQuery("select "+sql+"(oa_order) as oa_order,"+sql+"(ad_order) as ad_order from iam_user_org where org_id = ? ", GfIamUserOrgEntity.class).args(orgId).limit(1);
            GfIamUserOrgEntity user = args.list().size() > 0 ? args.list().get(0) : null;
            if(ObjectUtil.isNotEmpty(user)){
                Long oaOrder = user.getOaOrder()==null?100000L:(user.getOaOrder());
                Long adOrder = user.getAdOrder()==null?100000L:(user.getAdOrder());
                if(type == 0){// 最前 数字最大
                    finalOaOrderSort = 100000;
                    finalAdOrderSort = 100000;
                    // 刷新后面的用户
                    gfIamUserOrgManager.dao().executeUpdate("update iam_user_org set ad_order = ad_order - 1,oa_order = oa_order-1 where org_id = ?;",orgId);
//                    updateEntity.setSortOrder2(finalSort);
                }else if(type == 1){// 最后 数字最小
                    finalOaOrderSort = user.getOaOrder()==null?100000L:(oaOrder -20);
                    finalAdOrderSort = user.getAdOrder()==null?100000L:(adOrder -20);
//                    updateEntity.setSortOrder2(finalSort);
                }
            }

        }else if(type == 2){
            // 移动到某个用户之后
            List<GfIamUserOrgEntity> list = gfIamUserOrgManager.createQuery().filter("userId", sortUserId).filter("orgId",orgId).select("oa_order","ad_order").list();
            if(ObjectUtil.isNotEmpty(list)){
                Long adOrder = list.get(0).getAdOrder()==null?100000L:list.get(0).getAdOrder();
                Long oaOrder = list.get(0).getOaOrder()==null?100000L:list.get(0).getOaOrder();
                // 如果该用户后面还有用户，则排序号为两个用户的排序号/2
                List<RowMap> list1 = new ArrayList<>();
                if(oaOrder!=null) {
                    list1 = gfIamUserOrgManager.dao().createSQLQuery("select * from iam_user_org where  org_id = ? and ad_order < ?\n" +
                        "order by  ad_order desc limit 1", orgId, adOrder).list();
                }
                if(ObjectUtil.isNotEmpty(list1)){
                    Long adOrder1 = list1.get(0).getLong("adOrder");
                    finalAdOrderSort = (adOrder + adOrder1)/2;
                }else {
                    finalAdOrderSort = adOrder - 20;
                }
                if(ObjectUtil.isNotEmpty(list1)){
                    Long oaOrder1 = list1.get(0).getLong("oaOrder");
                    finalOaOrderSort = (oaOrder + oaOrder1)/2;
                }else {
                    finalOaOrderSort = oaOrder - 20;
                }
            }
//            updateEntity.setSortOrder2(finalSort);
        }
        if(isPrimary) {
            iamUserManager.dao().updateSettable(IamUserEntity.class, updateEntity);
            gfIamUserOrgManager.updateUsersPrimaryOrg(Arrays.asList(updateEntity.getId()),updateEntity.getOrgId());
        }
        gfIamUserOrgManager.dao().executeUpdate("update iam_user_org set oa_order = ? ,ad_order = ? where user_id = ? and org_id = ?",finalOaOrderSort,finalAdOrderSort,updateEntity.getId(),orgId);

        return syncDataResultVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void check(String requestId) {
        // 获取当前用户
        UserInfo currentUser = Security.getCurrentUser();
        IamRequestOperationEntity iamRequestOperationEntity = find(requestId);
        // 设置承接人信息
        iamRequestOperationEntity.setApprovalBy(currentUser.getIdAsString());
        iamRequestOperationEntity.setApprovalByName(currentUser.getName());
        this.dao().updateSettable(iamRequestOperationEntity);
    }

    public long count() {
        // 获取当前用户
        String id = Security.getCurrentUser().getIdAsString();
        CriteriaQuery<IamRequestOperationEntity> criteriaQuery = this.dao.createQuery(IamRequestOperationEntity.class);
        criteriaQuery.where("#status = ? ",1);
        criteriaQuery.where("#approval_by = ?",id);
        long count = criteriaQuery.count();
        return count;
    }

    public long checkIfExist(String id,String type) {
        CriteriaQuery<IamRequestOperationEntity> criteriaQuery = this.dao.createQuery(IamRequestOperationEntity.class);
        criteriaQuery.where("#status = ? ",1);
        criteriaQuery.where("#object_id like ?","%"+id+"%");
        if("org".equals(type)) {
            criteriaQuery.where("#object_type = ?",type);
        } else {
            criteriaQuery.where("#object_type = ? or object_type = 'users'",type);
        }
        long count = criteriaQuery.count();
        return count;
    }


    public List<String> checkIfListExist(List<String> ids, String type) {
        ArrayList<String> names = new ArrayList<>();
        for (String id : ids) {
            CriteriaQuery<IamRequestOperationEntity> query = this.dao.createQuery(IamRequestOperationEntity.class);
            query.where("#status = ? ",1);
            query.where("#object_type = ?",type);
            query.where("#object_id like ?","%"+id+"%");
            long count = query.count();
            if(count >= 1) {
                if("user".equals(type)||"users".equals(type)) {
                    String name = iamUserManager.find(id).getName();
                    names.add(name);
                }else{
                    String name = iamOrgManager.find(id).getName();
                    names.add(name);
                }
            }
        }
        return names;
    }

    public List<String> checkRequestIfExist(List<String> ids, String type) {
        List<String> names = new ArrayList<>();
        for (String id : ids) {
            // 查询该复核包含的实体
            String objectId = find(id).getObjectId();
            if(StrUtil.isNotEmpty(objectId)) {
                String[] objectIds = objectId.split(",");
                for (String oId : objectIds) {
                    List<IamRequestOperationEntity> list = this.createQuery().where("status_ = ?", 1)
                            .where("concat(object_id,',') like ? ", "%" + oId + ",%")
                            .list();
                    if (ObjectUtil.isNotEmpty(list)) {
                        if ("user".equals(type)) {
                            String name = iamUserManager.find(oId).getName();
                            names.add(name);
                        } else if ("org".equals(type)) {
                            String name = iamOrgManager.find(oId).getName();
                            names.add(name);
                        }
                    }
                }

            }
        }
        return names;
    }

    @Transactional(rollbackFor = Exception.class)
    public AcmResultMsgVO sendAcm(String requestId,String logId) throws Exception {
        // 更新状态
        IamRequestOperationEntity entity = (IamRequestOperationEntity) ((CriteriaQuery) this.dao.createQuery(IamRequestOperationEntity.class).id(requestId).forUpdate()).singleOrNull();
        AcmResultMsgVO acmResultMsgVO = AcmResultMsgVO.success(null);
        if ("user".equals(entity.getObjectType())) {
            if (entity.getOperationType().equals("add")) {
                IamUserVo creation = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);
                // 将排序的组织id放入creation的org_id中
                creation.setOrgId(creation.getIamUserOrg().getOrgId());
                // 其余所属组织不为空
                if (ObjectUtil.isNotEmpty(creation.getIamUserOtherOrg()))
                    creation.setOtherOrgIds(creation.getIamUserOtherOrg().stream().map(v -> v.getOrgId()).collect(Collectors.toList()));
                acmResultMsgVO = userSyncManager.addAcmUser(creation, acmResultMsgVO, logId);
            } else if (entity.getOperationType().equals("update") || entity.getOperationType().equals("enaDel")) {
                IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);
                //设置主组织与其余所属组织信息
                user.setOrgId(user.getIamUserOrg().getOrgId());
                if (ObjectUtil.isNotEmpty(user.getIamUserOtherOrg()))
                    user.setOtherOrgIds(user.getIamUserOtherOrg().stream().map(e -> e.getOrgId()).collect(Collectors.toList()));
                IamUserVo userOld = JSONUtil.toBean(entity.getRequestObjectOld(), IamUserVo.class);
                user.setStaffType(Arrays.asList(user.getStaffType().stream().collect(Collectors.joining("|"))));
                IamUserOrgOrderVo iamUserOrg = user.getIamUserOrg();
                if (ObjectUtil.isNotEmpty(iamUserOrg) && ObjectUtil.isNotEmpty(iamUserOrg.getSortOptions())) {
                    updataUserSort(entity.getObjectId(), user.getOrgId(), iamUserOrg.getSortOptions(), user.getIamUserOrg().getSortUserId(), false, true);
                }
                // 更新其余所属组织排序
                if (ObjectUtil.isNotEmpty(user.getIamUserOtherOrg())) {
                    for (IamUserOrgOrderVo orderVo : user.getIamUserOtherOrg()) {
                        if (ObjectUtil.isNotEmpty(orderVo.getSortOptions())) {
                            updataUserSort(entity.getObjectId(), orderVo.getOrgId(), orderVo.getSortOptions(), orderVo.getSortUserId(), false, false);
                        }
                    }
                }
                if (entity.getOperationType().equals("enaDel")) {
                    acmResultMsgVO = userSyncManager.enableAcmUser(userOld.getAcmUserId(), acmResultMsgVO,logId );
                }
                acmResultMsgVO = userSyncManager.updateAcmUser(user,acmResultMsgVO,logId);
                // 更新密码
                // acm 异步调用
                log.info("【复核过程】更新用户{}-更新密码",user.getName());
                if(ObjectUtil.isNotEmpty(entity.getAcmFlag())&&entity.getAcmFlag()==1) {
                    log.info("【复核过程】更新用户{}-更新密码(发送oa、ad)", user.getName());
                    iamUserPasswordManager.resetPassword(user.getUsername(), user.getPassword(),logId,acmResultMsgVO);
                }
            } else if (entity.getOperationType().equals("delete")) {
                IamUserEntity iamUserVo = iamUserManager.find(entity.getObjectId());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                long date1 = System.currentTimeMillis();
                Date date = new Date(date1);
                acmResultMsgVO = userSyncManager.deleteAcmUser((String) iamUserVo.getProperty("acmUserId"), acmResultMsgVO ,logId);
            } else if (entity.getOperationType().equals("move")) {
                IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);
                // 包括判断同步acm
                String acmUserId = (String) iamUserManager.find(entity.getObjectId()).getProperty("acmUserId");
                user.setAcmUserId(acmUserId);
                acmResultMsgVO = userSyncManager.moveAcmUser(user, acmResultMsgVO, logId);
            } else if (entity.getOperationType().equals("updatePass")) {
                IamUserVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserVo.class);

                String password = user.getPassword();
                String userId = entity.getObjectId();
//                    boolean b = passwordManager.updatePassword(userId, password);
//                    if(!b) {
//                        throw new RuntimeException("更新密码失败");
//                    }
                IamUserEntity iamUserEntity = iamUserManager.find(userId);
                // 发送oa、ad
                log.info("【复核过程】更新用户密码（发送ad，oa）：{}",user.getName());
                iamUserPasswordManager.resetPassword(iamUserEntity.getUsername(), password, logId, acmResultMsgVO);

            }
        } else if ("org".equals(entity.getObjectType())) {
            if (entity.getOperationType().equals("add")) {
                IamOrgVo creation = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                // 发送acm
                orgSyncManager.addAcmOrg(creation,  acmResultMsgVO, logId);
            } else if (entity.getOperationType().equals("update")) {
                IamOrgVo orgVoOld = JSONUtil.toBean(entity.getRequestObjectOld(), IamOrgVo.class);
                IamOrgVo orgVo = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                orgSyncManager.updateAcmOrg(orgVo, "update",acmResultMsgVO,logId);
            } else if ("move".equals(entity.getOperationType())) {
                IamOrgVo newOrgVo = JSONUtil.toBean(entity.getRequestObject(), IamOrgVo.class);
                IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, entity.getObjectId());
                iamOrgVo.setParentId(newOrgVo.getParentId());
                // acm 调用
                orgSyncManager.updateAcmOrg(iamOrgVo, "move",acmResultMsgVO,logId);
            } else {
                IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, entity.getObjectId());
                // acm 调用
                orgSyncManager.removeAcmOrg(iamOrgVo,acmResultMsgVO,logId);
            }
        } else if ("users".equals(entity.getObjectType())) {
            if (entity.getOperationType().equals("move")) {
                IamUsersMoveVo iamUsersMoveVo = JSONUtil.toBean(entity.getRequestObject(), IamUsersMoveVo.class);
                List<String> addUserIds = iamUsersMoveVo.getAddUserIds();
                String orgId = iamUsersMoveVo.getOrgId();
                // 修改用户的主部门
                for (String userId : addUserIds) {
                    IamUserVo iamUserEntity = iamUserManager.find(IamUserVo.class, userId);
                    iamUserEntity.setOrgId(orgId);
                    // acm
                    if (StrUtil.isNotEmpty(iamUserEntity.getAcmUserId())) {
                        String typeByUser = iamUserManagerGF.getTypeByUser(iamUserEntity);
                        AcmUserEmpDto acmUser = iamUserManagerGF.initUpdateAcmUser(iamUserEntity);
                        acmApiService.updateEmp(acmUser);
                    }
                }
            } else if (entity.getOperationType().equals("delay")) {
                IamUserDelayVo user = JSONUtil.toBean(entity.getRequestObject(), IamUserDelayVo.class);
                List<String> userIds = user.getUserIds();
                LocalDateTime expireAt = user.getExpireAt();
                userSyncManager.updateUserDelayTime(userIds,expireAt,AcmResultMsgVO.success(null),logId);
            } else if (entity.getOperationType().equals("impDelay")) {
                IamUserImportDelayDto user = JSONUtil.toBean(entity.getRequestObject(), IamUserImportDelayDto.class);
                List<IamUserEntity> userList = user.getUserList();
                AcmResultMsgVO success = AcmResultMsgVO.success(null);
                for (IamUserEntity iamUserEntity : userList) {
                    userAsyncManager.updateUserDelayTime(Arrays.asList(iamUserEntity.getId()),iamUserEntity.getExpiredAt(), success,logId);
                }
            }
        }
        return acmResultMsgVO;
    }

}
