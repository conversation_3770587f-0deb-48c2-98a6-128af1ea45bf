package net.bingosoft.fuse.iam.api.tenant.api.vo;

import fly.core.meta.annotation.Summary;
import lombok.Data;

import java.util.List;

@Data
public class IamOrgCreateVo {
    @Summary("类型  部门群组：1  公共群组：2 AD群组：3  ")
    protected String sysType;
    @Summary("用户名称")
    protected String name;
    @Summary("组账户名 大写字母")
    protected String simpleSpell;
    @Summary("OA组简拼 创建AD群组不需要传此参数")
    protected String oaSimpleSpell;
    @Summary("组织排序号 创建AD群组不需要传此参数")
    protected String sortOrder;
    @Summary("业务代码 创建公共群组和AD群组不需要传此参数")
    protected String newNo;
    @Summary("上级业务代码 创建公共群组和AD群组不需要传此参数")
    protected String parentNewNO;
    @Summary("上级组织ID OA-ID AD-ID 创建公共群组和AD群组不需要传此参数")
    protected String parentId;
    @Summary("组织描述")
    protected String description;
    @Summary("外部ID AD_ID")
    protected String externalId;
    @Summary("邮箱")
    protected String email;
    @Summary("部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数")
    protected String deptType;
    @Summary("群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数")
    protected String groupType;
    @Summary("组管理者")
    protected String manager;
    @Summary("其他群组 组织id")
    protected List<String> otherOrg;
    @Summary("用户成员 id")
    protected List<String> member;
    @Summary("组织成员 id")
    protected List<String> groupMember;
}
