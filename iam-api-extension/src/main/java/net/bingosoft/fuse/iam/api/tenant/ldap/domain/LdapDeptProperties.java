package net.bingosoft.fuse.iam.api.tenant.ldap.domain;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * ldap接口部门字段配置信息注入bean
 * @ClassName: LdapDeptProperties
 * @Description: 
 * <AUTHOR>
 * @DateTime 2021年12月3日 下午2:02:20
 */

@Data
@Configuration
@PropertySource("classpath:gfprotal.uum.dept.mapping.properties")
@ConfigurationProperties(prefix = "ldap-dept")
public class LdapDeptProperties {
	private String fduPersonUnid;
	private String no;
	private String fduParentId;
	private String description;
	private String displayname;
	private String fduParentName;
	private String deptType;
	private String fduType;
	private String fduGrade;
	private String fduPy;
	private String parentNo;
	private String fduYxptNo;
	private String newNo;
	private String docUniqueId;// 2024-01-03新增，iam专用
}
