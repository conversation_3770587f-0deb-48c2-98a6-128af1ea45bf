package net.bingosoft.fuse.iam.api.tenant.requst.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import fly.core.meta.annotation.Summary;
import fly.core.security.Security;
import fly.core.security.userinfo.UserInfo;
import fly.data.common.query.Query;
import fly.data.common.query.QueryResult;
import fly.data.common.query.SimpleQueryResult;
import fly.orm.dao.Dao;
import fly.rest.data.annotation.Crud;
import fly.rest.data.crud.operation.CrudAll;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.tenant.acm.vo.AcmResultMsgVO;
import net.bingosoft.fuse.iam.api.tenant.enums.Constants;
import net.bingosoft.fuse.iam.api.tenant.requst.entity.IamRequestOperationEntity;
import net.bingosoft.fuse.iam.api.tenant.requst.entity.IamRequestOperationLogEntity;
import net.bingosoft.fuse.iam.api.tenant.requst.manager.IamRequestOperationLogManager;
import net.bingosoft.fuse.iam.api.tenant.requst.manager.IamRequestOperationManager;
import net.bingosoft.fuse.iam.api.tenant.requst.models.IamRequestOperationDelete;
import net.bingosoft.fuse.iam.api.tenant.requst.models.IamRequestOperationQuery;
import net.bingosoft.fuse.iam.api.tenant.requst.service.ASyncLogService;
import net.bingosoft.fuse.iam.api.tenant.vo.*;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Tag(name = "requestOperation", description = "复核管理")
@RestController
@RequestMapping("/api/tenant/requestOperation")
@Crud(entityClass = IamRequestOperationEntity.class)
@Slf4j
public class IamRequestOperationController implements CrudAll<IamRequestOperationEntity> {
    @Autowired
    Dao dao;
    @Autowired
    protected IamRequestOperationManager iamRequestOperationManager;
    @Autowired
    protected IamRequestOperationLogManager iamRequestOperationLogManager;
    @Autowired
    protected ASyncLogService logService;
    @Resource
    protected RedisTemplate<String,String> redisTemplate;

    @Summary("创建/更新用户")
    @PostMapping("/operationUser")
    public IamRequestOperationEntity operationUser(@RequestBody IamUserVo userVo,String operationType,Integer status,String objectId,String requestId) {
        IamRequestOperationEntity iamRequestOperationEntity = iamRequestOperationManager.insertUserOperate(userVo, operationType, status, objectId, userVo.getAcmFlag(), requestId);
        // 添加日志
        logService.insertLog(true,status,iamRequestOperationEntity.getId());
        return iamRequestOperationEntity;
    }

    @Summary("批量插入AD用户")
    @PostMapping("/ad/batchInsert")
    public IamRequestOperationEntity batchInsert(@RequestBody Object userVo,String operationType,Integer status,String itType,String itUrl) {
        IamRequestOperationEntity iamRequestOperationEntity = iamRequestOperationManager.batchInsert(userVo, operationType, status, itType, itUrl);
        // 添加日志
        logService.insertLog(true,status,iamRequestOperationEntity.getId());
        return iamRequestOperationEntity;
    }

    @Summary("批量延期用户")
    @PostMapping("/batchDelayUser")
    public IamRequestOperationEntity batchDelayUsers(@RequestBody IamUserDelayVo userVo, String operationType, Integer status) {
        IamRequestOperationEntity insert = iamRequestOperationManager.insert("users", status, "delay", userVo, userVo.getUserIds().stream().collect(Collectors.joining(",")), userVo.getItType(), userVo.getItUrl(), userVo.getAcmFlag(), null);
        // 添加日志
        logService.insertLog(true,status,insert.getId());
        return insert;
    }
    @Summary("批量延期用户")
    @PostMapping("/importBatchDelayUser")
    public IamRequestOperationEntity importBatchDelayUser(@RequestBody IamUserImportDelayDto userVo, String operationType, Integer status) {

        List<IamUserEntity> isSuccessful = userVo.getUserList().stream().filter(e -> {
            return ObjectUtil.isNotEmpty(e.getProperty("isSuccessful"))&&true==(Boolean) e.getProperty("isSuccessful");
        }).collect(Collectors.toList());

        userVo.setUserList(isSuccessful);
        if(ObjectUtil.isNotEmpty(isSuccessful)) {
            IamRequestOperationEntity insert = iamRequestOperationManager.insert("users", status, "impDelay", userVo, null, userVo.getItType(), userVo.getItUrl(), 1, null);
            // 添加日志
            logService.insertLog(true, status, insert.getId());
            return insert;
        }else{
            return null;
        }
    }

    @Summary("批量移动用户/批量移动用户主部门")
    @PostMapping("/batchMoveUser")
    public IamRequestOperationEntity batchMoveUser(@RequestBody IamUsersMoveVo userVo, String operationType, Integer status) {
        IamRequestOperationEntity iamRequestOperationEntity = iamRequestOperationManager.insertBatchMoveUserOperate(userVo, operationType, status);
        // 添加日志
        logService.insertLog(true,status,iamRequestOperationEntity.getId());
        return iamRequestOperationEntity;
    }

    @Summary("创建/更新组织")
    @PostMapping("/operationOrg")
    public IamRequestOperationEntity operationOrg(@RequestBody IamOrgVo orgVo,String operationType,Integer status,String objectId,String requestId) {
        IamRequestOperationEntity iamRequestOperationEntity = iamRequestOperationManager.insertOrgOperate(orgVo, operationType, status, objectId, orgVo.getAcmFlag(), requestId);
        // 添加日志
        logService.insertLog(true,status,iamRequestOperationEntity.getId());
        return iamRequestOperationEntity;
    }

    /**
     * 更新复合状态信息
     * @param status 更新为某个状态 0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回
     * @param id 复核单
     * @param approvalOpinion 审核意见
     * @param oper  撤回复核人操作：reset
     * */
    @Summary("更新复核状态信息")
    @PostMapping("/updateOperation")
    public AcmResultMsgVO updateOperation(Integer status, String id, String approvalOpinion, String oper) throws Exception {
        // Redis锁的key
        String lockKey = Constants.REVIEW_LOCK_PREFIX + id;
        // 设置锁的超时时间为30分钟
        long lockTimeout = 30L;

        // 尝试获取锁
        boolean locked = Boolean.TRUE.equals(redisTemplate.opsForValue()
                .setIfAbsent(lockKey,
                        String.format("LOCKE By %s", Optional.ofNullable(Security.getCurrentUser()).map(UserInfo::getUsername).orElse("系统")),
                        lockTimeout,
                        TimeUnit.MINUTES));
        if (!locked) {
            log.info("[复核]{}-获取锁失败", id);
            return AcmResultMsgVO.error("复核处理中,请稍后刷新查看");
        }
        try {
            // 添加日志
            IamRequestOperationLogEntity logEntity = new IamRequestOperationLogEntity();
            try {
                log.info("[复核]{}-添加日志", id);
                logEntity = logService.insertLog(false, status, id);
            } catch (Exception e) {
                log.info("添加日志异常，异常信息" + e.getMessage(), e);
            }
            try {
                log.info("[复核]{}", id);
                AcmResultMsgVO syncDataResultVo = iamRequestOperationManager.updateEntity(status, id, approvalOpinion, oper, logEntity.getId());
                return syncDataResultVo;
            } catch (Exception e) {
                log.info("复核过程异常，异常信息" + e.getMessage(), e);
                return AcmResultMsgVO.error(e.getMessage());
            }
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }

    }

    @Summary("批量提交复核列表")
    @PostMapping("/batchUpdate")
    public void batchCommit(Integer status, @RequestBody  Set<String> id) throws Exception {
        // 添加日志
        id.stream().forEach(e->logService.insertLog(true,status,e));

        if(status == 1) {
            iamRequestOperationManager.createQuery().in("id_","id",id).update("status",status);
        } else if (status == 5) {
            iamRequestOperationManager.createQuery().in("id_","id",id).softDelete();
        }
    }

    @Summary("获取详情")
    @PostMapping("/getDetail")
    public IamRequestOperationVo getDetail(String id) {
        return iamRequestOperationManager.getDetail(id);
    }

    @Summary("获取详情")
    @PostMapping("/getEditDetail")
    public QueryResult<IamRequestOperationVo> getEditDetail(String id) {
        List<IamRequestOperationVo> list = new ArrayList<>();
        IamRequestOperationVo detail = iamRequestOperationManager.getDetail(id);
        list.add(detail);
        QueryResult<IamRequestOperationVo> queryResult = new SimpleQueryResult<>(list);
        return queryResult;
    }

    @PostMapping({"/getList"})
    @Summary("查询数据列表")
    public QueryResult<IamRequestOperationEntity> getList(@RequestBody(required = false) IamRequestOperationQuery operaQuery, Query query, String objectType, String name) {
        QueryResult<IamRequestOperationEntity> queryUsers = iamRequestOperationManager.getList(operaQuery,query,objectType,name);
        return queryUsers;
    }

    @Summary("逻辑删除该复核需求")
    @PostMapping("/delRecord")
    public void delRecord(String id){
        iamRequestOperationManager.delRecord(id);
    }

    @Summary("根据部门id获取用户信息")
    @PostMapping("/getUserList")
    public List<IamUserEntity> getUserList(Query query,String orgId){
        if(StringUtils.isNotEmpty(orgId)){
            return iamRequestOperationManager.getUserList(query,orgId);
        }else{
            return new ArrayList<>();
        }
    }

    @Summary("设置复核承办人")
    @GetMapping("/check")
    public void check(String requestId){
        iamRequestOperationManager.check(requestId);
    }

    @Summary("查询当前本复核人的未复核数")
    @GetMapping("/count")
    public long count(){
        return iamRequestOperationManager.count();
    }

    @Summary("批量提交删除组织数据")
    @PostMapping("/batchDeleteOrg")
    public void batchDeleteOrg(Integer status, @RequestBody IamRequestOperationDelete delete){
        delete.getIds().forEach(id->operationOrg(delete.getOrgVo(),"delete",status,id,null));
    }

    @Summary("批量提交删除用户数据")
    @PostMapping("/batchDeleteUser")
    public void batchDeleteUser(Integer status, @RequestBody IamRequestOperationDelete delete){
        delete.getIds().forEach(id->operationUser(delete.getUserVo(),"delete",status,id,null));
    }

    @Summary("查询当前请求实体的对象的未复核数")
    @GetMapping("/exist")
    public long exist(String id,String type){
        return iamRequestOperationManager.checkIfExist(id, type);
    }

    @Summary("查询当前请求实体的对象（列表）的未复核数，并返回对象名称")
    @PostMapping("/listExists")
    public List<String> listExists(@RequestBody List<String> ids, String type){
        return iamRequestOperationManager.checkIfListExist(ids, type);
    }

    @Summary("根据复核请求的id查询是否存在相同的实体请求,并返回对象名称")
    @PostMapping("/requestsExists")
    public List<String> requestsExists(@RequestBody List<String> id,String type){
        return iamRequestOperationManager.checkRequestIfExist(id, type);
    }

    @Summary("根据复核请求的id查询是否存在相同的实体请求,并返回对象名称")
    @PostMapping("/requestExists")
    public List<String> requestExists(String id,String type){
        return iamRequestOperationManager.checkRequestIfExist(Arrays.asList(id), type);
    }
    /**
     * 获取日志列表
     * @param queryObj 请求实体
     * @param query fly框架的请求参数
     * */
    @Summary("获取日志列表")
    @PostMapping("/log/list")
    public QueryResult<IamRequestOperationLogEntity> logList(@RequestBody IamRequestOperationQuery queryObj, Query query){
        return iamRequestOperationLogManager.getList(queryObj,query);
    }

    /**
     * 获取日志详情
     * @param id 日志id
     * */
    @Summary("获取日志列表")
    @GetMapping("/log/{id}")
    public IamRequestOperationLogEntity logList(@PathVariable("id") String id){
        IamRequestOperationLogEntity iamRequestOperationLogEntity = iamRequestOperationLogManager.find(id);
        String requestObject = iamRequestOperationLogEntity.getRequestObj();
        if(StrUtil.isNotEmpty(requestObject)) {
            try {
                JSONObject jsonObject = JSONUtil.parseObj(requestObject);
                String formatStr = JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                        SerializerFeature.WriteDateUseDateFormat);
                iamRequestOperationLogEntity.setRequestObj(formatStr);
            }catch (Exception e){
                log.error("json解析失败",e);
            }
        }
        return iamRequestOperationLogEntity;
    }

}
