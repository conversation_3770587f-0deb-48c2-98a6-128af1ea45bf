package net.bingosoft.fuse.iam.api.tenant.util;

import cn.hutool.core.util.StrUtil;
import com.novell.ldap.LDAPAttribute;
import com.novell.ldap.LDAPAttributeSet;
import com.novell.ldap.LDAPConnection;
import com.novell.ldap.LDAPEntry;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.tenant.enums.Constants;
import net.bingosoft.fuse.iam.api.tenant.ldap.domain.*;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class NewLdapUtils {
	
	private static NewLdapUtils _ldapUtils = null;

	private NewLdapUtils() {
	}
	
	public static synchronized NewLdapUtils Instance() {
		if (null == _ldapUtils) {
			_ldapUtils = new NewLdapUtils();
		}
		return _ldapUtils;
	}
	
	//根据key 获取Domino Ldap部门查询条件
	public String getLDAPDeptFilterPropertieValueByKey(String Key)
			throws Exception {
		System.out.println("--LDAP Query Filter��" + "ldap.fitler.dept." + Key
				+ "--");
		return getPropertieValueByKey(LdapUtils.Instance()
				.GetLDAPFilterProperties(), "ldap.fitler.dept." + Key);
	}// end

	private String getPropertieValueByKey(Properties pro, String Key) throws Exception {
		String ret = null;
		if (pro == null) {
			throw new Exception("Properties Object NULL Exception");
		} else {
			ret = pro.containsKey(Key) ? pro.getProperty(Key) : "";
		}
		return ret;
	}// end

	public String getUserMappingPropertieValueByKey(String Key)throws Exception {
		return getPropertieValueByKey(LdapUtils.Instance().GetUserMappingProperties(), "user.mapping." + Key);
	}// end

	public String getDeptMappingPropertieValueByKey(String Key)
			throws Exception {

		return getPropertieValueByKey(LdapUtils.Instance()
				.GetDeptMappingProperties(), "dept.mapping." + Key);
	}// end

	//获取 Domino Ldap连接
	public LDAPConnection GetLDAPConnnection(LdapBaseProperties ldapBaseProperties) throws Exception {
		System.out.println( "&&&& 获取 Domino Ldap连接" );
		LDAPConnection ldapConn = null;
		if (ldapConn == null) {
			try {
				System.out.println( " &&&& GetLDAPUri:"+ ldapBaseProperties.getLdapUrl());
				System.out.println( " &&&& GetLDAPBinder:"+ ldapBaseProperties.getBinder());
				ldapConn = new LDAPConnection();
				ldapConn.connect(ldapBaseProperties.getLdapUrl(), Integer.valueOf(ldapBaseProperties.getLdapPort()));
				ldapConn.bind(LDAPConnection.LDAP_V3, ldapBaseProperties.getBinder(), ldapBaseProperties.getBinderPassword().getBytes("UTF8"));
			} catch (Exception ex) {
				System.out.println(ex.getMessage());
				throw new Exception("--Can Not Connect to ladp:" + ldapBaseProperties.getLdapUrl()+ "--Msg:" + ex.getMessage());
			}
		}
		return ldapConn;
	}// end
	
	
	public String get_AD_LdapAttributeValue( String row ){
		String string = "";
		if( row.indexOf("value='")>-1 ){
			string = row.split("value='")[1].split("'}")[0];
		}else if( row.indexOf("values='")>-1 ){
			string = row.split("values='")[1].split("'}")[0];
		}
		return string;
	}
	
	public boolean matchAttribute( String row, String attr ){
		boolean match = false;
		if ( row.toLowerCase().indexOf( "{type='"+ attr.toLowerCase() +"'," )>-1 ) {
			match = true;
		}
		return match;
	}
	
	public Date tranToDominoDate( String str ) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
		Date date = null;
		try {
	    date = dateFormat.parse(str);
	    long l = date.getTime()+ (8*60*60*1000);
	    date = new Date(l);
	   } catch (Exception e) {
	    e.printStackTrace();
	   }
		return date;
	}
	

	

	private String getLDAPAttrbuteValByKey(LDAPAttributeSet attrs, String key)throws Exception {
		//System.out.println("--attr key:"+key+"--");
		String ret = null;
		if (attrs == null) {
			throw new Exception("LDAPAttributeSet Ojbect Can Not Be Null.");
		}

		LDAPAttribute _temp = attrs.getAttribute(key);
		ret = _temp == null ? "" : _temp.getStringValue();
		//System.out.println("--attr val:"+ret+"--");
		return ret;
	}
	
	private String[] getLDAPAttrbuteValsByKey(LDAPAttributeSet attrs, String key)throws Exception {
		//System.out.println("--attr key:"+key+"--");
		String ret[] = null;
		if (attrs == null) {
			throw new Exception("LDAPAttributeSet Ojbect Can Not Be Null.");
		}
		LDAPAttribute _temp = attrs.getAttribute(key);
		ret = _temp == null ? new String [0] : _temp.getStringValueArray();
		//System.out.println("--attr val:"+ret+"--");
		return ret;
	   }
	
    //把部门LDAP entry转换为GFDept实体
	public GFDept TransferLDAPDept2Entity(LDAPEntry entry, LdapDeptProperties ldapDeptProperties) throws Exception {
		GFDept ret = null;
		try {
			ret = new GFDept();
			ret.setDn(entry.getDN());
			ret.setGuid(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduPersonUnid()));
			ret.setDeptID(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getNo()));
			String DeptParentID = getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduParentId());
			ret.setDeptParentID("".equals(DeptParentID) ? Constants.GF_DEPT_ROOT_KEY : DeptParentID);
			ret.setDiscription(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDescription()));
			ret.setDeptDisplayName(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDisplayname()));
			ret.setParentDeptName(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduParentName()));
			ret.setDeptType(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDeptType()));
			ret.setFduType(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduType()));
			ret.setDetpGrade(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduGrade()));
			ret.setPingYing(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduPy()));
			ret.setParentNo(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getParentNo()));
			ret.setFduYxptno(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduYxptNo()));
			ret.setNewNo(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getNewNo()));
			
		} catch (Exception ex) {
			throw new Exception("Exception::TransferLDAPDept2Entity"
					+ ex.getMessage());
		}
		return ret;
	}// end

	public GFDeptWithMember TransferLDAPDept2GFDeptWithMember(LDAPEntry entry, LdapDeptProperties ldapDeptProperties) {
		GFDeptWithMember ret = new GFDeptWithMember();
		try {

			// ----------------------------- 遍历Domino中可读取的各字段及值 -----------------------------
//			System.out.println("		 ### for domino同步- tj entry.getDN(): " + entry.getDN());
			Object object = null;
			String row = null;
			String createtime_string = null;
			for (Iterator iterator = entry.getAttributeSet().iterator(); iterator.hasNext();) {
				object = iterator.next();
				row = object.toString();
				//System.out.println(" <> " + object.toString() );
				if( this.matchAttribute(row, "createtime") ){
					try {
						createtime_string = this.get_AD_LdapAttributeValue(row).substring(0, 12 );
						//System.out.println( " ### " + createtime_string );
						ret.setDeptCreateDate(createtime_string);
					} catch (Exception e) {
						e.printStackTrace();
						log.error(e.getMessage());
					}
				}
			}
			// ----------------------------- 遍历Domino中可读取的各字段及值 -----------------------------


			ret.setDn(entry.getDN());

			ret.setGuid(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduPersonUnid()));
			ret.setDeptId(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getNo()));
			String DeptParentID = getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduParentId());
			ret.setDeptParentId(StringUtils.isBlank(DeptParentID) ? Constants.GF_DEPT_ROOT_KEY: DeptParentID);
			ret.setDescription(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDescription()));
			ret.setDeptDisplayName(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDisplayname()));
			ret.setParentDeptName(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduParentName()));
			ret.setDeptType(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDeptType()));
			ret.setFduType(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduType()));
			String ldapAttrbuteValByKey = getLDAPAttrbuteValByKey(entry.getAttributeSet(), ldapDeptProperties.getFduGrade());
			if(StrUtil.isNotEmpty(ldapAttrbuteValByKey)) {
				ret.setDetpGrade(Integer.valueOf(ldapAttrbuteValByKey));
			}
			ret.setPinyin(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduPy()));
			ret.setParentNo(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getParentNo()));
			ret.setFduYxptNo(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getFduYxptNo()));

			ret.setNewNo(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getNewNo()));

			// 新增docUniqueId字段，iam专用 2024-01-03
			ret.setDocUniqueId(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapDeptProperties.getDocUniqueId()));

			//遍历Member
			LDAPAttributeSet attributeSet = entry.getAttributeSet();
			Iterator allAttributes = attributeSet.iterator();
			List<String> membersList=new ArrayList();
			while (allAttributes.hasNext()) {
				LDAPAttribute attribute = (LDAPAttribute) allAttributes.next();
				String attributeName = attribute.getName();
				if (attributeName.equalsIgnoreCase("member")|| attributeName.equalsIgnoreCase("uniqueMember")) {
					Enumeration member = attribute.getStringValues();
					while (member.hasMoreElements()) {
						String userdn = (String) member.nextElement();
						membersList.add(userdn);
					}//
				}
			}
			ret.setMembers(membersList);
			//遍历Member结束
		} catch (Exception ex) {
			log.error("把部门LDAP entry转换为GFDeptMember实体报错，entry："+entry+";Exception::TransferLDAPDept2Entity"+ ex.getMessage());
			ret = null;
			//throw new Exception("Exception::TransferLDAPDept2Entity"+ ex.getMessage());
		}
		return ret;
	}// end
	public GFUser TransferLDAPUser2Entity(LDAPEntry entry, LdapUserProperties ldapUserProperties) {
		GFUser ret = new GFUser();
		try {

			// ----------------------------- 遍历Domino中可读取的各字段及值 -----------------------------
//			System.out.println("		 ### for domino同步- tj entry.getDN(): " + entry.getDN());
			Object object = null;
			String row = null;
			String createtime_string = null;
			for (Iterator iterator = entry.getAttributeSet().iterator(); iterator.hasNext();) {
				object = iterator.next();
				row = object.toString();
				//System.out.println(" <> " + object.toString() );
				if( this.matchAttribute(row, "createtime") ){
					try {
						createtime_string = this.get_AD_LdapAttributeValue(row).substring(0, 12 );
						ret.setUsercreatedate(createtime_string);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
			// ----------------------------- 遍历Domino中可读取的各字段及值 -----------------------------

			String _ou = this.ExtractOUFormDN(entry.getDN());
//			System.out.println(" ### for Domino同步- tj _ou: " + _ou);

			// ldap dn
			ret.setDn(entry.getDN());

			// ldap中的 主键 (95FDD559704C187B48257A370002B52C)
			ret.setGuid(getLDAPAttrbuteValByKey(entry.getAttributeSet(),ldapUserProperties.getFduPersonUnid()));

			// 用户的登陆 loginid (lihl)
			if (ldapUserProperties.getUid().equalsIgnoreCase("uid")) {
				String[] uidStrings = getLDAPAttrbuteValsByKey(entry.getAttributeSet(),ldapUserProperties.getUid());
				if (uidStrings != null) {
					if (uidStrings.length == 1) {
						// ret.setUid(uidStrings[0]);//uid
						// login id to lower case
						ret.setUid(uidStrings[0].toLowerCase());// uid
					} else {
						// ret.setUid(uidStrings[0]);//uid
						// login id to lower case
						ret.setUid(uidStrings[0].toLowerCase());// uid
						ret.setTamUid(uidStrings[1]);// tam uid
					}
				}
			}
			// ret.setUid(getLDAPAttrbuteValByKey(entry.getAttributeSet(),getUserMappingPropertieValueByKey(Constants.GF_LADP_USER_ATTR_UID)));

			// 用户的 username (李华龙)
			ret.setName(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getCn()));

			// (李华龙/GFZQ)
			ret.setDisplayName(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getDisplayname()));

			// 8810
			ret.setDeptID(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getDepartment()));

			// 000035110000
			ret.setDeptID2(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getDepartment2()));

			//	3
			ret.setDeptType(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getDeptType()));

			// 信息技术部管理系统开发组
			ret.setDeptName(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getFduDeptName()));

			//	<EMAIL>
			ret.setEmail(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getMail()));

			// 13926093740
			ret.setMoblie(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getMobile()));

			// 020-37888630
			ret.setTelephone(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getTelephoneNumber()));

			// 8000
			ret.setParentNo(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getParentNo()));

			// 8810
			ret.setNewNo(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getNewNo()));

			// LHL
			ret.setPingYing(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getFduPy()));


			ret.setOfficeResNumber(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(), "officeresnumber"));
			ret.setOu(_ou);
			// mail server meta data
			ret.setMailServer(this.getMailServer(entry.getAttributeSet()));
			ret.setMailFilePath(this.getMailFilePath(entry.getAttributeSet()));
			// comment data for store staff
			ret.setComment(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getComment()));
			// user is Leave?
			ret.setLevel0(getLDAPAttrbuteValByKey(
					entry.getAttributeSet(),
					ldapUserProperties.getLevel0()));

		} catch (Exception ex) {
			//throw new Exception("TransferLDAPUser2Entity exception::" + ex.getStackTrace());
			log.info("转换失败的LDAPEntry："+entry);
			log.info("转换LDAPEntry失败错误信息："+ex.getMessage());
			ret = null;
		}
		return ret;
	}// end


	public String ExtractOUFormDN(String DN) {
		String ret = "";
		try {
			for (String i : DN.split(",")) {
				if (i.contains("OU=") || i.contains("ou=")) {
					ret = i.subSequence(3, i.length()).toString();
				}
			}
		} catch (Exception ex) {

		}
		return ret;
	}// end

	//ldap中获取mail server的地址
	public String getMailServer(LDAPAttributeSet attrs){
		String ret="";
		try{
			String orginalMailServerMetaData=getLDAPAttrbuteValByKey(attrs,getUserMappingPropertieValueByKey(Constants.GF_LDAP_USER_ATTR_MAILSERVER));
			String[] t = orginalMailServerMetaData.split("/");
			if (t.length > 0) {
				String[] t2 = t[0].split("-");
				if (t2.length > 1) {
					if (t2[1].equalsIgnoreCase("hkoa")) {
						ret = "oahk";
					} else if (t2[1].equalsIgnoreCase("qhoa")) {
						ret = "qhhk";
					} else if (t2[1].equalsIgnoreCase("xdoa")) {
						ret = "xdhk";
					} else {
						ret = t2[1];
					}
					ret += ".gf.com.cn";
				}//
			}
		}catch(Exception ex){
			ex.getStackTrace();
		}
		return ret;
	}//end

	//获取ldap中，oa的mail file路径
	public String getMailFilePath(LDAPAttributeSet attrs) {
		String ret = "";
		try {
			String originalMailFile=getLDAPAttrbuteValByKey(attrs,getUserMappingPropertieValueByKey(Constants.GF_LDAP_USER_ATTR_MAILFILE));
			if (StringUtils.isNotBlank(originalMailFile)) {
				if(originalMailFile.contains(".nsf")){
					ret += "/" + originalMailFile.replace("\\", "/");
				}else{
					ret += "/" + originalMailFile.replace("\\", "/") + ".nsf";
				}
			}
		} catch (Exception ex) {
			ex.getStackTrace();
		}
		return ret;
	}//end
}
