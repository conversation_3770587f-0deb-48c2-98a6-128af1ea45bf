package net.bingosoft.fuse.iam.api.tenant.models;

import fly.core.meta.annotation.Summary;

import java.util.List;

public class IamClientDataAuthDetailsListVo {

    @Summary("组织名称")
    protected String orgName;

    @Summary("组织id")
    protected String orgId;

    @Summary("认证Id")
    protected String authId;

    @Summary("客户端名称")
    protected String clientName;

    @Summary("每日请求数量")
    protected Integer requestNum;

    @Summary("接口名称")
    protected String interfaceNames;

    @Summary("接口id")
    protected List<String> ids;

    @Summary("联系人")
    protected String contacts;

    @Summary("联系人")
    protected String contactsBy;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getRequestNum() {
        return requestNum;
    }

    public void setRequestNum(Integer requestNum) {
        this.requestNum = requestNum;
    }

    public String getInterfaceNames() {
        return interfaceNames;
    }

    public void setInterfaceNames(String interfaceNames) {
        this.interfaceNames = interfaceNames;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactsBy() {
        return contactsBy;
    }

    public void setContactsBy(String contactsBy) {
        this.contactsBy = contactsBy;
    }
}
