package net.bingosoft.fuse.iam.api.tenant.msg.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.tenant.msg.manager.GfIamMessageNotifyManager;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.enums.NotifyType;
import net.bingosoft.fuse.iam.common.managers.messages.IamMessageNotifyManager;
import net.bingosoft.fuse.iam.common.message.listeners.MessageNotifyUserListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.mail.Message;

@Component
@Slf4j
public class MsgService {
    @Autowired
    GfIamMessageNotifyManager messageNotifyManager;
    @Async
    public void sendMsg(NotifyType notifyType,String sendMobile,IamUserEntity user){
        if(ObjectUtil.isEmpty(notifyType)) {
            return;
        }
        if(StrUtil.isEmpty(sendMobile)) {
            log.info("接收手机号不能为空！");
        }
        messageNotifyManager.sendUserInit(sendMobile,user, notifyType, false);
    }

}
