package net.bingosoft.fuse.iam.api.tenant.managers;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import fly.core.data.model.RowMap;
import fly.core.security.Security;
import fly.core.security.tenant.TenantInfo;
import fly.core.web.exception.BadRequestException;
import fly.core.web.exception.NotFoundException;
import fly.data.common.query.QueryResult;
import fly.lang.New;
import fly.lang.Pair;
import fly.lang.generator.ShortCode;
import fly.lang.util.Collections2;
import fly.lang.util.Strings;
import fly.orm.dao.Dao;
import fly.orm.dao.DaoOperations;
import fly.orm.dao.query.CriteriaQuery;
import lombok.extern.slf4j.Slf4j;
import net.bingosoft.fuse.iam.api.tenant.acm.dto.AcmOrgDto;
import net.bingosoft.fuse.iam.api.tenant.acm.dto.AcmUserOrgDto;
import net.bingosoft.fuse.iam.api.tenant.acm.service.AcmApiService;
import net.bingosoft.fuse.iam.api.tenant.acm.vo.AcmMemberEmployee;
import net.bingosoft.fuse.iam.api.tenant.acm.vo.AcmMemberOrg;
import net.bingosoft.fuse.iam.api.tenant.acm.vo.AcmOrg;
import net.bingosoft.fuse.iam.api.tenant.util.CommonUtil;
import net.bingosoft.fuse.iam.api.tenant.util.DictUtil;
import net.bingosoft.fuse.iam.api.tenant.vo.IamOrgVo;
import net.bingosoft.fuse.iam.api.tenant.vo.IamUserOrgOrderVo;
import net.bingosoft.fuse.iam.api.tenant.vo.OrgParentOrgVo;
import net.bingosoft.fuse.iam.common.builders.IamOrgBuilder;
import net.bingosoft.fuse.iam.common.entities.users.IamOrgEntity;
import net.bingosoft.fuse.iam.common.entities.users.IamTenantEntity;
import net.bingosoft.fuse.iam.common.entities.users.IamUserEntity;
import net.bingosoft.fuse.iam.common.enums.OrgType;
import net.bingosoft.fuse.iam.common.events.orgs.IamOrgCreateEvent;
import net.bingosoft.fuse.iam.common.managers.users.IamOrgManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserManager;
import net.bingosoft.fuse.iam.common.managers.users.IamUserOrgManager;
import net.bingosoft.fuse.iam.common.models.users.IamOrgCreation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/*
* iamUser的一些扩展方法
* */
@Service
@Slf4j
public class GfIamOrgManager {
    @Value("${fly.variable.tenant-id.default-value:#{null}}")
    protected String defaultValue;
    @Autowired
    protected IamUserManager iamUserManager;
    @Autowired
    protected GfIamUserManager iamUserManagerGF;
    @Autowired
    protected ApplicationContext applicationContext;
    @Autowired
    protected IamOrgManager iamOrgManager;
    @Autowired
    protected IamUserOrgManager iamUserOrgManager;
    @Autowired
    protected GfIamUserOrgManager gfIamUserOrgManager;
    @Autowired
    protected Dao dao;
    @Autowired
    protected AcmApiService acmApiService;
    @Autowired
    CommonUtil commonUtil;
    @Value("${ldap.baseDn}")
    String baseDn;
    @Value("${job.url}")
    String jobUrl;
    @Value("${job.auth}")
    String jobAuth;

    public IamOrgEntity create(IamOrgCreation creation) {
        creation.setTenantId(this.getRootTenantId());
        AtomicReference<IamOrgEntity> result = new AtomicReference(new IamOrgEntity());
        this.dao.doInTransaction(() -> {
            Assert.hasText(creation.getTenantId(), "Tenant id required");
            IamOrgEntity org = IamOrgBuilder.of(creation).build();
            if (Strings.isNotEmpty(org.getParentId())) {
                IamOrgEntity parent = (IamOrgEntity)((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).id(org.getParentId()).forUpdate()).singleOrNull();
                if (null == parent) {
                    throw new NotFoundException("父组织不存在！");
                }

                Pair<Integer, String> pairx = iamOrgManager.computeIndex(parent.getId(), parent.getPath(), parent.getTenantId());
                org.setChildIndex((Integer)pairx.getLeft());
                org.setPath((String)pairx.getRight());
                this.validScale(org.getPath());
                org.setChildTenantId(parent.getChildTenantId());
                org.setKindId(parent.getKindId());
                org.setCode(parent.getCode()+","+org.getId());
            } else {
                validTypeAndKind(creation);
                org.setKindId(creation.getKindId());
                Pair<Integer, String> pair = this.computeIndex();
                org.setChildIndex((Integer)pair.getLeft());
                org.setPath((String)pair.getRight());
            }

            this.dao.retry(1).onDuplicateKey(() -> {
                IamOrgEntity var10000 = (IamOrgEntity)this.dao.insertWithProtected(org);
            });
            result.set(org);
        });
        this.applicationContext.publishEvent(new IamOrgCreateEvent((IamOrgEntity)result.get()));
        return (IamOrgEntity)result.get();
    }
    /**
     * 获取根组织id
     */
    private String getRootTenantId() {
        TenantInfo tenant = Security.getCurrentTenant();
        if (null != tenant && null != tenant.getId()) {
            String parentId = tenant.getParentId();
            String tenantId = tenant.getId();
            return null == parentId ? tenantId : parentId;
        } else {
            return this.defaultValue;
        }
    }

    /*
    * 获取Org的详细信息，包括群组成员和用户成员
    *
    * */
    public IamOrgVo getDetails(String orgId) {
        IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, orgId);
        if(StrUtil.isNotEmpty(iamOrgVo.getParentId())){
            IamOrgEntity iamOrgEntity = iamOrgManager.find(iamOrgVo.getParentId());
            if(ObjectUtil.isNotEmpty(iamOrgEntity)) {
                OrgParentOrgVo parentOrg = new OrgParentOrgVo();
                parentOrg.setOrgName(iamOrgEntity.getName());
                parentOrg.setOrgId(iamOrgVo.getParentId());
                iamOrgVo.setParentOrg(parentOrg);
            }
        }
        List<String> groupList = iamOrgManager.createQuery()
                .where("#parent_id = ?", orgId)
                .where("#deleted_ = ?", false)
                .execute().list().stream().map(e -> e.getId()).collect(Collectors.toList());
        iamOrgVo.setGroupMember(groupList);
        List<String> memberList = gfIamUserOrgManager.createQuery().alias("iuo").leftJoin(IamUserEntity.class,"iu","iuo.user_id = iu.id_ and iu.deleted_ = false").where("iuo.org_id = ?", orgId).select("user_id").orderBy("oa_order desc,ad_order desc").scalars();
        iamOrgVo.setMember(memberList);
        return iamOrgVo;
    }

    public void updateOrgMember(List<String> oldUserIds, List<String> newUserIds, String orgId){
        if (ObjectUtil.isNull(newUserIds)) {
            newUserIds = new ArrayList<>();
        }
        List<String> removeList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(oldUserIds)&&ObjectUtil.isNotEmpty(newUserIds)) {
            HashSet<String> oldUserIdSet = new HashSet<>(oldUserIds);
            HashSet<String> newUserIdSet = new HashSet<>(newUserIds);
            removeList = oldUserIds.stream().filter(e -> !newUserIdSet.contains(e)).collect(Collectors.toList());
            addList = newUserIds.stream().filter(e->!oldUserIdSet.contains(e)).collect(Collectors.toList());
        }else if(ObjectUtil.isEmpty(oldUserIds)&&ObjectUtil.isNotEmpty(newUserIds)){
            // 原本为空，则新增为newUserIds
            addList = newUserIds;
        }else if(ObjectUtil.isEmpty(newUserIds)&&ObjectUtil.isNotEmpty(oldUserIds)){
            removeList = oldUserIds;
        }
        // 2.删除用户组织表中的信息
        gfIamUserOrgManager.createQuery().filter("primary",false).in("user_id","user_id",removeList).filter("orgId",orgId).delete();
        // 增加用户-组织关系
        if(ObjectUtil.isNotEmpty(addList)) {
            gfIamUserOrgManager.addUserOrg(addList,orgId,false);
        }
        // 更新用户排序
        iamUserManagerGF.changeOrder(newUserIds,orgId);
    }

    public void updateOrgGroup(List<String> oldGroup, List<String> newGroup, String orgId) throws Exception{
        List<String> removeList = new ArrayList<>();// 需要移出该组织的
        List<String> updateList = new ArrayList<>();// 需要更新的
        if(ObjectUtil.isNotEmpty(oldGroup)&&ObjectUtil.isNotEmpty(newGroup)) {
            HashSet<String> oldOrgIds = new HashSet<>(oldGroup);
            HashSet<String> newOrgIds = new HashSet<>(newGroup);
            removeList = oldGroup.stream().filter(e -> !newOrgIds.contains(e)).collect(Collectors.toList());
            updateList = newGroup.stream().filter(e->!oldOrgIds.contains(e)).collect(Collectors.toList());
        }else if(ObjectUtil.isEmpty(oldGroup)&&ObjectUtil.isNotEmpty(newGroup)){
            // 原本为空，则新增为newUserIds
            updateList = newGroup;
        }else if(ObjectUtil.isEmpty(newGroup)&&ObjectUtil.isNotEmpty(oldGroup)){
            removeList = oldGroup;
        }
        // 设置移出的群组parentId为""
        for (String removeId : removeList) {
            HashMap<String,Object> properties = new HashMap<>();
            properties.put("parentId","");
            iamOrgManager.dao().update(IamOrgEntity.class,removeId,properties);
        }
        // 设置更新的群组parentId为orgId
        newGroup.forEach(id->{
            transferOrg(id,orgId);
        });
    }


    public List<RowMap> list(List<String> ids) {
        String join = StrUtil.join("','", ids);
        List<RowMap> list = iamOrgManager.dao().createSQLQuery("select io1.id_ as id,io1.name_ as name,io1.simple_spell,io1.email_,\n" +
                "io1.email_internet,io1.other_email,io2.name_ as parentOrgName,\n" +
                "io1.parent_id,io1.sort_order,io1.business_code,io1.self_business_code,\n" +
                "io1.manager\n" +
                "from iam_org as io1\n" +
                "left join iam_org io2 on io1.parent_id = io2.id_ where io1.id_ in('"+join+"')").execute().list();

        for (RowMap rowMap : list) {
            List<String> userIds = iamUserManager.createQuery().filter("orgId", rowMap.getString("id")).select("name_").scalars();
            String names = userIds.stream().collect(Collectors.joining(","));
            rowMap.put("member",names);
        }
        return list;
    }

    public IamOrgCreation initOrg(IamOrgVo creation){
        IamOrgCreation orgCreation = new IamOrgCreation();
        BeanUtils.copyProperties(creation,orgCreation);
        if(ObjectUtil.isNotEmpty(creation.getParentOrg())){
            orgCreation.setParentId(creation.getParentOrg().getOrgId());
        }
        Map<String,Object> map = new HashMap<>();
        // 如果该群组为公共群组或者ad群组，设置父组织
        if(creation.getSysType().equals("3")) {
            IamOrgEntity adGroup = null;
            String valueOrTitle = "";
            try {
                valueOrTitle = DictUtil.getValueOrTitle("ADGROUP", null, "GROUP_NAME");
                if(StrUtil.isEmpty(valueOrTitle)) {
                    throw new RuntimeException("无值为ADGROUP的字典");
                }
                adGroup = iamOrgManager.dao().createQuery(IamOrgEntity.class).filter("name", valueOrTitle).first();
            }catch (Exception e){
                throw new RuntimeException("组织中无名称为"+valueOrTitle+"的群组");
            }
            orgCreation.setParentId(adGroup.getId());
        }else if(creation.getSysType().equals("2")){
            IamOrgEntity publicGroup = null;
            String publicGroupName = "";
            try {
                publicGroupName = DictUtil.getValueOrTitle("PUBLICGROUP", null, "GROUP_NAME");
                if(StrUtil.isEmpty(publicGroupName)) {
                    throw new RuntimeException("无值为PUBLICGROUP的字典");
                }
                publicGroup = iamOrgManager.dao().createQuery(IamOrgEntity.class).filter("name", publicGroupName).first();
            }catch (Exception e){
                throw new RuntimeException("组织中无名称为"+publicGroupName+"的群组");
            }
            orgCreation.setParentId(publicGroup.getId());
        }
        if(StringUtils.isNotEmpty(creation.getSimpleSpell())){
            map.put("simpleSpell",creation.getSimpleSpell());
        }
        if(StringUtils.isNotEmpty(creation.getOaSimpleSpell())){
            map.put("oaSimpleSpell",creation.getOaSimpleSpell());
        }
        if(StringUtils.isNotEmpty(creation.getSelfBusinessCode())){
            map.put("selfBusinessCode",creation.getSelfBusinessCode());
        }
        if(StringUtils.isNotEmpty(creation.getEmailInternet())){
            map.put("emailInternet",creation.getEmailInternet());
        }
        if(StringUtils.isNotEmpty(creation.getOtherEmail())){
            map.put("otherEmail",creation.getOtherEmail());
        }
        if(StringUtils.isNotEmpty(creation.getEmailDomain())){
            map.put("emailDomain",creation.getEmailDomain());
        }
        if(StringUtils.isNotEmpty(creation.getSysType())){
            map.put("sysType",creation.getSysType());
        }
        if(StringUtils.isNotEmpty(creation.getDeptType())){
            map.put("deptType",creation.getDeptType());
        }
        if(StringUtils.isNotEmpty(creation.getGroupType())){
            map.put("groupType",creation.getGroupType());
        }
        if(StringUtils.isNotEmpty(creation.getManager())){
            map.put("manager",creation.getManager());
        }
        if(StringUtils.isNotEmpty(creation.getDescription())){
            map.put("description",creation.getDescription());
        }
        if(StringUtils.isNotEmpty(creation.getBusinessCode())){
            map.put("businessCode",creation.getBusinessCode());
        }
        if(StrUtil.isNotEmpty(creation.getSortOrder2())){
            map.put("sortOrder2",creation.getSortOrder2());
        }
        orgCreation.setProperties(map);
        orgCreation.setTenantId( Strings.firstNonEmpty(new String[]{creation.getTenantId(), Security.getCurrentTenantId()}));
        return orgCreation;
    }

    public AcmOrgDto initAcmOrg(IamOrgVo creation,String operationType) {
        AcmOrgDto acmOrgDto = new AcmOrgDto();
        acmOrgDto.setId(creation.getAcmOrgId());
        String sysType = creation.getSysType();
        // 只有添加才要sysType参数
        if(operationType.equals("add")) {
            acmOrgDto.setSysType(sysType);
        }
        if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&StrUtil.isNotEmpty(creation.getParentOrg().getOrgId())) {
            IamOrgVo iamOrgEntity = iamOrgManager.find(IamOrgVo.class, creation.getParentOrg().getOrgId());
            acmOrgDto.setParentOrg(new AcmUserOrgDto(iamOrgEntity.getSimpleSpell()));
        }
        if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&ObjectUtil.isNotEmpty(creation.getParentOrg().getSortOptions())){
            OrgParentOrgVo sortInfo = creation.getParentOrg();
            AcmUserOrgDto parentOrg = acmOrgDto.getParentOrg();
//            parentOrg.setOrgId(sortInfo.getSortOrgId());
            if("2".equals(sortInfo.getSortOptions())){
                IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, sortInfo.getSortOrgId());
                parentOrg.setSort(iamOrgVo.getAcmOrgId().toLowerCase());
            }else if("0".equals(sortInfo.getSortOptions())){
                parentOrg.setSort("one");
            }
            acmOrgDto.setParentOrg(parentOrg);
        }
//        if(ObjectUtil.isNotEmpty(creation.getOtherOrg())) {
//            List<AcmUserOrgDto> otherOrgs = creation.getOtherOrg().stream().map(e -> {
//                IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, e);
//                return new AcmUserOrgDto(iamOrgVo.getSimpleSpell());
//            }).collect(Collectors.toList());
//            acmOrgDto.setOtherOrgs(otherOrgs);
//        }
        if(ObjectUtil.isNotEmpty(creation.getMember())) {
            // 要按照排序传输
            List<String> acmUserIds = iamUserManagerGF.getUsernameByIds(creation.getMember());
            List<AcmMemberEmployee> memberEmployees = acmUserIds.stream().map(AcmMemberEmployee::new).collect(Collectors.toList());
            acmOrgDto.setMemberEmployee(memberEmployees);
        }
        if(ObjectUtil.isNotEmpty(creation.getGroupMember())) {
            List<AcmMemberOrg> groupSimpleSpell = iamOrgManager.createQuery().in("id_", "id_", creation.getGroupMember()).list().stream().map(e -> (String) e.getProperty("simpleSpell")).filter(e -> StrUtil.isNotEmpty(e)).map(AcmMemberOrg::new).collect(Collectors.toList());
            acmOrgDto.setMemberOrg(groupSimpleSpell);
        }
        acmOrgDto.setSimpleSpell(creation.getSimpleSpell());
        acmOrgDto.setOaSimpleSpell(creation.getOaSimpleSpell());
        if(!sysType.equals("3")) {
            if (ObjectUtil.isNotEmpty(creation.getSortOrder2())) {
                acmOrgDto.setSerial(creation.getSortOrder2());
            }
        }
        acmOrgDto.setBusinessCode(creation.getBusinessCode());
        acmOrgDto.setSelfBusinessCode(creation.getSelfBusinessCode());
        acmOrgDto.setOtherEmail(creation.getOtherEmail());
        acmOrgDto.setDeptType(creation.getDeptType());
        if(sysType.equals("1"))// 公共群组,AD群组不传输groupType参数
        {
            acmOrgDto.setGroupType(creation.getGroupType());
        }
        acmOrgDto.setManager(creation.getManager());
        acmOrgDto.setDescription(creation.getDescription());
        acmOrgDto.setName(creation.getName());
        return acmOrgDto;
    }
    public AcmOrgDto initUpdateOrg(IamOrgVo creation) {
        AcmOrgDto acmOrgDto = new AcmOrgDto();
        acmOrgDto.setId(creation.getAcmOrgId());
        String sysType = getSysType(creation);
        AcmOrg acmOrg = acmApiService.getOrg(acmOrgDto.getId(), null);
        if(ObjectUtil.isEmpty(acmOrg)) {
            throw new RuntimeException("[acm]查询不到id为"+acmOrgDto.getId()+"的组织");
        }
        // 如果为部门群组，查询父级组织是否变更
        if("1".equals(sysType)) {
            // 查询该组织此时的父级acmId
            List<IamOrgEntity> parentOrg = iamOrgManager.createQuery().filter("id", creation.getParentOrg().getOrgId()).list();
            if (ObjectUtil.isEmpty(parentOrg)) {
                throw new RuntimeException("[iam]查询不到id为" + creation.getParentOrg().getOrgId() + "的父级组织组织");
            }
            // 查看父级组织是否变更
            IamOrgEntity iamOrgEntity = parentOrg.get(0);
            if (!StrUtil.equals(acmOrg.getParentOrg().getOrgId(), (String) iamOrgEntity.getProperty("acmOrgId"))) {
                acmOrgDto.setParentOrg(new AcmUserOrgDto((String) iamOrgEntity.getProperty("simpleSpell")));
            }
            if(ObjectUtil.isNotEmpty(creation.getParentOrg())&&ObjectUtil.isNotEmpty(creation.getParentOrg().getSortOptions())){
                OrgParentOrgVo sortInfo = creation.getParentOrg();
                AcmUserOrgDto parentOrgSortInfo = acmOrgDto.getParentOrg();
                if("2".equals(sortInfo.getSortOptions())){
                    IamOrgVo iamOrgVo = iamOrgManager.find(IamOrgVo.class, sortInfo.getSortOrgId());
                    if(ObjectUtil.isNotEmpty(iamOrgVo.getAcmOrgId())) {
                        parentOrgSortInfo.setSort(iamOrgVo.getAcmOrgId().toLowerCase());
                    }
                }else if("0".equals(sortInfo.getSortOptions())){
                    parentOrgSortInfo.setSort("one");
                }
                parentOrgSortInfo.setSimpleSpell((String) iamOrgEntity.getProperty("simpleSpell"));
                acmOrgDto.setParentOrg(parentOrgSortInfo);
            }
        }
        if(ObjectUtil.isNotEmpty(creation.getMember())) {
            // 排除掉主部门为该组织的用户
//            List<String> mainUserIds = gfIamUserOrgManager.createQuery().filter("primary", true).filter("org_id", creation.getId()).list().stream().map(e -> e.getUserId()).filter(e->StrUtil.isNotEmpty(e)).collect(Collectors.toList());
            List<String> memberIds = creation.getMember().stream().filter(e -> StrUtil.isNotEmpty(e)).collect(Collectors.toList());
            // 要按照排序传输
            List<String> acmUserIds = iamUserManagerGF.getUsernameByIds(memberIds);
            List<AcmMemberEmployee> memberEmployees = acmUserIds.stream().map(AcmMemberEmployee::new).collect(Collectors.toList());
            acmOrgDto.setMemberEmployee(memberEmployees);
        }
        if(ObjectUtil.isEmpty(creation.getMember())){
            acmOrgDto.setMemberEmployee(new ArrayList<>());
        }
        if(ObjectUtil.isNotEmpty(creation.getGroupMember())) {
            List<AcmMemberOrg> groupMember = creation.getGroupMember().stream().map(e -> {
                IamOrgVo iamOrgEntity1 = iamOrgManager.find(IamOrgVo.class, e);
                return new AcmMemberOrg(iamOrgEntity1.getSimpleSpell());
            }).collect(Collectors.toList());
            acmOrgDto.setMemberOrg(groupMember);
        }
        if(ObjectUtil.isEmpty(creation.getGroupMember())){
            acmOrgDto.setMemberOrg(new ArrayList<>());
        }
        if(!StrUtil.equals(acmOrg.getSimpleSpell(),creation.getSimpleSpell())) {
            acmOrgDto.setSimpleSpell(creation.getSimpleSpell());
        }
        if(!StrUtil.equals(acmOrg.getOaSimpleSpell(),creation.getOaSimpleSpell())) {
            acmOrgDto.setOaSimpleSpell(creation.getOaSimpleSpell());
        }
        if(!"3".equals(sysType)) {
            if (ObjectUtil.isNotEmpty(creation.getSortOrder2()) && !StrUtil.equals(acmOrg.getSerial(), creation.getSortOrder2().toString())) {
                acmOrgDto.setSerial(creation.getSortOrder2().toString());
            }
        }
        if("1".equals(sysType)) {
            if (!StrUtil.equals(acmOrg.getBusinessCode(), creation.getBusinessCode())) {
                acmOrgDto.setBusinessCode(creation.getBusinessCode());
            }
            if(!StrUtil.equals(acmOrg.getSelfBusinessCode(),creation.getSelfBusinessCode())) {
                acmOrgDto.setSelfBusinessCode(creation.getSelfBusinessCode());
            }
        }
        if(!StrUtil.equals(acmOrg.getOtherEmail(),creation.getOtherEmail())) {
            acmOrgDto.setOtherEmail(creation.getOtherEmail());
        }
        if(!StrUtil.equals(acmOrg.getManager(),creation.getManager())) {
            acmOrgDto.setManager(creation.getManager());
        }
        if(!StrUtil.equals(acmOrg.getDescription(),creation.getDescription())) {
            acmOrgDto.setDescription(creation.getDescription());
        }
        if(!StrUtil.equals(acmOrg.getName(),creation.getName())) {
            acmOrgDto.setName(creation.getName());
        }
        return acmOrgDto;
    }

//    public void updateOrgOrder(String orgId, List<String> userIds,Boolean primary) {
//        IamOrgEntity entity = iamOrgManager.find(orgId);
//        String lastAcmUserId = "";
//        if(ObjectUtil.isNotEmpty(userIds)&&StrUtil.isNotEmpty(orgId)) {
//            for (int i = 0; i < userIds.size(); i++) {
//                GfIamUserOrgEntity entityGF = new GfIamUserOrgEntity();
//                entityGF.setPrimary(primary);
//                entityGF.setOrgId(orgId);
//                String userid = userIds.get(i);
//                entityGF.setUserId(userid);
//                entityGF.setSortOrder(i);
//                // 如果是主部门需要一起修改用户表的排序
//                iamUserManager.createQuery().filter("id",userid).update("sortOrder",i);
//                iamUserManager.dao().updateSettable(GfIamUserOrgEntity.class,entityGF);
//                IamUserEntity iamUserEntity = iamUserManager.find(userid);
//                // 发送acm
//                Object acmUserIdObj = iamUserEntity.getProperty("acmUserId");
//                if(ObjectUtil.isNotEmpty(acmUserIdObj)) {
//                    String acmUserId = (String)acmUserIdObj;
//                    AcmUserEmpDto acmUserEmpDto = new AcmUserEmpDto();
//                    acmUserEmpDto.setId(acmUserId);
//                    AcmUserOrgDto acmUserOrgDto = new AcmUserOrgDto();
//                    acmUserOrgDto.setSimpleSpell((String) entity.getProperty("simpleSpell"));
//                    acmUserOrgDto.setIsAddGroup(true);
//                    if(i==0)
//                        acmUserOrgDto.setSort("one");
//                    else
//                        acmUserOrgDto.setSort(lastAcmUserId);
//                    if(primary)
//                        acmUserEmpDto.setParentOrg(acmUserOrgDto);
//                    else
//                        acmUserEmpDto.setOtherOrgs(Arrays.asList(acmUserOrgDto));
//                    acmApiService.updateEmp(acmUserEmpDto,);
//                    lastAcmUserId = acmUserId;
//                }
//            }
//        }
//    }

    public IamOrgEntity getInfo(String orgId) {
        List<IamOrgEntity> list = iamOrgManager.createQuery().alias("o")
                .filter("id", orgId)
                .leftJoin(IamOrgEntity.class, "o1", "o.parent_id = o1.id_")
                .selectExtra("o1.name_ parent_org_name")
                .list();
        return ObjectUtil.isNotEmpty(list)?list.get(0):new IamOrgEntity();

    }

    public static List<String> toPaths(String path, boolean includeSelf) {
        String[] parts = StringUtils.split(path, ',');
        List<String> paths = new ArrayList(parts.length);

        for(int i = 0; i < parts.length - 1; ++i) {
            String part = parts[i];
            if (i == 0) {
                paths.add(part);
            } else {
                paths.add((String)paths.get(i - 1) + ',' + part);
            }
        }

        if (includeSelf) {
            paths.add(path);
        }

        return paths;
    }
    private void validScale(String path) {
        String[] paths = StringUtils.split(path, '.');
        if (paths.length > 21) {
            throw new BadRequestException("组织(包括已删除组织)层级不能超过20层!");
        }
    }
    private static void validTypeAndKind(IamOrgCreation creation) {
        Assert.hasText(creation.getKindId(), "kindId required");
        Assert.notNull(creation.getType(), "orgType required");
        if (creation.getKindId().equals("P") && OrgType.STANDARD.equals(creation.getType())) {
            throw new BadRequestException("不允许创建多个默认组织类型的根组织！");
        }
    }
    private Pair<Integer, String> computeIndex() {
        Integer childIndex = (Integer)this.dao.createQuery(IamOrgEntity.class).where("#parentId is null", new Object[0]).select(new String[]{"max(#childIndex)"}).scalarOrNull(Integer.class);
        if (childIndex == null) {
            childIndex = 1;
        } else {
            childIndex = childIndex + 1;
        }

        return new Pair(childIndex, String.valueOf(childIndex));
    }
    public Pair<Integer, String> computeIndex(String parentId, String parentPath, String parentTenantId) {
        Integer childIndex = (Integer)((CriteriaQuery)((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).withoutDefaultFiltered()).where("#parentId = :parentId and #tenantId = :tenantId", new Object[0]).params(New.hashMap("parentId", parentId, "tenantId", parentTenantId))).select(new String[]{"max(#childIndex)"}).scalarOrNull(Integer.class);
        return this.computeIndex(childIndex, parentPath);
    }
    private Pair<Integer, String> computeIndex(Integer maxChildIndex, String parentPath) {
        int childIndex;
        if (maxChildIndex == null) {
            childIndex = 1;
        } else {
            childIndex = maxChildIndex + 1;
        }

        String pathSuffix = childIndex > 999999 ? ShortCode.random56() : String.valueOf(childIndex);
        String path = parentPath + '.' + pathSuffix;
        return new Pair(childIndex, path);
    }
    protected boolean couldMoveOrg(String sourceId) {
        String parentId = ((TenantInfo)Objects.requireNonNull(Security.getCurrentTenant())).getParentId();
        boolean exists = this.dao.createQuery(IamTenantEntity.class).where("#orgId =? ", new Object[]{sourceId}).exists();
        return !Strings.isEmpty(parentId) || !exists;
    }
    public void transferOrg(String sourceId, String targetId) {
        Assert.notNull(sourceId, "sourceId is required!");
        Assert.notNull(targetId, "targetId is required!");
        if (Strings.equalsIfNonEmpty(sourceId, targetId)) {
            throw new IllegalArgumentException("转移组织不能相同！");
        } else {
            boolean couldMoveOrg = this.couldMoveOrg(sourceId);
            if (!couldMoveOrg) {
                throw new BadRequestException("已被绑定子租户的组织不能被迁移！");
            } else {
                this.dao.doInTransaction(() -> {
                    IamOrgEntity sourceOrg = (IamOrgEntity)((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).id(sourceId).forUpdate()).single();
                    String sourcePath = sourceOrg.getCode();
                    String sourceBindDn = (String)sourceOrg.getProperty("bindDn");
                    List<IamOrgEntity> sourceChildren = ((CriteriaQuery)((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).where("code_ like ? ", new Object[]{sourceOrg.getCode() + ',' + "%"}).forUpdate()).limit(5000)).list();
                    if (Collections2.isNotEmpty(sourceChildren) && sourceChildren.stream().anyMatch((s) -> {
                        return s.getId().equals(targetId);
                    })) {
                        throw new IllegalArgumentException("不支持将组织转移到下属组织！");
                    } else {
                        IamOrgEntity targetOrg = (IamOrgEntity)((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).id(targetId).forUpdate()).single();
                        Pair<Integer, String> pair = this.computeIndex(targetId, targetOrg.getPath(), targetOrg.getTenantId());
                        sourceOrg.setChildIndex((Integer)pair.getLeft());
                        sourceOrg.setPath((String)pair.getRight());
                        sourceOrg.setProperty("bindDn","OU="+sourceOrg.getName()+","+targetOrg.getProperty("bindDn"));
                        this.validScale(sourceOrg.getPath());
                        sourceOrg.setCode(targetOrg.getCode()+","+sourceOrg.getId());
                        if (Collections2.isNotEmpty(sourceChildren)) {
                            sourceChildren.forEach((sc) -> {
                                sc.setPath(sc.getId());
                                sc.setCode(sourceOrg.getCode()+ StringUtils.removeStart(sc.getCode(), sourcePath));
                                sc.setProperty("bindDn",StringUtils.removeEnd((String) sc.getProperty("bindDn"), sourceBindDn)+sourceOrg.getProperty("bindDn"));
                                ((DaoOperations)this.dao.allowWriteProtected()).update(IamOrgEntity.class, sc.getId(), New.hashMap("path", sc.getPath(),"code", sc.getCode(),"bindDn",sc.getProperty("bindDn")));
                            });
                        }
                        HashMap<String, Object> updatePro = new HashMap<>();
                        updatePro.put("parentId", targetId);
                        updatePro.put("childIndex", sourceOrg.getChildIndex());
                        updatePro.put("path", sourceOrg.getId());
                        updatePro.put("code",sourceOrg.getCode());
                        updatePro.put("bindDn",sourceOrg.getProperty("bindDn"));
                        ((DaoOperations)this.dao.allowWriteProtected()).update(IamOrgEntity.class, sourceOrg.getId(), updatePro);
                    }
                });
            }
        }
    }

    public QueryResult findTenantRootOrg(String kindId) {
        if ("P".equals(kindId)) {
            IamTenantEntity iamTenantEntity = (IamTenantEntity)((CriteriaQuery)this.dao.createQuery(IamTenantEntity.class).id(Security.getCurrentTenantId()).withoutDefaultFiltered()).firstOrNull();
            Assert.notNull(iamTenantEntity, "current tenant is not allowed empty!");
            String parentOrgId = "";
            if (Strings.isEmpty(iamTenantEntity.getOrgId())) {
                parentOrgId = Security.getCurrentTenantId();
            } else {
                parentOrgId = iamTenantEntity.getOrgId();
            }

            return ((CriteriaQuery)this.dao.createQuery(IamOrgEntity.class).withoutDefaultFiltered()).id(parentOrgId).execute();
        } else {
            return this.dao.createQuery(IamOrgEntity.class).where("#kindId=? and #parentId is null", new Object[]{kindId}).execute();
        }
    }

    public String getSysType(IamOrgVo orgVo) {
        String publicId = commonUtil.getpublicGroupInfo().getId();
        String adId = commonUtil.getADGroupInfo().getId();
        List<IamOrgEntity> list = iamOrgManager.createQuery().filter("simpleSpell", orgVo.getSimpleSpell()).list();
        if(ObjectUtil.isEmpty(list)) {
            throw new RuntimeException("[iam-getSysType]：找不到simpleSpell为"+orgVo.getSimpleSpell()+"的组织");
        }
        String code = list.get(0).getCode();
        if((code+",").contains(publicId)) {
            return "2";
        } else if((code+",").contains(adId)) {
            return "3";
        } else {
            return "1";
        }
    }

    public void updateLdapBindDn(String orgId){
        List<String> orgPath = new ArrayList<>();
        IamOrgEntity cur = iamOrgManager.find(orgId);
        while (ObjectUtil.isNotEmpty(cur)) {
            orgPath.add("OU=" + cur.getName());
            if (ObjectUtil.isNotEmpty(cur.getParentId())) {
                cur = iamOrgManager.find(cur.getParentId());
            } else {
                cur = null;
            }
        }
        String orgPathStr = StrUtil.join(",", orgPath)+ ","+baseDn;
        iamOrgManager.dao().executeUpdate("update iam_org set bind_dn = ? where id_ = ?",orgPathStr,orgId);

    }
    /*
    * 更新对应组织下(包括子组织）的用户bindDn
    *
    * */
    public void updateOrgMemberBindDn(String id) {
        IamOrgEntity iamOrgEntity = iamOrgManager.find(id);
        iamOrgManager.dao().executeUpdate("update iam_user iu\n" +
                "    left join iam_org io on iu.org_id = io.id_\n" +
                "set iu.bind_dn = concat('CN=', iu.name_, ',', io.bind_dn)\n" +
                "where io.code_ like concat( ?,'%' )",iamOrgEntity.getCode());
    }

    public void sysOrgBySimpleSpell(String simpleSpell) {
        String url = jobUrl + "/sys/syncOrg/"+simpleSpell+"?Authorization="+jobAuth;
        //调用修改用户接口地址
        try {
            HttpUtil.post(url,"");
        }catch (Exception e){
            throw new RuntimeException("同步组织"+simpleSpell+"异常，异常信息"+e.getMessage(),e);
        }
    }

    public void  updateOrgADOrder(String sortOptions,String sortOrgId,String id,String parentOrgId){
        /*
        * sortOptions
        * 2-排在谁之后
        * 0-排在第一位
        * 1-排在最后
        * */
//        IamUserVo updateEntity = iamUserManager.find(IamUserVo.class,userId);
        long finalAdOrderSort = 0;
        // 移到最上或者最下
        if(ObjectUtil.isEmpty(sortOptions)){
            sortOptions = "1";
        }
        if("0".equals(sortOptions) || "1".equals(sortOptions) ){
            IamUserOrgOrderVo iamUserOrgOrderVo = new IamUserOrgOrderVo();
            String sql = "MAX";
            if("1".equals(sortOptions)){
                sql = "MIN";
            }
            // 查询oa,ad排序
            RowMap single = dao.createSQLQuery("select " + sql + "(ad_order) as ad_order from iam_org where parent_id = ? ",parentOrgId).limit(1).execute().single();
            if(ObjectUtil.isNotEmpty(single)){
                Long adOrder = single.getString("adOrder")==null?100000L:(single.getLong("adOrder"));
                if("0".equals(sortOptions)){// 最前 数字最大
                    finalAdOrderSort = adOrder + 20;
                }else {// 最后 数字最小
                    finalAdOrderSort = adOrder -20;
                }
            }

        }else if("2".equals(sortOptions)){
            // 移动到某个用户之后
            RowMap sortOrg = null;
            if(StrUtil.isNotEmpty(sortOrgId)) {
                List<RowMap> list2 = iamOrgManager.dao().createSQLQuery("select * from iam_org where id_ = ?", sortOrgId).list();
                if (ObjectUtil.isNotEmpty(list2)){
                    sortOrg = list2.get(0);
                    Long adOrder = sortOrg.getLong("adOrder")==null?100000L:sortOrg.getLong("adOrder");
                    // 如果该组织后面还有组织，则排序号为两个组织的排序号/2
                    List<RowMap> list1 = gfIamUserOrgManager.dao().createSQLQuery("select * from iam_org where parent_id = ? and ad_order < ?\n" +
                                "order by  ad_order desc limit 1", parentOrgId, adOrder).list();
                    if(ObjectUtil.isNotEmpty(list1)){
                        Long adOrder1 = list1.get(0).getLong("adOrder");
                        finalAdOrderSort = (adOrder + adOrder1)/2;
                    }else {
                        finalAdOrderSort = adOrder - 20;
                    }
                }else {
                    log.info("[组织排序]找不到id为{}的组织",sortOrgId);
                }
            }
        }
        gfIamUserOrgManager.dao().executeUpdate("update iam_org set ad_order = ? where id_ = ?",finalAdOrderSort,id);
    }
}
