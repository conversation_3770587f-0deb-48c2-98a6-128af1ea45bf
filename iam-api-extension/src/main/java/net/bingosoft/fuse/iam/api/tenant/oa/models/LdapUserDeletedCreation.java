package net.bingosoft.fuse.iam.api.tenant.oa.models;

import lombok.Data;
import net.bingosoft.fuse.commons.core.data.entities.AbstractEntity;

import java.sql.Timestamp;

@Data
public class LdapUserDeletedCreation extends AbstractEntity {
    String fduPersonUnid;
    String uid2;
    String cn;
    String displayName;
    String department;
    String department2;
    String deptType;
    String fduDeptName;
    String mail;
    String mobile;
    String telephoneNumber;
    String parentNo;
    String newNo;
    String fduPy;
    String ou;
    String dn;
    String serialNumber;
    String mainDepartment;
    String queryDpId;
    String queryDn;
    String tamUid;
    String oaMailServer;
    String oaMailFile;
    String oaComment;
    String level0;
    Timestamp deletedTime;
}
