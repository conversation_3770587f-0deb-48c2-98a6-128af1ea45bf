package net.bingosoft.fuse.iam.api.tenant.vo;

import java.util.List;

public class IamUsersMoveVo {
    String userOldOANames;
    String userNewOANames;
    String orgId;
    List<String> userNames;
    List<String> addUserIds;
    String itType;
    String itUrl;
    Integer acmFlag;
    Boolean moveMainDepartment;

    public String getItType() {
        return itType;
    }

    public void setItType(String itType) {
        this.itType = itType;
    }

    public String getItUrl() {
        return itUrl;
    }

    public void setItUrl(String itUrl) {
        this.itUrl = itUrl;
    }

    public String getUserOldOANames() {
        return userOldOANames;
    }

    public void setUserOldOANames(String userOldOANames) {
        this.userOldOANames = userOldOANames;
    }

    public String getUserNewOANames() {
        return userNewOANames;
    }

    public void setUserNewOANames(String userNewOANames) {
        this.userNewOANames = userNewOANames;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public List<String> getAddUserIds() {
        return addUserIds;
    }

    public void setAddUserIds(List<String> addUserIds) {
        this.addUserIds = addUserIds;
    }

    public Integer getAcmFlag() {
        return acmFlag;
    }

    public void setAcmFlag(Integer acmFlag) {
        this.acmFlag = acmFlag;
    }

    public List<String> getUserNames() {
        return userNames;
    }

    public void setUserNames(List<String> userNames) {
        this.userNames = userNames;
    }

    public Boolean getMoveMainDepartment() {
        return moveMainDepartment;
    }

    public void setMoveMainDepartment(Boolean moveMainDepartment) {
        this.moveMainDepartment = moveMainDepartment;
    }
}
