package net.bingosoft.fuse.iam.api.tenant.oa.entity;

import fly.core.data.annotation.Column;
import fly.core.data.annotation.Entity;
import fly.core.data.annotation.Id;
import fly.core.data.annotation.Settable;
import fly.core.data.domains.CreatedAt;
import fly.core.data.domains.UpdatedAt;
import lombok.Data;

import java.sql.Timestamp;

@Data
@Entity(table = "gf_user_ldap_deleted")
public class LdapUserDeletedEntity {
    @Id
    String fduPersonUnid;
    @Column("uid2")
    String uid2;
    @Column("cn")
    String cn;
    @Column
    String displayName;
    @Column("department")
    String department;
    @Column("department2")
    String department2;
    @Column
    String deptType;
    @Column
    String fduDeptName;
    @Column("mail")
    String mail;
    @Column("mobile")
    String mobile;
    @Column
    String telephoneNumber;
    @Column
    String parentNo;
    @Column
    String newNo;
    @Column
    String fduPy;
    @Column("ou")
    String ou;
    @Column("dn")
    String dn;
    @Column
    String serialNumber;
    @Column
    String mainDepartment;
    @Column
    String queryDpId;
    @Column
    String queryDn;
    @Column
    String tamUid;
    @Column
    String oaMailServer;
    @Column
    String oaMailFile;
    @Column
    String oaComment;
    @Column("level0")
    String level0;
    @Column
    Timestamp deletedTime;
    @CreatedAt
    @Settable
    Timestamp createTimed;
    @UpdatedAt
    @Settable
    Timestamp updateTimed;
}
