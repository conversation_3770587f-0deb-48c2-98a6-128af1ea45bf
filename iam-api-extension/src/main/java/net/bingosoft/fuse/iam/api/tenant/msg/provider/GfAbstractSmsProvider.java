package net.bingosoft.fuse.iam.api.tenant.msg.provider;

import com.github.rholder.retry.*;
import fly.core.util.JSON;
import fly.core.web.client.RestTemplateFactory;
import fly.lang.util.Strings;
import net.bingosoft.fuse.iam.api.tenant.msg.buider.GfTemplateBuilder;
import net.bingosoft.fuse.iam.common.message.providers.SymmetricPassword;
import net.bingosoft.fuse.iam.common.message.sms.SmsConfig;
import net.bingosoft.fuse.iam.common.message.sms.SmsMessage;
import net.bingosoft.fuse.iam.common.message.sms.SmsProvider;
import net.bingosoft.fuse.iam.common.message.templates.TemplateMappingRegistration;
import net.bingosoft.fuse.iam.common.utils.TemplateBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SMS短信基本类
 *
 * <AUTHOR>
 */
public abstract class GfAbstractSmsProvider implements SmsProvider {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    protected final RestTemplate restTemplate;
    protected static GfAbstractSmsProvider.SimpleAccessToken accessToken;


    protected GfAbstractSmsProvider(RestTemplateFactory restTemplateFactory) {
        restTemplate = restTemplateFactory.createRestTemplate();
    }

    protected static final Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfException()
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))  //重试次数
            .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))  //重试间隔
            .build();

    @Override
    public boolean send(SmsMessage message, SmsConfig smsConfig, Object params) {
        if (!support(smsConfig)) {
            return false;
        }
        try {
            retryer.call(() -> {
                sendInner(message, smsConfig, params);
                return true;
            });
        } catch (RetryException ex) {
            logger.error("[sms send failed] ,attempt number:{}", ex.getNumberOfFailedAttempts());
            throw new IllegalStateException(ex.getCause().getMessage(), ex);
        } catch (Exception ex) {
            throw new IllegalStateException("短信发送重试失败！", ex);
        }
        return true;
    }

    /**
     * 当前provider是否支持该类消息的发送
     */
    abstract boolean support(SmsConfig smsConfig);

    /**
     * 发送sms
     */
    abstract void sendInner(SmsMessage message, SmsConfig smsConfig, Object params);

    /**
     * 格式化参数
     */
    protected String formatContent(String template, Object params) {
        if (Strings.isEmpty(template)) {
            return null;
        }
        return GfTemplateBuilder.build(template, params);
    }


    private Map<String, Object> transferToMap(Object params) {
        if (params instanceof Map) {
            return (Map) params;
        }
        Map<String, Object> map = new HashMap<>();
        if (params instanceof List) {
            List listParams = (List) params;
            for (Object obj : listParams) {
                if (obj instanceof Map) {
                    map.putAll((Map) obj);
                }

            }
            return map;
        }
        return new HashMap<>();
    }

    protected Map<String, Object> transferToMapping(Object params) {
        if (params instanceof Map) {
            Map<String, Object> attrs = (Map) params;
            if (attrs.containsKey(TemplateMappingRegistration.ATTRS)) {
                return transferToMap(attrs.get(TemplateMappingRegistration.ATTRS));
            }
        }
        return new HashMap<>();
    }

    protected Map<String, Object> transferToCustomMapping(Object params) {
        if (params instanceof Map) {
            Map<String, Object> attrs = (Map) params;
            if (attrs.containsKey(TemplateMappingRegistration.EX_ATTRS)) {
                return transferToMap(attrs.get(TemplateMappingRegistration.EX_ATTRS));
            }
        }
        return new HashMap<>();
    }


    protected List<String> transferToMappingList(Object params) {
        if (params instanceof Map) {
            Map<String, Object> attrs = (Map) params;
            if (attrs.containsKey(TemplateMappingRegistration.ATTRS)) {
                return transferToList(attrs.get(TemplateMappingRegistration.ATTRS));
            }
        }
        return new ArrayList<>();
    }


    private List<String> transferToList(Object params) {
        List<String> list = new ArrayList<>();
        if (params instanceof List) {
            List listParams = (List) params;
            for (Object obj : listParams) {
                if (obj instanceof Map) {
                    list.addAll(((Map) obj).values());
                }
            }
            return list;
        }
        return new ArrayList<>();
    }

    protected static class SimpleAccessToken {

        private String accessToken;

        private String refreshToken;

        private Instant created;

        private int expiresIn;

        private String clientAuth;

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public Instant getCreated() {
            return created;
        }

        public void setCreated(Instant created) {
            this.created = created;
        }

        public int getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(int expiresIn) {
            this.expiresIn = expiresIn;
        }

        public boolean isExpired() {
            if (null == created) {
                return true;
            }
            return Instant.now().isAfter(created.plus(expiresIn, ChronoUnit.SECONDS));
        }

        public String getClientAuth() {
            return clientAuth;
        }

        public void setClientAuth(String clientAuth) {
            this.clientAuth = clientAuth;
        }

        public boolean isSameClientAuth(String clientAuth) {
            return Strings.isNotEmpty(clientAuth) && clientAuth.equals(this.clientAuth);
        }

    }

    protected String getAvailableAccessToken(SymmetricPassword symmetricPassword, SmsConfig smsConfig,
                                             boolean reBuild) {
        String clientAuth = getClientAuth(symmetricPassword, smsConfig);
        if (!reBuild
                && null != accessToken
                && !accessToken.isExpired()
                && accessToken.isSameClientAuth(clientAuth)) {
            return accessToken.getAccessToken();
        }

        refreshAccessToken(symmetricPassword, smsConfig, reBuild);

        return accessToken.getAccessToken();
    }

    protected synchronized void refreshAccessToken(SymmetricPassword symmetricPassword, SmsConfig smsConfig,
                                                   boolean reBuild) {
        String clientAuth = getClientAuth(symmetricPassword, smsConfig);
        if (null != accessToken && !accessToken.isExpired() && accessToken.isSameClientAuth(clientAuth)) {
            return;
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic " + clientAuth);
        headers.add("Content-Type", "application/x-www-form-urlencoded");

        MultiValueMap<String, String> map;
        if (null == accessToken || reBuild) {
            map = new LinkedMultiValueMap<>();
            // 查询参数
            map.add("grant_type", "client_credentials");
        } else {
            map = new LinkedMultiValueMap<>();
            // 查询参数
            map.add("grant_type", "refresh_token");
            map.add("refresh_token", accessToken.getRefreshToken());
        }

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        ResponseEntity<String> response;
        try {
            response = restTemplate.postForEntity(smsConfig.getAttr(SmsConfig.TOKENURL, String.class), request,
                    String.class);
        } catch (Exception e) {
            throw new IllegalStateException("fail to create access token because :" + e.getMessage());
        }

        if (!response.getStatusCode().is2xxSuccessful() || null == response.getBody()) {
            throw new IllegalStateException(
                    "fail to create access token because oauth2 server response: " + response.getBody()
                            + " with status: "
                            + response.getStatusCode().value());
        }

        logger.debug("create access token , response is : {}", response.getBody());

        HashMap<String, Object> result = JSON.decode(response.getBody(), HashMap.class);
        String at = (String) result.get("access_token");
        String rt = (String) result.get("refresh_token");
        Integer expiresIn = (Integer) result.get("expires_in");

        GfAbstractSmsProvider.SimpleAccessToken sat = new GfAbstractSmsProvider.SimpleAccessToken();
        sat.setAccessToken(at);
        sat.setRefreshToken(rt);
        sat.setCreated(Instant.now());
        // 提前标记过期，防止token失效
        sat.setExpiresIn(Math.abs(expiresIn - 1000));
        //token和客户端身份绑定，防止客户端身份失效后token仍有效
        sat.setClientAuth(clientAuth);

        accessToken = sat;
    }

    protected String getClientAuth(SymmetricPassword symmetricPassword, SmsConfig smsConfig) {
        String basic = smsConfig.getUsername() + ":" + symmetricPassword.decrypt(smsConfig.getSecret());
        return Base64.getEncoder().encodeToString(basic.getBytes(StandardCharsets.UTF_8));
    }

}

