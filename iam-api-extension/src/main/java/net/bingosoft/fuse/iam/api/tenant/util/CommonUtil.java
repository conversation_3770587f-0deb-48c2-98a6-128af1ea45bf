package net.bingosoft.fuse.iam.api.tenant.util;

import cn.hutool.core.util.StrUtil;
import net.bingosoft.fuse.iam.common.entities.users.IamOrgEntity;
import net.bingosoft.fuse.iam.common.managers.users.IamOrgManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CommonUtil {
    @Autowired
    IamOrgManager iamOrgManager;

    /*
     *   获取AD群组的信息
     * */
    public IamOrgEntity getADGroupInfo(){
        String valueOrTitle = "";
        IamOrgEntity adGroup = null;
        try {
            valueOrTitle = DictUtil.getValueOrTitle("ADGROUP", null, "GROUP_NAME");
            if(StrUtil.isEmpty(valueOrTitle)) throw new RuntimeException("无值为ADGROUP的字典");
            adGroup = iamOrgManager.dao().createQuery(IamOrgEntity.class).filter("name", valueOrTitle).first();
        }catch (Exception e){
            throw new RuntimeException("组织中无名称为"+valueOrTitle+"的群组");
        }
        return adGroup;
    }
    /*
     *   获取公共群组的信息
     * */
    public IamOrgEntity getpublicGroupInfo(){
        IamOrgEntity publicGroup = null;
        String publicGroupName = "";
        try {
            publicGroupName = DictUtil.getValueOrTitle("PUBLICGROUP", null, "GROUP_NAME");
            if(StrUtil.isEmpty(publicGroupName)) throw new RuntimeException("无值为PUBLICGROUP的字典");
            publicGroup = iamOrgManager.dao().createQuery(IamOrgEntity.class).filter("name", publicGroupName).first();
        }catch (Exception e){
            throw new RuntimeException("组织中无名称为"+publicGroupName+"的群组");
        }
        return publicGroup;
    }
}
