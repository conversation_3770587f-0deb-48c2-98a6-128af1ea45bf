package net.bingosoft.fuse.iam.api.tenant.msg.sender;


import fly.core.WrapperBeanFactory;
import fly.core.convert.Converts;
import fly.lang.util.Collections2;
import fly.orm.dao.Dao;
import net.bingosoft.fuse.iam.api.tenant.msg.buider.GfMessageEmailBuilder;
import net.bingosoft.fuse.iam.api.tenant.msg.enums.UserTypeEnum;
import net.bingosoft.fuse.iam.common.enums.MessageTplCode;
import net.bingosoft.fuse.iam.common.enums.NotifyType;
import net.bingosoft.fuse.iam.common.message.builders.MessageEmailBuilder;
import net.bingosoft.fuse.iam.common.message.mail.EmailConfig;
import net.bingosoft.fuse.iam.common.message.mail.MailSupport;
import net.bingosoft.fuse.iam.common.message.models.EmailMessageSendModel;
import net.bingosoft.fuse.iam.common.message.models.MessageSendModel;
import net.bingosoft.fuse.iam.common.message.notify.MessageNotifyContext;
import net.bingosoft.fuse.iam.common.message.notify.MessageNotifySender;
import net.bingosoft.fuse.iam.common.message.templates.EmailTemplateValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.stereotype.Component;

/**
 * 邮件通知
 */
@Component
public class GfEmailNotifySender implements MessageNotifySender {

    @Autowired
    protected MailSupport mailSupport;


    @Autowired
    protected WrapperBeanFactory wrapperBeanFactory;

    @Autowired
    protected Dao dao;

    @Override
    public boolean send(MessageSendModel model) {
        if (!NotifyType.EMAIL.getValue().equalsIgnoreCase(model.getType())) {
            return false;
        }

        if (Collections2.isEmpty(model.getTos())) {
            throw new IllegalArgumentException("接收的邮箱账号不能为空!");
        }

        Object templateValue = model.getTemplateValue();
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(model.getTos().stream().distinct().toArray(String[]::new));
        GfMessageEmailBuilder builder = GfMessageEmailBuilder
                .of(model.getTenantId(), UserTypeEnum.of(model.getCode()), wrapperBeanFactory)
                .context(model.getParams())
                .toId(model.getUserId())
                .toName(model.getUserName())
                .message(message)
                .config(null == model.getConfig() ? null : Converts.convert(model.getConfig(), EmailConfig.class))
                .templateValue(null == templateValue ? null : Converts.convert(templateValue, EmailTemplateValue.class));
        EmailMessageSendModel sendModel = builder.build();
        MessageNotifyContext messageNotifyContext = MessageNotifyContext.get();
        messageNotifyContext.setSendModel(sendModel.copy());
        return mailSupport.send(sendModel.getConfig(), sendModel.getMessage(), sendModel.getParams());
    }
}
