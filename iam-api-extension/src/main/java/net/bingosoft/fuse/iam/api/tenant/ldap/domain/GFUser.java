package net.bingosoft.fuse.iam.api.tenant.ldap.domain;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class GFUser implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -6891554948836924216L;
	/**
	 *1 mapping ldap:fdu-personuid
	 */
	private String guid;
	/**
	 * 2mapping ldap:uid
	 */
	private String uid;
	/**
	 * 3mapping ldap:cn
	 */
	private String name;
	/**
	 * 4mapping ldap:dispalyname
	 */
	private String displayName;
	/**
	 * 5mapping ldap:department
	 */
	private String deptID;
	/**
	 * 6mapping ldap:department2
	 */
	private String deptID2;
	/**
	 * 7mapping ldap:depttype
	 */
	private String deptType;

	/**
	 * 8mapping ldap:fdu-deptname
	 */
	private String deptName;

	/**
	 * 9mapping ldap:email
	 */
	private String email;

	/**
	 *10 mapping ldap:mobile
	 */
	private String moblie;
	/**
	 * 11 mapping ldap:telephonenumber
	 */
	private String telephone;

	/**
	 *12 mapping ldap:parentno
	 */
	private String parentNo;

	/**
	 * 13mapping ldap:newno
	 */
	private String newNo;

	/**
	 *14 mapping ldap:fdu-py
	 */
	private String pingYing;

	/**
	 *15 mapping ldap:ou
	 */
	private String ou;

	private String dn;

	private String tamUid;

	private String officeResNumber;
	//mail server attr
	private String mailServer;

	private String mailFilePath;

	//comment attr for store staff
	private String comment;

	private String level0;

	private String usercreatedate;

	private String deptdn;

	/**
	 * @return the ou
	 */
	public String isOu() {
		return ou;
	}

	@Override
	public String toString() {
		return "GFUser [guid=" + guid + ", uid=" + uid + ", name=" + name + ", displayName=" + displayName + ", deptID="
				+ deptID + ", deptID2=" + deptID2 + ", deptType=" + deptType + ", deptName=" + deptName + ", email="
				+ email + ", moblie=" + moblie + ", telephone=" + telephone + ", parentNo=" + parentNo + ", newNo="
				+ newNo + ", pingYing=" + pingYing + ", ou=" + ou + ", dn=" + dn + ", tamUid=" + tamUid
				+ ", officeResNumber=" + officeResNumber + ", mailServer=" + mailServer + ", mailFilePath="
				+ mailFilePath + ", comment=" + comment + ", level0=" + level0 + ", usercreatedate=" + usercreatedate
				+ ", deptdn=" + deptdn + "]";
	}

}
