{"name": "IAM控制台实体模型", "describe": "IAM控制台实体模型", "avatar": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABAEAYAAAD6+a2dAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAZiS0dEAAAAAAAA+UO7fwAAAAlwSFlzAAAASAAAAEgARslrPgAAD+1JREFUeNrtnHlclGXXx39nhkXNHBYxs6dUBLQUc8sWTR8lE+1xiVAEsxTQgWHYU3Iry9zSQXaGFCWf0lQycSv3qDRNzZ7cnUHNFRWGARVDZ+a8f+D4ft7h5XMPDMygz3z/hOtc97mvOfd1n+26ATt27NixY8eOHTt27Nix818E2VoBa9PqlcxAdZyXl9hXNNQw1M+Pe6GtaLujIw3Re+Lk3r1lXvKuXjtOnLC1ntbisTcAV5fMwCJ1//48hwYZDDExtBU/Iy0gAIcxALFisel4vo5cpO7bh685Ali0SDtJFu6VvmULQETEbOv7aWgeIwP4mJlFIpeVbZaro996C+3oGhQzZtA4tMbFV16p97RLsB27VCpewDn4JDOz5Yiq0c2mfvHF5aUJB55NvHvX1ndtKY+sAbgNS/NXyVu14nmOT9GdyZPhx3/wSzExAKQY8txzghO8jGT6SKPhIKSz+717lIDziG3bVkiMk9ERqcXFeIEm0LX0dEOs44QqUiorDoQv7Zqv0dh6XerKI2MArWfk5Jxq/vTT+nm6KQ6VUin6UjaiY2KgBhDr6io4QWccJsWFC1zC83ljSopzx8odleOXL7++s72zw1/377sE3Wzv7DduHFKoC3efNo1ewGq06dpVcN6ZaA9xVRVOQIYD69aJ/hTPMFTMn196RCrtHHH6tK3XTYgmawASzgw8u7ZHD1EoHSJ9QgKcIaVPx43DWrjwZkdHwQkC8SvaHT3KFRxFq1JStGtvvHwpYvVq4BMaRDpd7YLMzERuf2ZnqUL8/DgImaIrsbEohpxz//Uvwet6whfbDAZUQkrfbtuGXrSJWixcWPZ1pFenH/bts/W6mtIEDMDCBXdFJlTM/BaiqGD3bvRiR3ZMS9NOiprsnbF5c0NpKcnP/lQl79lTtJVz8Vp8PO7gGK0ODsZOfMVLHRwEJ5gDYO+RI7yfe5E+Lc18g2xcbGAAaf4qubOzS5B4DnUNCsIsSuTgpCQagPG4+cILguKR+Iy63LuHa3Dkf69di5mGbZS3aJG1wzcJZ/Mp7tCByjnbsX9EBHXEUf44IgJAT3SUSAQn6EfTKfz8eT5tOMNnUlP/95U09b0XFXfuWOs+Gt0AWvZI81fJPTwcHR1GAqGhvBSBdCQmhkYgn1e1ayckz6UYCKeKClqL/vR7Xh6t0r3Muxcv1nwf84N3xuXL1looIYxOqUElzkP+pEnYTEuoaupUeg2e/Nszz5gxxVGcLy9HFvLpzpdfWus+G9wAHiZaYmk3BkZHswsm8+7wcApBB8S2aCE4wQNnDXr0YQelEiqxSBSnVJZppRGdvMrLG2shGp51a08EOjnV27k03encyUcU9/nnZUsid3a6cfx4Q2lpsQHUNdFSg4EYTm/+8Qc7czGmLV3aVN6NDY+Fvo5xlgZOVNXBAKoTLa6uTxWqHceNg4yC8UtiIrJ4Ntx79RIUNzprezAG2374gf9N+0Trliwpnx25s9ONPXus9Cs0OdwDMneee/Gll/RB6Kef+sEHtJxCceCdd8x+gIzRzkxajCSFQtvB/Sev9G++AcaOJdLrhcQFDaA9r+Tz3KxZxfK7wbrorVsxDQMQO3iwoGLGLawfvY6U1auxFxqaq1A09Bb2uCGZmx5+5qWOHUWO4imiu/HxPIiGU+fQUPLnT3nhE08IyfMxdEbh/v3iI86H9fD3Lx0VltQl/Nat2sYLGoBLSVZftfP06eSNiXx8/vxaB7pRHLZrtXyEU2hNTo64UN9ONyktrXRU9Ogu4Vev2nphH1VavbI8/kSgm5sopirCeVhkJKroHB+Vy4Uyl9yFzsBj6VLtr5Gp3i4JCbWNEzaA6KwstXTnTvoK4A/eeOPhBVTIo263blELDECLOXNEO509dIuXLROyODuWUh1Gu950LKRdEybwcS7ArYQECoCaf3z+eeMo/glfw+PkSa2vbJ+3S+1Op0jochSIYLSuqKjxd29M5ONPPokC5PL44GBDZdVCB/fhw4F165jNeHfZqReuLs6jRXHNmuESxuJViYQmowO/XTO6ouM4iVmVlULzCe4ArguzflOXDhuGLzCJD27dijJEwZtqlzMmOAzoa5ibkuJ8//aEuwW5udZOcDwuPPQJwsQ6sS4mhp1wEifDwh4+gLXAG+BF/5TLtYNkb3pdycysbZzZUYDrzezm6i7h4ezOh3m7QkHuKMS9Vq0EBR9U3dCNOvLh7GyHvwwSh7MZGTfXR+V3pOJiWy9wU8N1fLa6yL9fP3bgaWyIj6ercGKf0aMFo4IheJfidTqocI+nLF5cdjTS3WvEzJlC4WGd8wDuBbmLTi9/8kl94t/vi2eFhlIpDcVHCQkwtwxrkuCgYtEg7F+4UPN9RJ53xsmTtv4BrIdJ/8IQ8kSPDz8kX5zBwNdeE5J+6IPthArfrFzJRYZ00dDkZO0s+bueLf76y1wtGiATaGEjhpWKObamxoMDqkBgfDxKsQux7dsLyRv7EGgi+yM9J8fgQtlOF1NTy8tlsvbty8rqq1ej1QIszhDKaC5Kf/+d1Ybv6Ghq6qOWITS+uyla3EvsJJVSb4rjYKkUGk7BUBcXwQlqZEgd/tD2WbMGkEb0een+/YbS02rVQItrBE2kelYbEklWllrdu7coAIv4t9hYs8vFNt4BbdYPYGmVECbVM1EL/Upd6aJFjZ94suzdbdpBZGsfqAk0hBixsE/AdGFDeAdfXLBAMySqs8+Hp07VVytL392IQyia37zJsVhBQStWNLUMaRMyAFOqnzS3P5/KUoUMHmxpaxatRZThmdRUTXdZlM+aXbtqE7P43Z2EPrimVvNyfg5jMzKaT3UoeGLEsmXXpFJpu2XCiRlrU/c8QDLv5zZTppASE+nG4cP6pWIZv6FQVEyUSr0ziooaW+FWvyiXFy3s00fU1pBryE9MpBnYTm0DA81tzTIWS/g6VYpWLllCOkzSG0pK6G9WiQbHxeEzRHL5qFFmO6sJeBs/793LgZTP7yQna58vlnu32rYN+ISIDIbGWgePMZmB57ltW90ukadu58SJSOJAJLRrJ1ovPmqQZ2WZ25QqnAl0SS8uUvv6gsQbDPznnzUGGJ+0ZHSjGxs34io80UehKAuWlXm9vX9/Yy2AEYu7hc3FdEfpgI3cfd48zfcyH++MAwca+z5d1Rkn1G927coJIl+cSUigAdjMe8ePxzz8Bb2zs3Ec34QKe44f1zrIlnpLfX2F5hU0gCdWZQ8patOmjWNnHOXXz50zuyy5m8Oh/fVX0UyE4MvkZM33HmO90r/7ztw6dX2xOFFl1N8k0YJI8QLyVSiqO5MuXmws/Y1I/DPFRcv8/EhJsw3/SUykwViP4f7+Qql4/hL5uLt9u3akbI93d39/oeuY/Qpwq8zefNatb1/uzUmibrNnowVy+J3hw3EOxzBcJFhUwiiA+p07xy7Yjg0pKY5X2LEqZeXKm+uj8rvm377deEuZozx8yNHR5YJhtOTLsWOpgDege3w85gAY1Lv3w2EPwkw6ZpjGc9LT8YL+Z3ybm1vdk1ezGNbg+m3Sr5M4BwXRCqygUYmJKMQ23tGjh6D4gzASAdQOJZs2iZ1EgTppZGTJfKn0+bvXrgmJ19sJbJWXk6OSd+okdtZ/SH4xMXWN602bPav/umRJ9avj0qXGW/AH6+aSoyxSP/ccsvXT+Rizta5rukPhDO3FmsREao0y/OPZZwUnaOCu6AaLAiyO6x/cGLfBTD5VUCD+iX3EYxYvLt0QNcTzP4cONfQPYS1qtI/XMaqo0RU9ilYZwj7/XNMicoSP5soVS/VrxDDQwrjeuACP2Gldiw+QWLkr2op5gAbqik0kGVLPnkWeoRj5WVm2O61rYZ7CSL2PsDUMNk8EWXoGkGdhJnZcv04JrIFMqdTN4L66jPT0Wwvkh56PLi1tOE0t3NGaaNXT5gZgiqVxPX+GAhT9/Tcdxmlo1q+v72ndGj7NbOTTndhYeh+BPOPppwUnaCJH2IRocgZgisVxvZmndS2tVj4qR9hMMdsAXC5kF6vk48djB5eQ8r33sJKWccmJEw46/X7x+0plySH5Ic+Cs2cbX+XquNnVVd9F8uqYMXU+oPIAY0qYhiCaVmk0dc5rmHxvwLEn5Pf65+Y2fl7DSLUPIuG2UGPAAIBViO7RQzxcNB/YscPc6qIZBlDtvLmoslcXda+ooL4o5w0tWz78t/EJ64I1GLJlC1+BBlnJydofZd94ZxQWNv5CVGNxA4oQNj7C5jEmM/BEYMuW92U0xfnX99+nc8jkWbGx+ABD8Ya3t3Ecp1EijTh4UDsh8q7XaeGOLPOLQXOzrqlW7NmDZHyH1wcNEhSYA2DvkSMoYB1DoSjb4+BU0TM/v6E7WmrD4i29RvhpXWfNmKjiVfp1fFkup3j6hY9PniyYP+hJcnpx8+ayPZEGr8qRI4WuY7YBGN/FhjlVYeI206ejgtrhYmSk2QmNErji8qVLtA4y0qalVefWly2z1qnfhzWNj1BsyIuMxC+cgqtvvknbkYb7Hh4sokqc+PFHel2fQmdTU63trLlWKjerXV99lTfr/Tg9Lo6+JR19ExAgmD8w7sAhuA7Vpk0iL72DvkdUlLn9BvV2Ao1bkq4/DXZeGBLCTtAgIyGBZsCNozp3FpLn3yChgNu3cQHlLFq92mGGYaW4g0JhPV/CVph0FI0nJZCURE8hDLH9+gmKN3DjSwNGARa2Sj1i39YxF9MPR9Q5ipmLMly8cQNRPAODs7PvH3HIMeRkZNzuI43oHFFSYql+jR4G1miWvIxXsT8kxGznbA6a5Ld1aqNGkcyTxPgqLMzsMrqVM51WzwNIJFlZZ4M9PUVKzBSNjY2t6wIZy7a4zgvYNyfH1l8QsTT6sHWtw+aJoGpvVyLh1rrRhv4TJ9b12zrWKyv/30++0O90gdcmJqIEGizr3l1Q3DQz+J3+oKjv4sVl2ui2nbyOHbPV+tvcAGpi8m2dr2kti6ZOJQ94Y3C3boLiQdDSiPv3uQskvGnjRlErw7tcrFBoQuUSnwEHD5qrhTFqcErntw1jJ02ytG29qWYGm6AB/P8Yt1o8TSc5NCkJVSD+8K23BE8rP8C41YqeQihiUlM1Za09vLw3bDC2qLkEKT89c+7FFxFuaC16WybDTYxE2IQJFIVNGNa8uaCCYZgH16Ii3shH0C09vakdXKmNR8YATHlYd/+C/ZCYkABPFNKwoCCzq4gb4EX/PHWKsqDlyzduYBdKoB440GwF3kBreBUWsjtG4kpyslZ53dnrmS1bGrsbuKF5ZA3AlIdt0uuwXoeICAwjbwqJjsZBJPCnbm71ntjG3cCNzWNjAKbUSFTdIhmnJCaSgrMQ6+NTm5ytu4GtzWNrADX5mPeyg4Oruu0fzw4NCOCr/AT7BATQJopC32bNWMctyHP3bvEg59d0p/Py7N86smPHjh07duzYsWPHzuPJ/wApJVs9KysF1AAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMS0wNy0yNFQxODo1Njo1NyswODowMKIxoyIAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjEtMDctMjRUMTg6NTY6NTcrMDg6MDDTbBueAAAATnRFWHRzdmc6YmFzZS11cmkAZmlsZTovLy9ob21lL2FkbWluL2ljb24tZm9udC90bXAvaWNvbl92OHRlMHJhZDF2ZC9nb25nY2hlbmdfMS5zdmdKgq7UAAAAAElFTkSuQmCC", "version": "4.1.3", "createdTime": "2022-4-18 19:28:26", "updatedTime": "2023-4-20 10:20:29", "profile": {"default": {"db": "52CA5BFD-BF53-47B2-8C63-4127688BC626", "dbConn": "2D288ADA-5083-45B3-ACA9-8B57E6AA3726", "entityInitFields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "77ADB000-0978-49B3-BF5B-09EEBD7E27FA"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "A00C10D7-B4C4-4F82-A3CC-39E8713E1A95", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "73028158-212D-4C24-870F-2A3DF6355A99", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "DD604BBC-0554-4B38-976B-C25CABB8EF4E", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "492AABD8-F93B-426B-8183-84435EAD496B", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "D974349E-78C8-4066-9A9C-558044B4F91D", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "entityInitProperties": {}}, "sql": {"delimiter": ";"}, "dataTypeSupports": [{"defKey": "MYSQL", "id": "52CA5BFD-BF53-47B2-8C63-4127688BC626"}, {"defKey": "ORACLE", "id": "72C35736-1226-4B9F-8497-B0DFF626B554"}, {"defKey": "SQLServer", "id": "DB846F60-1156-40D5-9AB8-E69CFF5D5CED"}, {"defKey": "PostgreSQL", "id": "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A"}, {"defKey": "DB2", "id": "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402"}, {"defKey": "DM", "id": "3A4FDB50-CF5B-422F-9E8B-4D5481172952"}, {"defKey": "GaussDB", "id": "180F87E7-665F-4D49-9F83-96BD0B3043A9"}, {"defKey": "Kingbase", "id": "AB97A1B2-40A8-420F-AD6E-16AC1D395D44"}, {"defKey": "MaxCompute", "id": "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF"}, {"defKey": "JAVA", "id": "5B3D490C-72F5-4988-9865-85FBE92F282A"}, {"defKey": "C#", "id": "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB"}, {"defKey": "SQLite", "id": "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A"}, {"defKey": "JavaMybatis", "id": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B"}, {"defKey": "JavaMybatisPlus", "id": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073"}, {"defKey": "Hive", "id": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2"}], "codeTemplates": [{"type": "dbDDL", "applyFor": "52CA5BFD-BF53-47B2-8C63-4127688BC626", "isDefault": true, "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT = '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}\n", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('ALTER TABLE '+before.defKey+' RENAME TO '+after.defKey);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldAdded) { \n            let ddlItem = 'ADD COLUMN '+field.defKey+' '+field.dbType;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            if(field.autoIncrement){\n                ddlItem += ' AUTO_INCREMENT';\n            }\n            if(field.defaultValue){\n                ddlItem += (' DEFAULT' + field.defaultValue);\n            }\n            ddlItem += (' COMMENT \\''+field.defName+';'+field.comment+'\\'');\n            \n            if(field.index>0 && field.afterFieldKey){\n                ddlItem += (' AFTER '+field.afterFieldKey);\n            }\n            ret.push(ddlItem);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldRemoved) { \n            ret.push('DROP '+field.defKey);\n        }\n        return firstDDL+'\\n'+ret.join(',\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = 'ALTER TABLE '+entity.data.baseInfo.defKey;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' MODIFY COLUMN '+after.defKey);\n            }else{\n                changeDDL += (' CHANGE COLUMN '+before.defKey+' '+after.defKey);\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(field.scale)>0){\n                    changeDDL += (','+field.scale);\n                }\n                changeDDL += ')';\n            }\n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            let defaultValue = '';\n            if(after.defaultValue != null && after.defaultValue.length>0){\n                defaultValue = (after.defaultValue);\n            }else{\n                defaultValue = 'NULL';\n            }\n            changeDDL += (' DEFAULT ' + defaultValue);\n            let comment = after.comment||'';\n            changeDDL += (' COMMENT \\''+comment+'\\';');\n            ret.push(firstDDL+' '+changeDDL);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "72C35736-1226-4B9F-8497-B0DFF626B554", "isDefault": true, "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* -------------------------------------------------- */\n创建表：\n{{~ createEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* -------------------------------------------------- */\n删除表：\n{{~ dropEntities:entity}}\n    {{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* -------------------------------------------------- */\n修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n    {{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n    {{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n    {{?}}{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}\n    {{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('\\n\\t建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('\\n\\t解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}\n{{=indexChanged?'\\n\\t更改了索引':''}}\n{{=changed?'\\n\\t更改了属性':''}}\n{{=relaArray.length>0?relaArray.join(''):''}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD (${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ddlItem += ')';\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            changeDDL += ('MODIFY ('+after.defKey+'');\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(field.scale)>0){\n                    changeDDL += (','+field.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            changeDDL += ')';\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "DB846F60-1156-40D5-9AB8-E69CFF5D5CED", "isDefault": true, "createTable": "IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[{{=it.entity.defKey}}]') AND type in (N'U'))\nDROP TABLE [dbo].[{{=it.entity.defKey}}];\n\nCREATE TABLE [dbo].[{{=it.entity.defKey}}](\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' IDENTITY(1,1)' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n){{=it.separator}}\n$blankline\n{{? it.entity.defKey || it.entity.defName}}EXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, null, null;{{?}}\n{{~it.entity.fields:field:index}}\nEXEC sp_addextendedproperty 'MS_Description', '{{=it.func.join(field.defName,field.comment,';')}}', 'SCHEMA', dbo, 'table', {{=it.entity.defKey}}, 'column', {{=field.defKey}};\n{{~}}\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`EXEC sp_rename '${before.defKey}','${after.defKey}'`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `IF ((SELECT COUNT(*) FROM ::fn_listextendedproperty('MS_Description','SCHEMA', 'dbo','TABLE', '${after.defKey}', NULL, NULL)) > 0)\n            \\n\\tEXEC sp_updateextendedproperty 'MS_Description', '${commentText}','SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            \\nELSE\n            \\n\\tEXEC sp_addextendedproperty 'MS_Description', '${commentText}', 'SCHEMA', 'dbo','TABLE', '${after.defKey}'\n            `;\n            ret.push(myText);\n            /*ret.push('ALTER TABLE '+after.defKey+' COMMENT \\''+commentText+'\\'');*/\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD [${field.defKey}] ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `EXEC sp_addextendedproperty 'MS_Description', N'${commentText}','SCHEMA', N'dbo','TABLE', N'${entity.data.baseInfo.defKey}','COLUMN', N'${field.defKey}'`;\n                ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN [${field.defKey}]`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE [dbo].[${entity.data.baseInfo.defKey}]`;\n        for (let field of fieldModified) { \n            let changeDDL = '';\n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey === after.defKey){\n                changeDDL += (' ALTER COLUMN ['+after.defKey+']');\n            }else{\n                let renameText = `EXEC sp_rename '[dbo].[${entity.data.baseInfo.defKey}].[${before.defKey}]','${after.defKey}','COLUMN';`;\n                ret.push(renameText);\n                continue;\n            }\n            changeDDL += (' '+after.dbType);\n            if(after.len>0){\n                changeDDL += ('('+after.len);\n                if(parseInt(field.scale)>0){\n                    changeDDL += (','+field.scale);\n                }\n                changeDDL += ')';\n            }\n            let defaultValue = after.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                changeDDL += (' DEFAULT ' + defaultValue);\n            }\n            \n            if(after.notNull){\n                changeDDL += ' NOT NULL';\n            }\n            ret.push(`${firstDDL} ${changeDDL};`);\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "dbDDL", "applyFor": "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A", "isDefault": true, "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' AUTO_INCREMENT' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChanged(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push('代码:'+(before.defKey||'NULL')+'->'+(after.defKey||'NULL'));\n        }\n        if(before.defName !== after.defName){\n            ret.push('显示名称:'+(before.defName||'NULL')+'->'+(after.defName||'NULL'));\n        }\n        if(before.comment !== after.comment){\n            ret.push('说明:'+(before.comment||'NULL')+'->'+(after.comment||'NULL'));\n        }\n        if(ret.length>0){\n            return '    基本信息:\\n\\t'+ret.join('\\n\\t');\n        }\n        return '';\n    };\n    \n    function buildAddedDesc(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        \n        for (let field of fieldAdded) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildRemovedDesc(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        \n        for (let field of fieldRemoved) { \n            let row = [];\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n    \n    function buildModifiedDesc(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        for (let field1 of fieldModified) { \n            let row = [];\n            let field = field1.before;\n            row.push(field.defKey+'['+field.defName+']');\n            ret.push(row.join(\"\"))\n        }\n        return ret;\n    };\n}}\n\n\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=entity.data.defKey}}[{{=entity.data.defName}}]\n{{~}}\n{{?}}\n\n{{? modifyEntities && modifyEntities.length > 0}}\n/* --------------- 修改表 --------------- */\n{{~ modifyEntities:entity}}\n{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]{{let changeText=baseChanged(entity.data.baseChanged);}}\n{{=baseChanged(entity.data.baseChanged)}}\n    {{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n    修改字段：\n    {{='\\t'}}{{=buildModifiedDesc(entity).join('\\n\\t')}}{{?}}{{\n        /*计算是否调整了属性*/\n        let propAdded = entity.data.propAdded || [];\n        let propRemoved = entity.data.propRemoved || [];\n        let propModified = entity.data.propModified || [];\n        let changed = propAdded.length>0 || propRemoved.length>0 || propModified.length>0;\n        /*计算关联是否调整*/\n        let refEntityAdd = entity.data.refEntityAdd || [];\n        let refEntityRemoved = entity.data.refEntityRemoved || [];\n        let relaArray = [];\n        for (let rela of refEntityAdd) {\n            relaArray.push('建立关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        for (let rela of refEntityRemoved) {\n            relaArray.push('解除关联:'+rela.defKey+'['+rela.defName+']');\n        }\n        /*索引是否修改过*/\n        let indexChanged = entity.data.indexChanged;\n    }}{{=indexChanged?'\\n\\t更改了索引':''}}{{=changed?'\\n\\t更改了属性':''}}{{=relaArray.length>0?('\\n\\t'+relaArray.join('\\n\\t')):''}}\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n    添加字段：\n{{='\\t'}}{{=buildAddedDesc(entity).join('\\n\\t')}}\n{{?}}{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n    删除字段：\n{{='\\t'}}{{=buildRemovedDesc(entity).join('\\n\\t')}}\n{{?}}\n{{~}}\n{{?}}", "update": "{{\n    let createEntities = it.changes.filter(function(row){return (row.opt==='add'&&row['type']==='entity');});\n    let dropEntities   = it.changes.filter(function(row){return (row.opt==='delete'&&row['type']==='entity');});\n    let modifyEntities = it.changes.filter(function(row){return (row.opt==='update'&&row['type']==='entity');});\n    \n    function baseChangedDDL(beforeAfter){\n        if(beforeAfter == null){\n            return '';\n        }\n        let ret = [];\n        let before = beforeAfter.before || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        let after = beforeAfter.after || {\"defKey\":\"\",\"defName\":\"\",\"comment\":\"\"};\n        if(before.defKey !== after.defKey){\n            ret.push(`ALTER TABLE ${before.defKey} RENAME TO ${after.defKey}`);\n        }\n        let commentText = '';\n        let commentChanged = false;\n        if(before.defName !== after.defName){\n            commentText = after.defName;\n            commentChanged = true;\n        }\n        if(before.comment !== after.comment){\n            commentChanged = true;\n            if(commentText){\n                commentText = (commentText+ ';'+after.comment)\n            }else{\n                commentText = after.comment\n            }\n        }\n        if(commentChanged){\n            let myText = `COMMENT ON TABLE ${after.defKey} IS '${commentText}'`;\n            ret.push(myText);\n        }\n        let baseText = '-- 基本信息:\\n';\n        return baseText+ret.join(';\\n')+';';\n    };\n    \n    function buildAddedDDL(entity){\n        let ret = [];\n        let fieldAdded = entity.data.fieldAdded||[];\n        if(fieldAdded.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldAdded) { \n            let ddlItem = `ADD COLUMN ${field.defKey} ${field.dbType}`;\n            /*处理数据类型长度*/\n            if(field.len>0){\n                ddlItem += ('('+field.len);\n                if(parseInt(field.scale)>0){\n                    ddlItem += (','+field.scale);\n                }\n                ddlItem += ')';\n            }\n            if(field.notNull){\n                ddlItem += ' NOT NULL';\n            }            \n            let defaultValue = field.defaultValue;\n            defaultValue = (defaultValue==null)?\"\":(\"\"+defaultValue);\n            if(defaultValue.length>0){\n                ddlItem += (' DEFAULT ' + defaultValue);\n            }\n\n            ret.push(`${firstDDL} ${ddlItem}`);\n            \n            /*处理字段注释*/\n            let fieldComments = [];\n            if(field.defName != null &&field.defName.length>0){\n                fieldComments.push(field.defName);\n            }\n            if(field.comment != null &&field.comment.length>0){\n                fieldComments.push(field.comment);\n            }\n            let commentText = fieldComments.join(';');\n            if(commentText != null && commentText.length > 0){\n                let commentDDL = `COMMENT ON COLUMN ${entity.data.baseInfo.defKey}.${field.defKey} IS '${commentText}'`;\n                 ret.push(commentDDL);\n            }\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildRemovedDDL(entity){\n        let ret = [];\n        let fieldRemoved = entity.data.fieldRemoved||[];\n        if(fieldRemoved.length == 0){\n            return '';\n        }\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldRemoved) { \n            ret.push(`${firstDDL} DROP COLUMN ${field.defKey}`);\n        }\n        return '\\n'+ret.join(';\\n');\n    };\n    \n    function buildModifiedDDL(entity){\n        let ret = [];\n        let fieldModified = entity.data.fieldModified||[];\n        \n        let firstDDL = `ALTER TABLE ${entity.data.baseInfo.defKey}`;\n        for (let field of fieldModified) { \n            let before = field.before || {};\n            let after = field.after || {};\n            if(before.defKey !== after.defKey){\n                let renameText = `ALTER TABLE ${entity.data.baseInfo.defKey} RENAME COLUMN ${before.defKey} TO ${after.defKey};`;\n                ret.push(renameText);\n            }\n            /*如果没有变化，则不生成变更语句*/\n            if(before.dbType === after.dbType \n            && before['len'] === after['len'] \n            && before.scale === after.scale\n            && before.primaryKey === after.primaryKey\n            && before.notNull === after.notNull\n            && before.autoIncrement === after.autoIncrement\n            && before.defaultValue === after.defaultValue){\n                continue;\n            }\n            if(before.dbType !== after.dbType || before.len !== after.len || before.scale !== after.scale){\n                let dbTypeDDL = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${after.defKey} TYPE ${before.dbType}`;\n                if(after.len>0){\n                    dbTypeDDL += ('('+after.len);\n                    if(parseInt(after.scale)>0){\n                        dbTypeDDL += (','+after.scale);\n                    }\n                    dbTypeDDL += ')';\n                }\n                ret.push(dbTypeDDL+';');\n            }\n            \n            if(before.defaultValue !== after.defaultValue){\n                let defaultDDL = '';\n                let defaultValue = after.defaultValue;\n                defaultValue = (defaultValue==null)?\"NULL\":(\"\"+defaultValue);\n                if(defaultValue.length>0){\n                    defaultDDL += ('SET DEFAULT ' + defaultValue);\n                }\n                let defaultTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${defaultDDL};`;\n                ret.push(defaultTpl);\n            }\n            \n            if(before.notNull !== after.notNull){\n                let notNullDDL= 'SET NULL';\n                if(after.notNull){\n                    let notNullDDL= 'SET NOT NULL';\n                }\n                let notNullTpl = `ALTER TABLE ${entity.data.baseInfo.defKey} ALTER COLUMN ${notNullDDL};`;\n                ret.push(notNullTpl);\n            }\n        }\n        return ret;\n    };\n}}\n{{? createEntities && createEntities.length > 0}}\n/* --------------- 创建表 --------------- */\n{{~ createEntities:entity}}\n{{=it.func.createDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? dropEntities && dropEntities.length > 0}}\n/* --------------- 删除表 --------------- */\n{{~ dropEntities:entity}}\n{{=it.func.dropDDL(entity.data,entity['type'])}}\n{{~}}\n{{?}}\n\n\n{{? modifyEntities && modifyEntities.length > 0}}\n{{~ modifyEntities:entity}}\n/* --------------- 修改表 --------------- */\n-- 修改表：{{=entity.data.baseInfo.defKey}}[{{=entity.data.baseInfo.defName}}]\n{{=baseChangedDDL(entity.data.baseChanged)}}\n{{? entity.data.fieldModified && entity.data.fieldModified.length > 0}}\n-- 修改字段：\n{{=buildModifiedDDL(entity).join('\\n')}}\n{{?}}{{\n/*索引是否修改过*/\nlet indexChanged = entity.data.indexChanged;\n}}\n{{? indexChanged }}\n-- 索引重建\n{{=it.func.indexRebuildDDL(entity.data.baseInfo,entity.data.newIndexes,entity.data.fullFields,entity['type'])}}\n{{?}}\n\n{{? entity.data.fieldAdded && entity.data.fieldAdded.length > 0}}\n-- 添加字段：\n{{=buildAddedDDL(entity)}};\n{{?}}\n\n{{? entity.data.fieldRemoved && entity.data.fieldRemoved.length > 0}}\n-- 删除字段：\n{{=buildRemovedDDL(entity)}};\n{{?}}\n{{~}}\n{{?}}"}, {"type": "appCode", "applyFor": "5B3D490C-72F5-4988-9865-85FBE92F282A", "isDefault": true, "content": "package cn.com.chiner.entity;\n$blankline\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n /**\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\n@Table(name=\"{{=it.entity.defKey}}\")\npublic class {{=it.func.camel(it.entity.defKey,true) }} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    {{? field.primaryKey }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}", " JpaBean": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@Table(name=\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    {{? field.primaryKey }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"type": "dbDDL", "applyFor": "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402", "isDefault": true, "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"type": "dbDDL", "applyFor": "3A4FDB50-CF5B-422F-9E8B-4D5481172952", "isDefault": true, "createTable": "CREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? '' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ','('+field.defaultValue+')',' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"type": "appCode", "applyFor": "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB", "isDefault": true, "content": "using System;\nusing System.Collections.Generic;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n/*\n * <AUTHOR> http://www.chiner.com.cn\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace Chiner.Application\n{\n    public partial class {{=it.func.camel(it.entity.defKey,true) }}\n    {\n    \n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}} { get; set; }\n        $blankline\n        {{~}}\n        \n    }\n}", "Default": "using System;\nusing System.Collections.Generic;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n}}\n/*\n * <AUTHOR> http://www.chiner.com.cn\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace PDManer.Application\n{\n    public partial class {{=it.func.camel(it.entity.defKey,true) }}\n    {\n    \n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}} { get; set; }\n        $blankline\n        {{~}}\n        \n    }\n}", "SqlSugar": "using System;\nusing System.Collections.Generic;\nusing SqlSugar;\n\n$blankline\n{{\n    var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    var sqlSugartable='[SugarTable(\"{{=it.entity.defKey}}\", TableDescription = \"{{=it.func.join(it.entity.defName,it.entity.comment,';')}}\")]';\n}}\n/*\n * <AUTHOR> <EMAIL>\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n * @desc : {{=it.func.join(it.entity.defName,it.entity.comment,'-')}}\n */\nnamespace Model.DBModel\n{\n    /// <summary>\n    /// {{=it.func.join(it.entity.defName,it.entity.comment,';')}}\n    /// </summary>\n    {{=sqlSugartable}}\n    public class {{=it.entity.defKey}}\n    {\n        {{~it.entity.fields:field:index}}\n        /// <summary>\n        /// {{=it.func.join(field.defName,field.comment,';')}}\n        /// </summary>\n        {{? field.primaryKey }}\n        [SugarColumn(IsIdentity = true, IsPrimaryKey = true)]\n        {{?}}\n        public {{=field.type}} {{=it.func.camel(field.defKey,true)}}{ get; set; }\n        $blankline\n        {{~}}\n    }\n}"}, {"type": "dbDDL", "applyFor": "180F87E7-665F-4D49-9F83-96BD0B3043A9", "isDefault": true, "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= field.autoIncrement ? ' AUTO_INCREMENT' : '' }}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"type": "dbDDL", "applyFor": "AB97A1B2-40A8-420F-AD6E-16AC1D395D44", "isDefault": true, "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{= field.defaultValue ? it.func.join(' DEFAULT ',field.defaultValue,' ') : '' }}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);\n$blankline\n{{? it.entity.defKey || it.entity.defName}}COMMENT ON TABLE {{=it.entity.defKey}} IS '{{=it.func.join(it.entity.defName,it.entity.comment,';')}}';{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.defName || field.comment}}COMMENT ON COLUMN {{=it.entity.defKey}}.{{=field.defKey}} IS '{{=it.func.join(field.defName,field.comment,';')}}';{{?}}\n{{~}}", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"type": "dbDDL", "applyFor": "dictSQLTemplate", "isDefault": true, "content": "/* 插入字典总表[{{=it.dict.defKey}}-{{=it.dict.defName}}] */\nINSERT INTO SYS_DICT(KEY_,LABEL,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=it.dict.defName}}','{{=it.dict.intro}}',1);\n/* 插入字典明细表 */\n{{~it.dict.items:item:index}}\nINSERT INTO SYS_DICT_ITEM(DICT_KEY,KEY_,LABEL,SORT_,INTRO,REVISION) VALUES('{{=it.dict.defKey}}','{{=item.defKey}}','{{=item.defName}}','{{=item.sort}}','{{=item.intro}}',1);\n{{~}}"}, {"type": "dbDDL", "applyFor": "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF", "isDefault": true, "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.defName,field.comment,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  COMMENT = '{{=it.func.join(it.entity.defName,it.entity.comment,';') }}';\n$blankline\n", "createIndex": "", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"applyFor": "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A", "type": "dbDDL", "createTable": "DROP TABLE IF EXISTS {{=it.entity.defKey}};\nCREATE TABLE {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.primaryKey }}{{ pkList.push(field.defKey) }}{{?}}\n    {{=field.defKey}} {{=field.type}}{{?field.len>0}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{?}}{{=')'}}{{?}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTOINCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }} --{{=it.func.join(field.defName,field.comment,';')}}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n)  ; --{{=it.func.join(it.entity.defName,it.entity.comment,';') }}\n$blankline\n", "createIndex": "{{~ it.entity.indexes:index}}\nCREATE {{? index.unique}}UNIQUE {{?}}INDEX {{=index.defKey}} ON {{=it.entity.defKey}}({{ fieldsKeys = index.fields.map(function(field){return field.fieldDefKey}) ; }}{{=it.func.join(...fieldsKeys,',')}});\n{{~}}", "createView": "CREATE VIEW {{=it.view.defKey}} AS\nSELECT \n{{~it.view.fields:field:index}}\n    {{=field.refEntity}}.{{=field.refEntityField}} AS {{=field.defKey}}{{= index < it.view.fields.length-1 ? ',' : ''}}\n{{~}}\nFROM {{~it.view.refEntities:refEntity:index}}{{=refEntity}}{{= index < it.view.refEntities.length-1 ? ',' : ''}}{{~}};\n$blankline\n", "message": "", "update": ""}, {"applyFor": "895CFD1D-4273-4D32-A2C4-CAC70200AB5B", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<Page<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        return ResponseEntity.ok({{=serviceVarName}}.paginQuery({{=beanVarName}}, pageRequest));\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.queryById({{=pkVarName}});\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        long total = {{=mapperName}}.count({{=beanVarName}});\n        return new PageImpl<>({{=mapperName}}.queryAllByLimit({{=beanVarName}}, pageRequest), pageRequest, total);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.update({{=beanVarName}});\n        return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\nimport java.util.List;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.data.domain.Pageable;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper{\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    \n    /** \n     * 分页查询指定行数据\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @param pageable 分页对象\n     * @return 对象列表\n     */\n    List<{{=beanClass}}> queryAllByLimit({{=beanClass}} {{=beanVarName}}, @Param(\"pageable\") Pageable pageable);\n\n    /** \n     * 统计总行数\n     *\n     * @param {{=beanVarName}} 查询条件\n     * @return 总行数\n     */\n    long count({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int insert({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 批量新增数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 批量新增或按主键更新数据\n     *\n     * @param entities List<{{=beanClass}}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertOrUpdateBatch(@Param(\"entities\") List<{{=beanClass}}> entities);\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 影响行数\n     */\n    int update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 影响行数\n     */\n    int deleteById({{=pkDataType}} {{=pkVarName}});\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n    <resultMap type=\"{{=pkgName}}.entity.{{=beanClass}}\" id=\"{{=beanClass}}Map\">\n    {{~it.entity.fields:field:index}}\n        <result property=\"{{=it.func.camel(field.defKey,false)}}\" column=\"{{=field.defKey}}\" jdbcType=\"{{=field.dbType}}\"/>\n    {{~}}\n    </resultMap>\n    $blankline\n    <!-- 通过ID查询单条数据 -->\n    <select id=\"queryById\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </select>\n    $blankline\n    <!--分页查询指定行数据-->\n    <select id=\"queryAllByLimit\" resultMap=\"{{=beanClass}}Map\">\n        select\n            {{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}}\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n        limit #{pageable.offset}, #{pageable.pageSize}\n    </select>\n    $blankline\n    <!--统计总行数-->\n    <select id=\"count\" resultType=\"java.lang.Long\">\n        select count(1)\n        from {{=it.entity.defKey}}\n        <where>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                and {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}}\n            </if>\n        {{~}}\n        </where>\n    </select>\n    $blankline\n    <!--新增数据-->\n    <insert id=\"insert\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values ({{=it.entity.fields.map(function(e,i){return '#{'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n    </insert>\n    $blankline\n    <!-- 批量新增数据 -->\n    <insert id=\"insertBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n    </insert>\n    $blankline\n    <!-- 批量新增或按主键更新数据 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"{{=pkField}}\" useGeneratedKeys=\"true\">\n        insert into {{=it.entity.defKey}}({{=it.entity.fields.map(function(e,i){return e.defKey}).join(',')}})\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            ({{=it.entity.fields.map(function(e,i){return '#{entity.'+it.func.camel(e.defKey,false)+'}'}).join(',')}})\n        </foreach>\n        on duplicate key update\n        {{=it.entity.fields.map(function(e,i){return e.defKey + '=values('+e.defKey+')'}).join(',\\n\\t\\t')}}\n    </insert>\n    $blankline\n    <!-- 更新数据 -->\n    <update id=\"update\">\n        update {{=it.entity.defKey}}\n        <set>\n        {{~it.entity.fields:field:index}}\n            <if test=\"{{=it.func.camel(field.defKey,false)}} != null and {{=it.func.camel(field.defKey,false)}} != ''\">\n                {{=field.defKey}} = #{{{=it.func.camel(field.defKey,false)}}},\n            </if>\n        {{~}}\n        </set>\n        where {{=pkField}} = #{{{=pkVarName}}}\n    </update>\n    $blankline\n    <!--通过主键删除-->\n    <delete id=\"deleteById\">\n        delete from {{=it.entity.defKey}} where {{=pkField}} = #{{{=pkVarName}}}\n    </delete>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "A2EE7B4A-CE62-4290-B00C-B26C1BF18073", "type": "appCode", "Controller": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.controller;\n$blankline\nimport java.util.List;\nimport io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.service.{{=serviceClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表控制层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Api(tags = \"{{=it.entity.defName}}对象功能接口\")\n@RestController\n@RequestMapping(\"/{{=it.func.camel(it.entity.defKey,false)}}\")\npublic class {{=beanClass}}Controller{\n    @Autowired\n    private {{=serviceClass}} {{=serviceVarName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    @ApiOperation(\"通过ID查询单条数据\")\n    @GetMapping(\"{{{=it.func.camel(pkVarName,false)}}}\")\n    public ResponseEntity<{{=beanClass}}> queryById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.queryById({{=pkVarName}}));\n    }\n    $blankline\n    /** \n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param pageRequest 分页对象\n     * @return 查询结果\n     */\n    @ApiOperation(\"分页查询\")\n    @GetMapping\n    public ResponseEntity<PageImpl<{{=beanClass}}>> paginQuery({{=beanClass}} {{=beanVarName}}, PageRequest pageRequest){\n        //1.分页参数\n        long current = pageRequest.getPageNumber();\n        long size = pageRequest.getPageSize();\n\n        //2.分页查询\n        /*把Mybatis的分页对象做封装转换，MP的分页对象上有一些SQL敏感信息，还是通过spring的分页模型来封装数据吧*/\n        com.baomidou.mybatisplus.extension.plugins.pagination.Page<{{=beanClass}}> pageResult = {{=serviceVarName}}.paginQuery({{=beanVarName}}, current,size);\n\n        //3. 分页结果组装\n        List<{{=beanClass}}> dataList = pageResult.getRecords();\n        long total = pageResult.getTotal();\n        PageImpl<{{=beanClass}}> retPage = new PageImpl<{{=beanClass}}>(dataList,pageRequest,total);\n        return ResponseEntity.ok(retPage);\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"新增数据\")\n    @PostMapping\n    public ResponseEntity<{{=beanClass}}> add({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.insert({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    @ApiOperation(\"更新数据\")\n    @PutMapping\n    public ResponseEntity<{{=beanClass}}> edit({{=beanClass}} {{=beanVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.update({{=beanVarName}}));\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    @ApiOperation(\"通过主键删除数据\")\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById({{=pkDataType}} {{=pkVarName}}){\n        return ResponseEntity.ok({{=serviceVarName}}.deleteById({{=pkVarName}}));\n    }\n}", "Service": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.service;\n$blankline\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务接口\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\npublic interface {{=serviceClass}}{\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}});\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size);\n\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} insert({{=beanClass}} {{=beanVarName}});\n\n    \n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    {{=beanClass}} update({{=beanClass}} {{=beanVarName}});\n\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    boolean deleteById({{=pkDataType}} {{=pkVarName}});\n}", "ServiceImpl": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkVarNameU = \"UndefinedId\";\n    var pkFieldKey = \"UNDEFINED\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkFieldKey = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkVarNameU = it.func.camel(field.defKey,true);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    var mapperName = beanVarName+'Mapper';\n    \n}}package {{=pkgName}}.service.impl;\n$blankline\nimport cn.hutool.core.util.StrUtil;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;\n\nimport {{=pkgName}}.entity.{{=beanClass}};\nimport {{=pkgName}}.mapper.{{=beanClass}}Mapper;\nimport {{=pkgName}}.service.{{=serviceClass}};\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表服务实现类\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Service\npublic class {{=serviceClass}}Impl implements {{=serviceClass}}{\n    @Autowired\n    private {{=beanClass}}Mapper {{=mapperName}};\n    $blankline\n    /** \n     * 通过ID查询单条数据 \n     *\n     * @param {{=pkVarName}} 主键\n     * @return 实例对象\n     */\n    public {{=beanClass}} queryById({{=pkDataType}} {{=pkVarName}}){\n        return {{=mapperName}}.selectById({{=pkVarName}});\n    }\n    $blankline\n    /**\n     * 分页查询\n     *\n     * @param {{=beanVarName}} 筛选条件\n     * @param current 当前页码\n     * @param size  每页大小\n     * @return\n     */\n    public Page<{{=beanClass}}> paginQuery({{=beanClass}} {{=beanVarName}}, long current, long size){\n        //1. 构建动态查询条件\n        LambdaQueryWrapper<{{=beanClass}}> queryWrapper = new LambdaQueryWrapper<>();\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            queryWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n\n        //2. 执行分页查询\n        Page<{{=beanClass}}> pagin = new Page<>(current , size , true);\n        IPage<{{=beanClass}}> selectResult = {{=mapperName}}.selectByPage(pagin , queryWrapper);\n        pagin.setPages(selectResult.getPages());\n        pagin.setTotal(selectResult.getTotal());\n        pagin.setRecords(selectResult.getRecords());\n\n        //3. 返回结果\n        return pagin;\n    }\n    $blankline\n    /** \n     * 新增数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} insert({{=beanClass}} {{=beanVarName}}){\n        {{=mapperName}}.insert({{=beanVarName}});\n        return {{=beanVarName}};\n    }\n    $blankline\n    /** \n     * 更新数据\n     *\n     * @param {{=beanVarName}} 实例对象\n     * @return 实例对象\n     */\n    public {{=beanClass}} update({{=beanClass}} {{=beanVarName}}){\n        //1. 根据条件动态更新\n        LambdaUpdateChainWrapper<{{=beanClass}}> chainWrapper = new LambdaUpdateChainWrapper<{{=beanClass}}>({{=mapperName}});\n        {{~it.entity.fields.filter(function(e){return e[\"type\"]===\"String\"&&e.defKey !== pkFieldKey}):field:index}}\n        if(StrUtil.isNotBlank({{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}())){\n            chainWrapper.eq({{=beanClass}}::get{{=it.func.camel(field.defKey,true)}}, {{=beanVarName}}.get{{=it.func.camel(field.defKey,true)}}());\n        }\n        {{~}}\n        //2. 设置主键，并更新\n        chainWrapper.set({{=beanClass}}::get{{=pkVarNameU}}, {{=beanVarName}}.get{{=pkVarNameU}}());\n        boolean ret = chainWrapper.update();\n        //3. 更新成功了，查询最最对象返回\n        if(ret){\n            return queryById({{=beanVarName}}.get{{=pkVarNameU}}());\n        }else{\n            return {{=beanVarName}};\n        }\n    }\n    $blankline\n    /** \n     * 通过主键删除数据\n     *\n     * @param {{=pkVarName}} 主键\n     * @return 是否成功\n     */\n    public boolean deleteById({{=pkDataType}} {{=pkVarName}}){\n        int total = {{=mapperName}}.deleteById({{=pkVarName}});\n        return total > 0;\n    }\n}", "Mapper": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.mapper;\n$blankline\n\nimport com.baomidou.mybatisplus.core.conditions.Wrapper;\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport com.baomidou.mybatisplus.core.metadata.IPage;\nimport com.baomidou.mybatisplus.core.toolkit.Constants;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport {{=pkgName}}.entity.{{=beanClass}};\n$blankline\n\n /**\n * {{=it.entity.defName}};({{=it.entity.defKey}})表数据库访问层\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@Mapper\npublic interface {{=beanClass}}Mapper  extends BaseMapper<{{=beanClass}}>{\n    /** \n     * 分页查询指定行数据\n     *\n     * @param page 分页参数\n     * @param wrapper 动态查询条件\n     * @return 分页对象列表\n     */\n    IPage<{{=beanClass}}> selectByPage(IPage<{{=beanClass}}> page , @Param(Constants.WRAPPER) Wrapper<{{=beanClass}}> wrapper);\n}", "Mapper.xml": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    var pkField = \"UNDEFINED_ID\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkField = field.defKey;\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n$blankline\n\n<mapper namespace=\"{{=pkgName}}.mapper.{{=beanClass}}Mapper\">\n     <select id=\"selectByPage\" resultType=\"{{=pkgName}}.entity.{{=beanClass}}\">\n        select * from user ${ew.customSqlSegment}\n    </select>\n</mapper>\n\n", "Entity": "{{  var today=new Date();\n    var fullYear=today.getFullYear();\n    var month=today.getMonth() + 1;\n    var days=today.getDate();\n    \n    var pkVarName = \"undefinedId\";\n    var pkDataType = \"String\";\n    it.entity.fields.forEach(function(field){\n        if(field.primaryKey){\n            pkVarName = it.func.camel(field.defKey,false);\n            pkDataType = field[\"type\"];\n            return;\n        }\n    });\n    \n    var pkgName = it.entity.env.base.nameSpace;\n    var beanClass = it.entity.env.base.codeRoot;\n    var beanVarName = beanClass.charAt(0).toLowerCase()+beanClass.slice(1);\n    var serviceClass = beanClass+'Service';\n    var serviceVarName= beanVarName+'Service';\n    \n}}package {{=pkgName}}.entity;\n$blankline\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n\n /**\n * {{=it.entity.defName}};{{=it.entity.comment}}\n * <AUTHOR> http://www.chiner.pro\n * @date : {{=fullYear}}-{{=month}}-{{=days}}\n */\n@ApiModel(value = \"{{=it.entity.defName}}\",description = \"{{=it.entity.comment}}\")\n@TableName(\"{{=it.entity.defKey}}\")\npublic class {{=beanClass}} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    @ApiModelProperty(name = \"{{=field.defName}}\",notes = \"{{=field.comment}}\")\n    {{? field.primaryKey }}\n    @TableId\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.defKey,false)}} ;\n{{~}}\n$blankline\n\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.defKey,true)}}(){\n        return this.{{=it.func.camel(field.defKey,false)}};\n    }\n    /** {{=it.func.join(field.defName,field.comment,';')}} */\n    public void set{{=it.func.camel(field.defKey,true)}}({{=field.type}} {{= it.func.camel(field.defKey,false) }}){\n        this.{{=it.func.camel(field.defKey,false)}}={{=it.func.camel(field.defKey,false)}};\n    }\n{{~}}\n}"}, {"applyFor": "81CCA482-3F4D-4EAC-8CF9-F5E7BC098AD2", "type": "dbDDL", "createTable": "/**字段名,关键字等全部用的小写*/\ndrop table if exists {{=it.entity.defKey}};\n/**补充上库名,external关键字根据建表规范看是否添加*/\ncreate [external] table if not exists {{=it.entity.defKey}}(\n{{ pkList = [] ; }}\n/**这里把varchar,char,text,date,datetime字段全部映射为string类型.tinyint unsigned,bit,Integer,tinyint,smallint,mediumint映射为int类型,int unsigned映射为bigint.其它自定义映射规则根据自己情况修改*/\n/**当长度>0只有为decimal类型或double类型时才保留长度和小数的位数*/\n{{~it.entity.fields:field:index}}\n    {{=it.func.lowerCase(field.defKey)}} {{=it.func.lowerCase(field.dbType)=='varchar'||it.func.lowerCase(field.dbType)=='char'||it.func.lowerCase(field.dbType)=='text'||it.func.lowerCase(field.dbType)=='date'||it.func.lowerCase(field.dbType)=='datetime' ? 'string':it.func.lowerCase(field.dbType)=='tinyint unsigned'||it.func.lowerCase(field.dbType)=='bit'||it.func.lowerCase(field.dbType)=='integer'||it.func.lowerCase(field.dbType)=='tinyint'||it.func.lowerCase(field.dbType)=='smallint'||it.func.lowerCase(field.dbType)=='mediumint' ? 'int':it.func.lowerCase(field.dbType)=='int unsigned' ? 'bigint':it.func.lowerCase(field.dbType)}}{{?field.len>0&&(it.func.lowerCase(field.dbType)=='decimal'||it.func.lowerCase(field.dbType)=='double')}}{{='('}}{{=field.len}}{{?field.scale>0}}{{=','}}{{=field.scale}}{{=')'}}{{?}}{{?}} comment '{{=it.func.join(field.defName,field.comment,'')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n{{?}}\n)\n{{\n    let partitionedBy = it.entity.properties['partitioned by'];\n    partitionedBy = partitionedBy?partitionedBy:'请在扩展属性中配置[partitioned by]属性';\n}}\ncomment '{{=it.func.join(it.entity.defName,';') }}'\n/**是否分区表,分区字段名和字段注释自定义*/\n[partitioned by {{=partitionedBy}}]\n/**文件存储格式自定义*/\n[stored as orc]\n/**hdfs上的地址自定义*/\n[location xxx]\n;", "createView": "", "deleteTable": "", "createIndex": "", "deleteIndex": "", "message": "", "update": ""}], "generatorDoc": {"docTemplate": ""}, "relationFieldSize": 30, "uiHint": [], "menuWidth": "292px", "modelType": "modalAll", "relationType": "field", "headers": [{"refKey": "def<PERSON><PERSON>", "hideInGraph": false, "value": "字段代码"}, {"refKey": "defName", "hideInGraph": false, "value": "显示名称"}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false, "value": "主键"}, {"refKey": "notNull", "hideInGraph": true, "value": "不为空"}, {"refKey": "autoIncrement", "hideInGraph": true, "value": "自增"}, {"refKey": "domain", "hideInGraph": true, "value": "数据域"}, {"refKey": "type", "hideInGraph": false, "value": "数据类型"}, {"refKey": "len", "hideInGraph": false, "value": "长度"}, {"refKey": "scale", "hideInGraph": true, "value": "小数位数"}, {"refKey": "defaultValue", "hideInGraph": true, "value": "默认值"}, {"refKey": "refDict", "hideInGraph": true, "value": "数据字典"}, {"refKey": "comment", "hideInGraph": true, "value": "说明"}, {"refKey": "isStandard", "hideInGraph": true, "value": "标准字段"}, {"refKey": "uiHint", "hideInGraph": true, "value": "UI建议"}, {"refKey": "extProps", "hideInGraph": true, "value": "拓展属性"}]}, "entities": [{"id": "2A14CE16-0C3D-4EC9-B2D3-538BA13B44B7", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_catalog", "defName": "服务目录", "comment": "服务目录，或者（服务市场）", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "7D2AF78D-FDDE-4019-B249-0E872CEBF200"}, {"defKey": "title_", "defName": "名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "724DFFCE-FF6C-4CB1-94F3-D4365A5109E9"}, {"defKey": "icon_", "defName": "图标", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "0F18255B-5DEC-4B78-962E-73CDB287230F", "id": "DC12B42F-0B12-4151-8E61-9785335B00DB", "isStandard": true}, {"defKey": "visibility_", "defName": "开放范围", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "'PUBLIC'", "hideInGraph": false, "refDict": "958EC5B6-4B2E-455D-B778-6F903CD13BAD", "extProps": {}, "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "CAEC26FA-4C92-4EB9-B7A4-0798008528D2"}, {"defKey": "sort_order", "defName": "排序", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "100", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "467D80A5-23C5-451A-A7D0-F03994C25130"}, {"defKey": "summary_", "defName": "描述", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "id": "56707087-85CB-4E41-BCA3-885D4E703835"}, {"rowNo": 5, "defKey": "system_", "defName": "是否内置", "comment": "内置群组有：所有人，企业用户组，外部用户组，非受限用户组，受限用户组，每个租户都会有默认的内置群组", "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "typeFullName": "BIT(1)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "34F420EA-CD1A-4580-92FC-76F1051F0C9B", "id": "68DB8676-1F54-47AA-A801-ED6BDC6A6A3F", "isStandard": true}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "93A84F50-BDB1-43A1-81DC-4D45EBB822DC", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "E4C7A707-23BB-4B4E-B716-C3C198BBAB68", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "0E19B6E9-6BF0-46C7-8A77-0D5FA392575D", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "6F979232-5B53-4C65-B2EB-3665A0B0682E", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "2EA04B0B-50DC-49E6-A546-9CF181F5E0C2", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "DB8F595E-8D02-4047-BB3E-A2F4817CE595", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_category", "defName": "服务分类", "comment": "服务目录", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "6C461718-D69C-4BD8-B436-E7AC95986F16"}, {"defKey": "icon_", "defName": "图标", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "0F18255B-5DEC-4B78-962E-73CDB287230F", "id": "4C900A9D-A98F-4EBB-9E9D-A501BD5D7F36", "isStandard": true}, {"defKey": "title_", "defName": "分类名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "43681897-4F7D-42EB-9371-AD4365974F38"}, {"rowNo": 5, "defKey": "sort_order", "defName": "排序顺序", "comment": "", "domain": "CBD62BBF-5707-41C2-B736-BED0A6FF2598", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "100", "hideInGraph": false, "typeFullName": "INT(11)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "CA243149-650E-41CA-9D7E-28ACC5AC65F1", "isStandard": true}, {"defKey": "parent_id", "defName": "上级分类", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "871D5C73-1C10-428E-9A2D-981FB54C6C69"}, {"defKey": "catalog_id", "defName": "所属目录", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "6A8985CA-6906-42FE-90DA-111B28BCB767"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "1633038B-C9AD-4412-A3C7-11140C469FBE", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "4E380437-C482-4686-A213-A0DD6DCB7E88", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "86F7DC5F-7669-4234-8434-DD0B393CD730", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "799AA2D0-720D-4663-903B-B4F4D66BABD3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "64406D9E-F55A-4A5D-B473-0B5C17A5E3CE", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "D3770455-F51E-427A-9B64-5CDC9ED00C09", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service", "defName": "服务", "comment": "", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "C8CD8908-59FB-4E44-B376-1330FE7540B1"}, {"defKey": "title_", "defName": "服务名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "D37E12D7-B785-4D85-B90B-90C393CAC7F3"}, {"defKey": "code_", "defName": "服务编码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "345DDF0D-6B21-4A0B-8C52-F2E7A3B22D65"}, {"defKey": "type_", "defName": "服务类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "'S'", "hideInGraph": false, "refDict": "6AFC4A28-F3E7-45FD-B2C3-3952AEEA17BC", "extProps": {}, "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "E1DF3664-C0DA-442A-B66B-3B289CAE371F"}, {"defKey": "summary_", "defName": "摘要", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "id": "F8007D08-B51F-478E-9542-B398256EB401"}, {"rowNo": 5, "defKey": "sort_order", "defName": "排序顺序", "comment": "", "domain": "CBD62BBF-5707-41C2-B736-BED0A6FF2598", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "100", "hideInGraph": false, "typeFullName": "INT(11)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "25BD8DFC-20F3-4846-9F03-A1AC380C17A2", "isStandard": true}, {"defKey": "app_id", "defName": "所属应用", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "366A2121-1BF0-4CEC-982D-AC2030B9A774"}, {"defKey": "client_id", "defName": "关联应用账号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "24C37839-FD4D-474F-A1C3-5A71D7337785"}, {"defKey": "version_", "defName": "版本号", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "583C4166-AC13-4590-BEE1-B90500CA9846"}, {"defKey": "icon_", "defName": "图标", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "0F18255B-5DEC-4B78-962E-73CDB287230F", "id": "4592235F-99D4-4801-AFF1-BFED7CD20292", "isStandard": true}, {"defKey": "master_id", "defName": "责任人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "913DF11E-0A0E-4441-8037-00053DEB8D6D"}, {"defKey": "org_id", "defName": "所属组织ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "72816EB8-32F5-4439-97DC-7336C30ABA34", "isStandard": true}, {"defKey": "apply_form", "defName": "申请表单", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "96B737A1-8009-4946-8FD0-F439EEB87BA4"}, {"defKey": "home_url", "defName": "服务入口", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "2FCE01F9-D8AB-4013-8F93-F3C76EC654EC"}, {"defKey": "console_url", "defName": "管理入口", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "008DF783-EACD-450D-86EA-106DB9EEF21A"}, {"defKey": "demo_url", "defName": "演示地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "5C8D8D2D-9111-4F98-94C3-D0B97A88CA0C"}, {"defKey": "docs_url", "defName": "文档地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "8D39DE8F-6450-4DE3-BE4D-C37DADDA6CD1"}, {"defKey": "app_inte_url", "defName": "应用集成地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "0563E8B8-E959-4AFE-B832-FA3A1358D24B"}, {"defKey": "external_id", "defName": "外部ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "45093674-0D63-4DBB-A113-6C8FACDE7029"}, {"defKey": "provider_id", "defName": "服务源ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "C4E5DF7D-4C3B-4CC2-80E0-F15BA7727664"}, {"defKey": "developer_", "defName": "提供商名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "F1293116-59C5-47D2-8B64-DD438C690EA7"}, {"defKey": "readme_", "defName": "门户介绍页面", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "33048F09-5F71-49F0-96A8-6D67F54981A4", "id": "3B83EB4F-9DC5-4909-84CA-DF3555E3F9B4"}, {"defKey": "granted_count", "defName": "开通客户端数", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "4631FFDB-210B-40F0-A149-C4D39293D16A"}, {"defKey": "call_count", "defName": "服务接口调用数", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "2ACD31C1-932B-4738-A2F1-7D73BC9B6624"}, {"defKey": "viewed_at", "defName": "最新访问时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "F916279B-4E19-4206-9164-2FDD2A18FDF7"}, {"defKey": "view_count", "defName": "访问次数", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "", "id": "469FEE4B-5686-4AF7-B6D5-A002D7D3044D"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "3677FE26-CF8B-4129-B29B-7804B3DBC748", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "609E2ABE-2F0E-4505-9914-E3AB8371D281", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "548C2D29-C679-412B-A67F-284BB656974C", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "B9A9D90A-F8AD-41EC-B9CE-8E364531F97F", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F46AB530-411E-4A9C-A08D-842D1D2C5B17", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "CDD515F6-9B4B-42D1-9760-B61B94CE2D02", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_publish_req", "defName": "服务发布申请", "comment": "服务发布记录", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "A0CBEBF4-2C9B-4116-AFCB-2572C09211CA"}, {"defKey": "service_id", "defName": "服务Id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "B9283FDD-D1CD-4139-A90E-4E1C7D31BD35"}, {"defKey": "version_", "defName": "服务版本", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "A7ECF82D-714B-475D-8980-10C489FEBA30"}, {"defKey": "catalog_id", "defName": "目录Id", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "72ECA08F-A7E1-4AD7-8E79-047D893A65D2"}, {"defKey": "category_ids", "defName": "发布分类Id", "comment": "已发布服务目录的集合", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "161C6A82-2931-4760-8765-EEEBB8A63ACA", "id": "14EB8E29-0DA1-4DDE-B911-4ED89CD714E0"}, {"defKey": "status_", "defName": "状态", "comment": "", "type": "INT", "len": 1, "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "83ACE40C-60EE-4213-8328-520E82462189", "extProps": {}, "domain": "", "id": "3FB97B47-2B40-4F10-A755-8F181EE34D29"}, {"defKey": "summary_", "defName": "发布说明", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "id": "FFC2F489-C9F3-4464-A917-7182F6F2A4D6"}, {"defKey": "detail_id", "defName": "发布详情ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "B1814F2E-D6FB-455F-AA7D-F08D31EE710B"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "BF9C7C25-E9B9-48C2-87B9-092ED2AD617E", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "D6DC087D-B607-4211-B6DD-6F09FBA6C9E6", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "6C14FD41-541E-40C1-A237-250E7CF586AA", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "B55E9D37-ED64-47BB-A37A-F306BF9D07A1", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "0F87786F-AB61-4C14-BB0C-1AC4D3ADF986", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": [{"defKey": "uk_service_publish_req_market", "defName": null, "unique": false, "comment": "", "fields": [{"fieldDefKey": "B9283FDD-D1CD-4139-A90E-4E1C7D31BD35", "ascOrDesc": "A", "id": "014ADC1D-9BF8-45DD-AAB4-837551E0BB66"}, {"fieldDefKey": "72ECA08F-A7E1-4AD7-8E79-047D893A65D2", "ascOrDesc": "A", "id": "64DB34D7-203A-467C-8620-E1E1954B27D6"}], "id": "BD5E9BC2-016F-4C45-B891-B923977B112E"}]}, {"id": "430F0056-364B-4001-839D-8EF6B81D7F0C", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_category", "defName": "服务发布记录", "comment": "服务发布到的服务目录", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "28C06312-3930-41DE-B879-076C79504924"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "5262215A-A8C3-4C6B-A0B0-64335BC0CEE0"}, {"defKey": "catalog_id", "defName": "目录ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "DCE0F9D1-8A13-4FE8-8B69-F9BAD181046D"}, {"defKey": "category_id", "defName": "分类ID", "comment": "支持只发布到市场，而不有目录", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "FB2AC934-601E-4E11-8F95-A3EDE1635C70"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "3B10B694-8D6C-4FA2-86DA-D0F9D0B8D1D1", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "1352EEE9-5437-4EBB-B727-651A93AF5AA3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "1AD190FA-50B1-42CD-9B3B-5B22B3DEC7F4", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_req_item", "defName": "服务申请项", "comment": "服务开通、变更等申请单", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "DF73FD48-83FD-4A7D-A3D6-CDE6A3A59E4C"}, {"defKey": "req_id", "defName": "申请单ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "CB4F638E-F534-48CE-BA96-2B03CC5C7D7D"}, {"defKey": "type_", "defName": "申请类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "6E7890B5-3B55-48F2-AB4A-C477C0F7DD45", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "AD7DF027-A6CA-4087-B5AF-97B2DF54DEB2"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "B43B6D91-6690-4C61-AF3D-28DE7EBA3BB5"}, {"defKey": "app_id", "defName": "申请的应用ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "23C796EB-7B81-4773-8ADA-9893A5C94F9D"}, {"defKey": "client_id", "defName": "申请的ClientID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "17B88931-29DC-451B-840E-D2591AEBC130"}, {"defKey": "status_", "defName": "申请单状态", "comment": "", "type": "INT", "len": 1, "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "83ACE40C-60EE-4213-8328-520E82462189", "extProps": {}, "domain": "", "id": "42D18C63-2F86-4F7D-AEDD-BCD18766A430"}, {"defKey": "form_data", "defName": "申请表单内容", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "33048F09-5F71-49F0-96A8-6D67F54981A4", "id": "3FB4CEB7-718B-4EDF-812E-715845CA8648"}, {"defKey": "comment_", "defName": "申请原因", "comment": "", "type": "VARCHAR", "len": 500, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "", "id": "F8159CAA-6680-4D04-84E3-70332BBF98ED"}, {"defKey": "authz_", "defName": "服务授权信息", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "33048F09-5F71-49F0-96A8-6D67F54981A4", "id": "03815C46-DF40-4B83-90F5-F4DC7E2FD7AF"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "DFEE4B6D-4E9C-44E5-8F26-CC1CF149BD50", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "9CF70038-3A1E-4785-8D4C-D9282ECF1767", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "4712427B-8B4B-481A-95C0-4B45359B591A", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "21DCAE86-E00F-4874-9962-FFBBA936951A", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "VARCHAR", "len": 36, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "", "id": "4424BA72-24A6-427D-B5BE-6291496DDD22", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "FE7A596F-B421-43FE-9434-83F14AE1949F", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_req", "defName": "服务申请单", "comment": "", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F277C7C6-052B-4C48-ACB0-080D327F3AF6"}, {"defKey": "title_", "defName": "申请单名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "C286C7E2-E7CE-4F8C-9453-14B70A17CEC2"}, {"defKey": "applicant_id", "defName": "申请人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "11BFAA38-236E-4A53-8F78-728F95F192DF"}, {"defKey": "status_", "defName": "申请单状态", "comment": "", "type": "INT", "len": 1, "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "83ACE40C-60EE-4213-8328-520E82462189", "extProps": {}, "domain": "", "id": "CE41D32A-3ADB-4F8E-9DB5-ED05382F18A0"}, {"defKey": "comment_", "defName": "审批意见", "comment": "", "type": "VARCHAR", "len": 500, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "2E3578C3-06FB-43BF-B999-7C77EDB5FC60"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "C31C00A5-D7C7-44B2-8AD4-3B02055547E2", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "AC4ED8A5-0892-4A37-9E4B-0332A48DE854", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "EAAB2F4E-490E-4ADC-9F43-0B138FFDD43C", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "F9658E24-0E72-4C13-B103-755837801C78", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "VARCHAR", "len": 36, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "", "id": "6CE3BE12-D147-4950-A07D-4E853ACA4497", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "09C4AC33-F3D4-447B-B0AB-8FB1B0824D89", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_publish_his", "defName": "服务发布历史", "comment": "服务发布历史", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "480B46F7-5AA5-4211-9DAD-8FC013DD356B"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "DBA2747E-86D3-4D78-AA96-51781A876C1C"}, {"defKey": "version_", "defName": "服务版本", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "E73DC744-02F9-472A-8FDF-C0FE93587991"}, {"defKey": "catalog_id", "defName": "目录ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "8598E4ED-88DE-4C41-86BD-49314339DDEA"}, {"defKey": "category_ids", "defName": "分类Ids", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "161C6A82-2931-4760-8765-EEEBB8A63ACA", "id": "75543DC9-0D76-48FC-946B-2B0D38E6F9A0"}, {"defKey": "status_", "defName": "审批状态", "comment": "", "type": "INT", "len": 1, "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "83ACE40C-60EE-4213-8328-520E82462189", "extProps": {}, "domain": "", "id": "06CA53B7-2A66-48FB-A90E-B46D9E7A7FBB"}, {"defKey": "summary_", "defName": "发布摘要", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "id": "3FCAD94D-E49C-4451-B5D3-DC7DF2B635EE"}, {"defKey": "snapshot_", "defName": "服务快照", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "33048F09-5F71-49F0-96A8-6D67F54981A4", "id": "21A5B816-1539-43A3-B406-EFB79CB82D28"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "4B2DE92B-678A-4424-B825-5E73EE65B239", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "65444D4F-B1A3-47F8-BBB2-94A5F2EBD128", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "FA2FD637-B09B-42A7-B296-9302967DD1E3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "614A656B-D235-42CE-B7B1-19120D070868", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "9F843779-280A-4C17-9BD3-9B00841D4996", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": [{"defKey": "ix_service_publish_his_service", "defName": null, "unique": false, "comment": "", "fields": [{"fieldDefKey": "DBA2747E-86D3-4D78-AA96-51781A876C1C", "ascOrDesc": "A", "id": "EECF0131-0B59-4559-BA6B-01E6B19273A0"}], "id": "9AC352CB-05DF-4215-AA82-D719851DC0D5"}]}, {"id": "B8F3B528-58AE-4559-A077-A6371E9A52DA", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_sub", "defName": "服务订阅关系", "comment": "服务与租户间的订阅开通关系", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "DC372CCD-F365-4F9A-8814-00F5CDB1BCAE"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F6163162-4B74-4493-9971-3DDDDD301FF8"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "DE1BE333-4D22-4E8D-84DF-D2AE923991ED", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "371D4B5A-2FAF-4DE7-925E-46EADEE16714", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "C296F189-2DFF-4B06-935F-65850A7944EA", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "A8C6CF93-CD29-4B42-BBD7-B50AAC768071"}, {"defKey": "updated_by", "defName": "更新人", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "967AC735-D976-4C6E-B57D-40FF42F3F703"}], "correlations": [], "indexes": []}, {"id": "0A0D4C7E-8E0B-475A-B8DC-6854EAD5C9F9", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_api", "defName": "服务API", "comment": "服务包含的API", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "385EE659-312E-485F-AC06-3A85030D8F47"}, {"defKey": "title_", "defName": "API名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "ECC5D141-4F64-4A60-9B47-3B4A47B56DDD"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "EE5B12D9-63DD-444B-94D3-7137E99A4802"}, {"defKey": "external_id", "defName": "外部关联ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "31B6BF3E-95FB-482F-BFE5-C682F48ACD69"}, {"defKey": "docs_url", "defName": "API文档(swagger)", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "FEDEFB67-4E72-4106-8FBC-CB2E17AE7353"}, {"defKey": "url_", "defName": "API调用地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "8A5CC444-9D84-410C-8A18-19333728A921"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "EB9AB0BB-6F1A-47F9-A425-9CD34C6903A8", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "2CD457E6-AC6C-4F2A-BF5E-34AF165E5308", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "3CCE291A-2569-445B-844D-91F0A36DD129", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "188369D8-0880-4F72-A20C-A5ACAB672CF6", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F9AB28A2-2A1D-4700-B23B-86A647881DD3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "E3B51382-8A00-4E43-8B3E-43C145DFF7DF", "env": {"base": {"nameSpace": "", "codeRoot": ""}}, "defKey": "fuse_service_comment", "defName": "服务评论", "comment": "", "properties": {}, "nameTemplate": "{defKey}[{defName}]", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "3514E840-24CD-435E-A647-C96558322BB9"}, {"defKey": "service_id", "defName": "服务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "28F9D1AA-11E4-4A73-A87B-939BCD541D5C"}, {"defKey": "rate_", "defName": "评分", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "42888FC0-C464-47A2-880D-B515EA7803B8"}, {"defKey": "content_", "defName": "评论内容", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "id": "DA179140-30C7-4EEB-9413-C3DF09B1D73F"}, {"defKey": "content_length", "defName": "评论字数", "comment": "", "type": "INT", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "DF81BD61-7B3A-4572-B230-00791EEFC41C"}, {"defKey": "anonymous_", "defName": "是否匿名评论", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "id": "5E1BC299-6495-4661-9468-D6CC6BACF10C"}, {"defKey": "reply_id", "defName": "回复的评论ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "92E500BB-8CB8-43C1-A9FE-BC2306E081A6"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "48D07745-F8F2-4E85-B6EA-DEF25D8DB4F4", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "0D6FF065-A7A3-4864-8B11-5BD72F4CA4D6", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "A4281F81-37B6-4C24-8DE4-2290F8B7B394", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "DDF35A8F-5351-4508-8F05-165130A9F67B", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F623B250-4210-4DB4-B993-931335960180", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "correlations": [], "indexes": []}, {"id": "6EBC9AD1-FA00-4F59-BCF7-1C8E569366A4", "defKey": "fuse_provider", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F93088EF-2FD4-4C45-B7F5-681D1223143F"}, {"defKey": "title_", "defName": "名称", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "9ED4203F-4373-4ED3-B8A7-CB0B114AF85F", "uiHint": "18A8D3D1-9E32-4D8A-911E-36DAF7932B07"}, {"defKey": "webhook_url", "defName": "回调地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "D91C9654-9086-4126-BF94-A947D3163021"}, {"defKey": "site_url", "defName": "站点地址", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "4071F174-D871-44CB-9E57-C608DE0557E2", "id": "F2D91D06-8A1F-4128-B37C-D2C80AA5FCD6"}, {"defKey": "secret_", "defName": "调用密钥", "comment": "", "type": "VARCHAR", "len": 1000, "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "", "id": "E80EC3FB-655D-4529-A2F5-D73F16D00561"}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "F909B205-7F1B-4993-99AA-E0EC089FE63E", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "E85468E4-FA56-42BD-9608-9883A3B471D2", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "786906A3-1841-408D-BB94-FD765886E7A3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "58D01496-3E33-472E-97A0-979283986BAC", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "properties": {}, "defName": "服务源", "indexes": [], "correlations": []}, {"id": "7C3FF4BF-B4AF-46E3-ADEF-F7DC4D00BA47", "defKey": "iam_metrics_registry", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F93088EF-2FD4-4C45-B7F5-681D1223143F"}, {"defKey": "name_", "defName": "指标名称", "comment": "名称", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "09631282-0DD1-4737-A61C-344CA303798F", "isStandard": true}, {"defKey": "code_", "defName": "指标编码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "F2D91D06-8A1F-4128-B37C-D2C80AA5FCD6"}, {"defKey": "job_id", "defName": "任务ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "EC4CCB5A-A9C5-486E-B077-86FA0ABEE042"}, {"rowNo": 2, "defKey": "summary_", "defName": "简要说明", "comment": "", "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "typeFullName": "VARCHAR(150)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "28B32A02-AC14-4CB5-AD7E-4F0CDD144949", "isStandard": true}, {"defKey": "config_", "defName": "统计配置", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "44D2086C-5D81-4BFB-BDB6-0F1ADB28C797", "id": "BEC2172A-CD2E-463A-BD26-1B47EAC7CBDA"}, {"defKey": "enabled_", "defName": "是否启用", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "id": "9183A281-2D8C-48F3-BF38-4DF9182BF450"}, {"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "0E35A933-EEEF-48AE-BD8C-70F5E7A397FC", "isStandard": true}, {"defKey": "created_at", "defName": "创建时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "F909B205-7F1B-4993-99AA-E0EC089FE63E", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "created_by", "defName": "创建人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "E85468E4-FA56-42BD-9608-9883A3B471D2", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "786906A3-1841-408D-BB94-FD765886E7A3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "updated_by", "defName": "更新人ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "58D01496-3E33-472E-97A0-979283986BAC", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}], "properties": {}, "defName": "指标注册登记表", "indexes": [], "correlations": []}, {"id": "FA5978FC-66FF-4888-9933-2B8AE45399C8", "defKey": "iam_metrics_data", "headers": [{"refKey": "hideInGraph", "hideInGraph": true}, {"refKey": "def<PERSON><PERSON>", "hideInGraph": false}, {"refKey": "defName", "hideInGraph": false}, {"refKey": "<PERSON><PERSON><PERSON>", "hideInGraph": false}, {"refKey": "notNull", "hideInGraph": true}, {"refKey": "autoIncrement", "hideInGraph": true}, {"refKey": "domain", "hideInGraph": true}, {"refKey": "type", "hideInGraph": false}, {"refKey": "len", "hideInGraph": false}, {"refKey": "scale", "hideInGraph": true}, {"refKey": "defaultValue", "hideInGraph": true}, {"refKey": "refDict", "hideInGraph": true}, {"refKey": "comment", "hideInGraph": true}, {"refKey": "isStandard", "hideInGraph": true}, {"refKey": "uiHint", "hideInGraph": true}, {"refKey": "extProps", "hideInGraph": true}], "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "F93088EF-2FD4-4C45-B7F5-681D1223143F"}, {"defKey": "name_", "defName": "指标名称", "comment": "名称", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "09631282-0DD1-4737-A61C-344CA303798F", "isStandard": true}, {"defKey": "code_", "defName": "指标编码", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "F2D91D06-8A1F-4128-B37C-D2C80AA5FCD6"}, {"defKey": "value_", "defName": "指标值", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "33048F09-5F71-49F0-96A8-6D67F54981A4", "id": "2A42CD0D-9A55-487A-B34F-E229A52A8365"}, {"defKey": "unit_", "defName": "指标单位", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "316D5FFD-2EAB-4420-AE3F-7BD24DF957C6"}, {"defKey": "v_type", "defName": "值类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "C46C3591-C4FF-4E40-8210-9C39B8B7CFCF"}, {"defKey": "owner_id", "defName": "指标统计维度的ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "9DF88AF5-9144-42F5-9556-51DC56CDF4CC", "id": "7F549F67-93A6-4A5A-9AD2-2F2A4206DAF4", "isStandard": true}, {"defKey": "owner_type", "defName": "指标统计维度的类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "75636DE0-BD32-416A-B5F5-524C63C35B09", "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "D5AED5FE-B1FB-4F08-B46B-D0F3F87727C3", "isStandard": true}, {"defKey": "registry_id", "defName": "指标登记ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "A89FFB28-A55A-45FD-B598-F23DA654D42C"}, {"defKey": "start_at", "defName": "统计范围的开始时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "F909B205-7F1B-4993-99AA-E0EC089FE63E", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "end_at", "defName": "统计范围的结束时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "786906A3-1841-408D-BB94-FD765886E7A3", "uiHint": "8EC2BAAC-3091-49FE-8C71-BAB9B48087CB"}, {"defKey": "monitor_at", "defName": "指标监控时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "4AD9A9A2-69F6-43E3-9DA6-4F850308FA69"}, {"defKey": "archive_", "defName": "是否归档数据", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "extProps": {}, "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "id": "0A639172-0D87-4AFC-9F67-27CBA5081D31"}, {"defKey": "type_", "defName": "指标记录类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "'S'", "hideInGraph": false, "refDict": "8E568B3E-05C4-4EC0-8169-86CDD45F3FD1", "extProps": {}, "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "53A76731-EA06-4DBB-A45B-1E893745F429"}], "properties": {}, "defName": "指标数据记录表", "indexes": [], "correlations": []}], "views": [], "dicts": [{"defKey": "ComType", "defName": "", "sort": "", "intro": "", "id": "35B27142-D219-481E-8440-559B0F4D2F1D", "items": [{"defKey": "S", "defName": "Service", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "2A23358C-A29A-4B9F-976E-C2613B9C0C53"}, {"defKey": "A", "defName": "Api", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "BB22AE91-6939-47BD-AF69-200528653F27"}]}, {"defKey": "ApprovedStatus", "defName": "审核状态", "sort": "", "intro": "", "id": "83ACE40C-60EE-4213-8328-520E82462189", "items": [{"defKey": "-1", "defName": "取消审核/取消申请", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "34395265-2D66-4512-80FD-E3A4CA3DBD6B"}, {"defKey": "0", "defName": "未审核", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "3551BF7C-7710-4D6E-8E63-F1553CD32515"}, {"defKey": "1", "defName": "审核通过", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "7FB26A5C-EF46-4E62-8F83-9D3B30E75002"}, {"defKey": "2", "defName": "拒绝", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "CE551D2F-6A16-4541-9B0F-9234D2A98909"}]}, {"defKey": "ServiceType", "defName": "服务类型", "sort": "", "intro": "", "id": "6AFC4A28-F3E7-45FD-B2C3-3952AEEA17BC", "items": [{"defKey": "S", "defName": "服务", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "74641204-915C-4C05-B486-3BA4526C1C46"}, {"defKey": "P", "defName": "产品", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "9FC9E929-FBD2-4AD3-BD60-44FBB8D1DABB"}]}, {"defKey": "Visibility", "defName": "数据可见范围", "sort": "", "intro": "", "id": "958EC5B6-4B2E-455D-B778-6F903CD13BAD", "items": [{"defKey": "PUBLIC", "defName": "完全公开", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "C603B672-6C34-4777-8000-856092F8DB3D"}, {"defKey": "PROTECTED", "defName": "受保护的(租户及子租户)", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "5CC0537C-A996-49B3-B10A-DA88E7206AC7"}, {"defKey": "PRIVATE", "defName": "私有的(租户内部)", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "CE7DF002-4FF6-4AB9-87B3-33239E8611F9"}]}, {"defKey": "ServiceActionType", "defName": "申请单类型", "sort": "", "intro": "", "id": "6E7890B5-3B55-48F2-AB4A-C477C0F7DD45", "items": [{"defKey": "service_register", "defName": "服务注册", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "C2DC0894-5DAC-460A-82EA-2A73E5601AAF"}, {"defKey": "service_removed", "defName": "服务移除", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "74641204-915C-4C05-B486-3BA4526C1C46"}, {"defKey": "service_publish", "defName": "服务发布", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "89E6E02E-CF2D-47BF-8214-7C1962D1FA68"}, {"defKey": "service_publish_offline", "defName": "服务下架", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "01512BCF-314F-4BA6-B293-A378A8A04B0F"}, {"defKey": "service_agreement", "defName": "服务申请", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "9FC9E929-FBD2-4AD3-BD60-44FBB8D1DABB"}, {"defKey": "service_subscribe", "defName": "服务订阅", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "EB596FEF-13F1-4891-BAA4-70CCAC81A25A"}, {"defKey": "service_agreement_change", "defName": "改变服务申请数据", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "EC358B9A-62DB-4B07-B9D5-635BF89CD6BD"}, {"defKey": "service_agreement_expire", "defName": "服务过期", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "C5CA1FFB-0EC3-46E7-8D70-29F230C870BE"}, {"defKey": "service_agreement_destroy", "defName": "删除服务申请", "sort": "", "parentKey": "", "intro": "", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "1BD0DC3D-1E65-4F3F-A73D-12C45670EDC4"}]}, {"defKey": "MetricsDataType", "defName": "指标记录类型", "sort": "", "intro": "", "id": "8E568B3E-05C4-4EC0-8169-86CDD45F3FD1", "items": [{"defKey": "P", "defName": "过程数据", "sort": "", "parentKey": "", "intro": "Process", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "34395265-2D66-4512-80FD-E3A4CA3DBD6B"}, {"defKey": "S", "defName": "统计数据", "sort": "", "parentKey": "", "intro": "Statistics", "enabled": true, "attr1": "", "attr2": "", "attr3": "", "id": "3551BF7C-7710-4D6E-8E63-F1553CD32515"}]}], "viewGroups": [], "dataTypeMapping": {"referURL": "", "mappings": [{"defKey": "VARCHAR", "defName": "字符串", "id": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "VARCHAR", "72C35736-1226-4B9F-8497-B0DFF626B554": "VARCHAR2", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "VARCHAR", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "VARCHAR", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "VARCHAR", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "VARCHAR2", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "VARCHAR", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "VARCHAR", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "STRING", "5B3D490C-72F5-4988-9865-85FBE92F282A": "String", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "string", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "TEXT"}, {"defKey": "double", "defName": "小数", "id": "52A3C541-E84A-4FC8-8BE9-CC2C58324198", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "DECIMAL", "72C35736-1226-4B9F-8497-B0DFF626B554": "DECIMAL", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "DECIMAL", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "NUMERIC", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "DECIMAL", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "DECIMAL", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "NUMERIC", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "NUMERIC", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "DOUBLE", "5B3D490C-72F5-4988-9865-85FBE92F282A": "Double", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "decimal", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "REAL"}, {"defKey": "int", "defName": "整数", "id": "131E85AB-B7D0-46D9-B5BF-6F49F43C2BA7", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "INT", "72C35736-1226-4B9F-8497-B0DFF626B554": "INT", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "INT", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "INTEGER", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "INT", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "INTEGER", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "INTEGER", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "INT4", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "INT", "5B3D490C-72F5-4988-9865-85FBE92F282A": "Integer", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "int", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "INTEGER"}, {"defKey": "bit", "defName": "比特", "id": "12716C00-592A-4AEE-90E4-11A66553C02D", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "BIT", "72C35736-1226-4B9F-8497-B0DFF626B554": "BIT", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "BIT", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "BIT", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "BIT", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "BIT", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "BIT", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "BIT", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "BOOLEAN", "5B3D490C-72F5-4988-9865-85FBE92F282A": "boolean", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "Boolean", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "NONE"}, {"defKey": "largeText", "defName": "大文本", "id": "347D69A2-CC7A-4E78-BC1C-76B7960DFF4C", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "TEXT", "72C35736-1226-4B9F-8497-B0DFF626B554": "CLOB", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "TEXT", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "TEXT", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "CLOB", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "CLOB", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "TEXT", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "TEXT", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "STRING", "5B3D490C-72F5-4988-9865-85FBE92F282A": "String", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "string", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "TEXT"}, {"defKey": "longText", "defName": "长文本", "id": "2FD12880-8A20-4C0E-BF2B-FAE5BF0820B3", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "LONGTEXT", "72C35736-1226-4B9F-8497-B0DFF626B554": "LONGTEXT", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "TEXT", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "TEXT", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "CLOB", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "CLOB", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "TEXT", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "TEXT", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "STRING", "5B3D490C-72F5-4988-9865-85FBE92F282A": "String", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "string", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "TEXT"}, {"defKey": "bytes", "defName": "二进制", "id": "BF534821-827B-4CEF-8987-D30339D78216", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "BLOB", "72C35736-1226-4B9F-8497-B0DFF626B554": "BLOB", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "VARBINARY", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "BYTEA", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "BLOB", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "BLOB", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "BYTEA", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "BYTEA", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "BINARY", "5B3D490C-72F5-4988-9865-85FBE92F282A": "byte[]", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "binary", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "NONE"}, {"defKey": "date", "defName": "日期", "id": "A419884C-1291-4037-9CBD-FF03DCA32BFB", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "DATETIME", "72C35736-1226-4B9F-8497-B0DFF626B554": "DATE", "DB846F60-1156-40D5-9AB8-E69CFF5D5CED": "DATETIME", "BAFDCB4C-0847-49F3-BF22-BE20D7618A8A": "DATE", "BF8B67BC-0E09-43E8-8C9F-CDFBADE53402": "DATE", "3A4FDB50-CF5B-422F-9E8B-4D5481172952": "DATE", "180F87E7-665F-4D49-9F83-96BD0B3043A9": "DATE", "AB97A1B2-40A8-420F-AD6E-16AC1D395D44": "DATE", "7136E36E-50DA-49E1-BA46-E97AFFDAEFAF": "DATETIME", "5B3D490C-72F5-4988-9865-85FBE92F282A": "Date", "9DF2D24D-12BA-45F1-84B3-1DD5C5C29AEB": "DateTime", "B3C2CFAD-8E57-40BF-AB42-9278F4D5980A": "NUMERIC"}, {"defKey": "TIMESTAMP", "defName": "时间戳", "id": "02C2340B-02BD-4EA5-AD99-2931B5970069", "52CA5BFD-BF53-47B2-8C63-4127688BC626": "TIMESTAMP"}]}, "domains": [{"defKey": "UID", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 36, "scale": "", "uiHint": "", "id": "F8B0E91C-E1F3-42E6-89BD-F562E5464168"}, {"defKey": "NameOrTitle", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 150, "scale": "", "uiHint": "", "id": "20B73262-C803-47D0-81D1-C3BDE63BDACF"}, {"defKey": "Code", "defName": "Code", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 50, "scale": "", "uiHint": "", "id": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F"}, {"defKey": "Enum", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 15, "scale": "", "uiHint": "", "id": "8E09B4F4-25D5-4231-A964-52727B0DB5FD"}, {"defKey": "YesOrNo", "defName": "", "applyFor": "12716C00-592A-4AEE-90E4-11A66553C02D", "len": "", "scale": "", "uiHint": "", "id": "01592BEA-E3CA-4086-B489-A969D611A534"}, {"defKey": "NoOrYes", "defName": "", "applyFor": "12716C00-592A-4AEE-90E4-11A66553C02D", "len": "", "scale": "", "uiHint": "", "id": "7BF07192-4609-4441-8D68-CE0CE52843B9"}, {"defKey": "SortOrder", "defName": "", "applyFor": "131E85AB-B7D0-46D9-B5BF-6F49F43C2BA7", "len": "", "scale": "", "uiHint": "", "id": "CBD62BBF-5707-41C2-B736-BED0A6FF2598"}, {"defKey": "Timestamp", "defName": "", "applyFor": "02C2340B-02BD-4EA5-AD99-2931B5970069", "len": "", "scale": "", "uiHint": "", "id": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE"}, {"defKey": "DateTime", "defName": "", "applyFor": "A419884C-1291-4037-9CBD-FF03DCA32BFB", "len": "", "scale": "", "uiHint": "", "id": "C0B9E426-4776-44F4-9176-75E521BEACEC"}, {"defKey": "Url", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 350, "scale": "", "uiHint": "", "id": "4071F174-D871-44CB-9E57-C608DE0557E2"}, {"defKey": "OwnerId", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 50, "scale": "", "uiHint": "", "id": "9DF88AF5-9144-42F5-9556-51DC56CDF4CC"}, {"defKey": "Summary", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 300, "scale": "", "uiHint": "", "id": "B374D108-3369-4E2B-AEA0-90723460F220"}, {"defKey": "Description", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 2000, "scale": "", "uiHint": "", "id": "10CCE9B7-0117-4E03-ACBF-9B4A8141C4A4"}, {"defKey": "TenantID", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 60, "scale": "", "uiHint": "", "id": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6"}, {"defKey": "JsonMin", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 100, "scale": "", "uiHint": "", "id": "23D2184A-78E3-4012-83D5-8B985719D61C"}, {"defKey": "JsonTiny", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 1000, "scale": "", "uiHint": "", "id": "161C6A82-2931-4760-8765-EEEBB8A63ACA"}, {"defKey": "JsonSmall", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 2000, "scale": "", "uiHint": "", "id": "672DF2CF-93EC-4993-B0B4-50D9A97D6143"}, {"defKey": "JsonMedium", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 4000, "scale": "", "uiHint": "", "id": "FE6080FE-A5EE-4A45-923D-3CAD92CD7107"}, {"defKey": "JsonLarge", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 10000, "scale": "", "uiHint": "", "id": "44D2086C-5D81-4BFB-BDB6-0F1ADB28C797"}, {"defKey": "JsonBig", "defName": "", "applyFor": "347D69A2-CC7A-4E78-BC1C-76B7960DFF4C", "len": "", "scale": "", "uiHint": "", "id": "33048F09-5F71-49F0-96A8-6D67F54981A4"}, {"defKey": "JsonFile", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 500, "scale": "", "uiHint": "", "id": "0F18255B-5DEC-4B78-962E-73CDB287230F"}, {"defKey": "LongUID", "defName": "", "applyFor": "E3AB5D3E-2CFD-4097-A0CF-34B9B1739854", "len": 50, "scale": "", "uiHint": "", "id": "814A799A-30CD-47E6-9A94-D5F42A611E33"}], "diagrams": [{"defKey": "《数据库设计规范》", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "ff1679aa-daf1-4302-9bf7-3d34a83d79a6", "shape": "edit-node", "position": {"x": -110.00000000000114, "y": 20.000000000001876}, "label": "# 数据库设计规范\n\n- 命名规范\n  * 表名和字段名必须包含下划线`_`，多个单词用`_`连接，一个单词以`_`结尾，如`user_`，`user_org`\n  * 索引名格式：`{prefix}_{table}_{name}`，普通索引前缀`ix_`，唯一索引前缀`uk_`\n  * boolean类型字段不用`is_`前缀\n\n- 数据字典\n  * 枚举值用大写字母缩写表示，如`N`代表`Normal`\n\n- 名称和标题\n  * 标题字段名`title`，名称字段名`name`\n  * 仅用于显示用`title`，名称/名字用`name`", "fontColor": "#fcf1f1", "fillColor": "#051946", "size": {"width": 600, "height": 340}, "ports": {"groups": {"in": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "left"}}, "out": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "right"}}, "top": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "top"}}, "bottom": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "bottom"}}}, "items": [{"group": "in", "id": "in"}, {"group": "in", "id": "in2"}, {"group": "in", "id": "in3"}, {"group": "out", "id": "out"}, {"group": "out", "id": "out2"}, {"group": "out", "id": "out3"}, {"group": "top", "id": "top"}, {"group": "top", "id": "top2"}, {"group": "top", "id": "top3"}, {"group": "bottom", "id": "bottom"}, {"group": "bottom", "id": "bottom2"}, {"group": "bottom", "id": "bottom3"}]}}]}, "id": "0C22DF78-55FC-47B8-8B73-1EF2B480DFF7"}, {"defKey": "服务市场", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "a8bd162c-5326-4cb8-b0c0-123a6e09c537", "shape": "table", "position": {"x": 280, "y": -468}, "count": 0, "originKey": "DB8F595E-8D02-4047-BB3E-A2F4817CE595"}, {"id": "c32bb041-b66a-441e-8133-d9928d753f27", "shape": "table", "position": {"x": -443, "y": -30}, "count": 0, "originKey": "09C4AC33-F3D4-447B-B0AB-8FB1B0824D89"}, {"id": "2cd41339-da24-4982-8e70-8ab1595aa483", "shape": "table", "position": {"x": -74, "y": -340}, "count": 0, "originKey": "2A14CE16-0C3D-4EC9-B2D3-538BA13B44B7"}, {"id": "b6b598c8-0698-4ed8-b28c-1085a52d9f87", "shape": "table", "position": {"x": -443, "y": -330}, "count": 0, "originKey": "CDD515F6-9B4B-42D1-9760-B61B94CE2D02"}, {"id": "32a858c1-15d3-47bb-b1a6-56c8714cd73f", "shape": "table", "position": {"x": -455, "y": -957}, "count": 0, "originKey": "A77A1FC7-B1EE-4A08-8C3B-319760E46E40"}, {"id": "edf07f7f-e6a6-411a-b99c-e1829f3dc62c", "shape": "table", "position": {"x": -872, "y": -880}, "count": 0, "originKey": "0A0D4C7E-8E0B-475A-B8DC-6854EAD5C9F9"}, {"id": "75d8caf4-98d4-4d45-8ddb-f8bfcb8c692c", "shape": "table", "position": {"x": -870, "y": -614}, "count": 0, "originKey": "E3B51382-8A00-4E43-8B3E-43C145DFF7DF"}, {"id": "46736dce-0242-47d1-baee-4f2a7922864a", "shape": "table", "position": {"x": -870, "y": -299}, "count": 0, "originKey": "6EBC9AD1-FA00-4F59-BCF7-1C8E569366A4"}, {"id": "4cafbc4d-29f0-431e-b6c1-fa6d58a5a4f5", "shape": "table", "position": {"x": -74, "y": -614}, "count": 0, "originKey": "430F0056-364B-4001-839D-8EF6B81D7F0C"}, {"id": "8701921d-f28d-4bf1-b14d-1ee8c3b62de6", "shape": "erdRelation", "source": {"cell": "a8bd162c-5326-4cb8-b0c0-123a6e09c537", "port": "6A8985CA-6906-42FE-90DA-111B28BCB767%in"}, "target": {"cell": "2cd41339-da24-4982-8e70-8ab1595aa483", "port": "7D2AF78D-FDDE-4019-B249-0E872CEBF200%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "34311c9a-25e5-475e-9052-40a5f483dc35", "shape": "erdRelation", "source": {"cell": "4cafbc4d-29f0-431e-b6c1-fa6d58a5a4f5", "port": "5262215A-A8C3-4C6B-A0B0-64335BC0CEE0%in"}, "target": {"cell": "d51a0701-fcb3-4441-ac69-80f4d41ed119", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "8d79934f-5c8d-42fd-a5bb-f2f21eb5f40e", "shape": "erdRelation", "source": {"cell": "b6b598c8-0698-4ed8-b28c-1085a52d9f87", "port": "72ECA08F-A7E1-4AD7-8E79-047D893A65D2%out"}, "target": {"cell": "2cd41339-da24-4982-8e70-8ab1595aa483", "port": "7D2AF78D-FDDE-4019-B249-0E872CEBF200%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "25c3ecc9-1b3b-4a48-8587-379c77119940", "shape": "erdRelation", "source": {"cell": "b6b598c8-0698-4ed8-b28c-1085a52d9f87", "port": "A0CBEBF4-2C9B-4116-AFCB-2572C09211CA%in"}, "target": {"cell": "d51a0701-fcb3-4441-ac69-80f4d41ed119", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "916e5364-74f9-4f7a-b3ed-6129f5ce0195", "shape": "erdRelation", "source": {"cell": "4cafbc4d-29f0-431e-b6c1-fa6d58a5a4f5", "port": "DCE0F9D1-8A13-4FE8-8B69-F9BAD181046D%in"}, "target": {"cell": "2cd41339-da24-4982-8e70-8ab1595aa483", "port": "7D2AF78D-FDDE-4019-B249-0E872CEBF200%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "245bfe3c-e9db-45e9-abf8-5337ea9bf9d8", "shape": "erdRelation", "source": {"cell": "4cafbc4d-29f0-431e-b6c1-fa6d58a5a4f5", "port": "FB2AC934-601E-4E11-8F95-A3EDE1635C70%out"}, "target": {"cell": "a8bd162c-5326-4cb8-b0c0-123a6e09c537", "port": "6C461718-D69C-4BD8-B436-E7AC95986F16%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "b96ffb65-aedb-4916-b6b5-2a05e449674c", "shape": "erdRelation", "source": {"cell": "b6b598c8-0698-4ed8-b28c-1085a52d9f87", "port": "B1814F2E-D6FB-455F-AA7D-F08D31EE710B%in"}, "target": {"cell": "c32bb041-b66a-441e-8133-d9928d753f27", "port": "480B46F7-5AA5-4211-9DAD-8FC013DD356B%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "00c5cffa-ea7b-45b4-a89c-44402ead0199", "shape": "erdRelation", "source": {"cell": "edf07f7f-e6a6-411a-b99c-e1829f3dc62c", "port": "EE5B12D9-63DD-444B-94D3-7137E99A4802%out"}, "target": {"cell": "d51a0701-fcb3-4441-ac69-80f4d41ed119", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "2e4b4c95-71bd-402a-a642-18ef2a8126c4", "shape": "erdRelation", "source": {"cell": "75d8caf4-98d4-4d45-8ddb-f8bfcb8c692c", "port": "28F9D1AA-11E4-4A73-A87B-939BCD541D5C%out"}, "target": {"cell": "d51a0701-fcb3-4441-ac69-80f4d41ed119", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "d51a0701-fcb3-4441-ac69-80f4d41ed119", "shape": "table", "position": {"x": -444, "y": -762}, "count": 0, "originKey": "D3770455-F51E-427A-9B64-5CDC9ED00C09"}]}, "id": "187A0A9E-0533-4E04-B4B1-3FBC3D76A9CF"}, {"defKey": "服务开通", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "eddca51e-e9dd-4a1f-8cda-1491725c1a23", "shape": "table", "position": {"x": 118.5, "y": 547}, "count": 0, "originKey": "B8F3B528-58AE-4559-A077-A6371E9A52DA", "fillColor": "#f8cbad"}, {"id": "99b79b21-0660-4d6f-930a-a9090d02bbba", "shape": "table", "position": {"x": 530, "y": 404}, "count": 0, "originKey": "6509DBE7-ECAB-4BF9-9046-472CC1DA9880", "fillColor": "#ffd966"}, {"id": "8d9113e0-0798-4ba3-88a2-6b41ee0db285", "shape": "table", "position": {"x": 520, "y": 60}, "count": 0, "originKey": "FCFEE11B-7260-447E-9A9F-37932260B09F", "fillColor": "#ffd966"}, {"id": "ba592da3-0e3f-460d-bb6c-2680329019d4", "shape": "edit-node-circle", "position": {"x": 86.5, "y": -99.99999999999972}, "label": "应用开通过程\n- 发起申请单\n- 审批通过后，将授权数据写入client_authz\n- 如果是跨租户申请，自动创建服务应用和服务间的订阅关系", "size": {"width": 350, "height": 110}, "ports": {"groups": {"in": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "left"}}, "out": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "right"}}, "top": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "top"}}, "bottom": {"attrs": {"fo": {"width": 8, "height": 8, "x": -4, "y": -4, "magnet": "true", "style": {"visibility": "hidden"}}}, "zIndex": 3, "position": {"name": "bottom"}}}, "items": [{"group": "in", "id": "in"}, {"group": "in", "id": "in2"}, {"group": "in", "id": "in3"}, {"group": "out", "id": "out"}, {"group": "out", "id": "out2"}, {"group": "out", "id": "out3"}, {"group": "top", "id": "top"}, {"group": "top", "id": "top2"}, {"group": "top", "id": "top3"}, {"group": "bottom", "id": "bottom"}, {"group": "bottom", "id": "bottom2"}, {"group": "bottom", "id": "bottom3"}]}}, {"id": "33269c75-e611-47af-b262-65cf49416c9f", "shape": "erdRelation", "source": {"cell": "56a57125-6100-4e59-aa07-43bd193e7691", "port": "B43B6D91-6690-4C61-AF3D-28DE7EBA3BB5%in"}, "target": {"cell": "3a3211b3-9afc-4aa7-a321-c58751bf6158", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "2611b290-f092-4fca-9716-92c0a298e6ad", "shape": "erdRelation", "source": {"cell": "56a57125-6100-4e59-aa07-43bd193e7691", "port": "CB4F638E-F534-48CE-BA96-2B03CC5C7D7D%out"}, "target": {"cell": "f4a9bfb8-6091-4d50-8f0b-4f040f0a7440", "port": "F277C7C6-052B-4C48-ACB0-080D327F3AF6%out"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "fdfc754e-6f62-4e43-b7c7-d5b94c299124", "shape": "erdRelation", "source": {"cell": "eddca51e-e9dd-4a1f-8cda-1491725c1a23", "port": "F6163162-4B74-4493-9971-3DDDDD301FF8%in"}, "target": {"cell": "3a3211b3-9afc-4aa7-a321-c58751bf6158", "port": "C8CD8908-59FB-4E44-B376-1330FE7540B1%in"}, "relation": "1:n", "fillColor": "#ACDAFC"}, {"id": "3a3211b3-9afc-4aa7-a321-c58751bf6158", "shape": "table", "position": {"x": -320, "y": 60}, "count": 0, "originKey": "D3770455-F51E-427A-9B64-5CDC9ED00C09", "fillColor": "#f8cbad"}, {"id": "f4a9bfb8-6091-4d50-8f0b-4f040f0a7440", "shape": "table", "position": {"x": 120, "y": 60}, "count": 0, "originKey": "FE7A596F-B421-43FE-9434-83F14AE1949F", "fillColor": "#f8cbad"}, {"id": "56a57125-6100-4e59-aa07-43bd193e7691", "shape": "table", "position": {"x": 118.5, "y": 240}, "count": 0, "originKey": "1AD190FA-50B1-42CD-9B3B-5B22B3DEC7F4", "fillColor": "#f8cbad"}]}, "id": "7CCB6184-967A-4400-BAF7-17CE9369AE98"}, {"defKey": "监控指标", "defName": "", "relationType": "field", "canvasData": {"cells": [{"id": "a7fb1796-e16a-40d2-9691-94050b434d33", "shape": "erdRelation", "source": {"cell": "3deaf781-f948-41c9-8d77-740f138df296", "port": "A89FFB28-A55A-45FD-B598-F23DA654D42C%in"}, "target": {"cell": "2ae199d9-3a3f-4a8f-ac4c-4c5c59ba7797", "port": "F93088EF-2FD4-4C45-B7F5-681D1223143F%out"}, "relation": "1:n", "fillColor": "#ACDAFC", "router": {"name": "manhattan"}}, {"id": "3deaf781-f948-41c9-8d77-740f138df296", "shape": "table", "position": {"x": 370, "y": -70}, "count": 0, "originKey": "FA5978FC-66FF-4888-9933-2B8AE45399C8"}, {"id": "2ae199d9-3a3f-4a8f-ac4c-4c5c59ba7797", "shape": "table", "position": {"x": -70, "y": -70}, "count": 0, "originKey": "7C3FF4BF-B4AF-46E3-ADEF-F7DC4D00BA47"}]}, "id": "578E9BF9-F88A-47B5-B6B1-8DC7CEF1E10D"}], "standardFields": [{"defKey": "base", "defName": "基础字段", "fields": [{"defKey": "id_", "defName": "主键ID", "comment": "主键ID", "type": "", "len": "", "scale": "", "primaryKey": true, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "BC6665E7-97A6-40C7-BB6D-72AD4015D0E7"}, {"defKey": "name_", "defName": "名称", "comment": "名称", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "id": "F55F8D1D-3170-4850-BBF1-08001524A076"}, {"defKey": "code_", "defName": "编码", "comment": "编码", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "0BEB5ACF-6BF4-4B20-BB81-44F128B8E78F", "id": "C7CAD1AB-7B32-4CE8-8A88-0A52390A191F"}, {"defKey": "deleted_", "defName": "是否删除", "comment": "1表示删除，0表示未删除", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "refDict": "", "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "id": "9B387A01-C469-47E7-962B-4D3B2ECDDF77"}, {"rowNo": 6, "defKey": "desc_", "defName": "描述", "comment": null, "domain": "10CCE9B7-0117-4E03-ACBF-9B4A8141C4A4", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "typeFullName": "VARCHAR(2000)", "primaryKeyName": "", "notNullName": "", "autoIncrementName": "", "refDict": "", "id": "C9B9A418-867F-45D5-99E5-3EE3BA5538E6"}, {"rowNo": 2, "defKey": "title_", "defName": "标题", "comment": null, "domain": "20B73262-C803-47D0-81D1-C3BDE63BDACF", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "typeFullName": "VARCHAR(150)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "DCB5D66D-8F5E-4B72-BF25-84E57BB31DB6"}, {"defKey": "enabled_", "defName": "是否启用", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "1", "hideInGraph": false, "refDict": "", "domain": "01592BEA-E3CA-4086-B489-A969D611A534", "id": "467F0A24-C59F-4398-9F99-047FBDDC9E6E"}, {"rowNo": 5, "defKey": "sort_order", "defName": "排序顺序", "comment": "", "domain": "CBD62BBF-5707-41C2-B736-BED0A6FF2598", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "typeFullName": "INT(11)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "85001667-D5F3-4294-848C-B31B29920972"}, {"rowNo": 2, "defKey": "summary_", "defName": "简要说明", "comment": "", "domain": "B374D108-3369-4E2B-AEA0-90723460F220", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "typeFullName": "VARCHAR(150)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "B629E3BD-A19D-4750-A860-1B3840E62581", "isStandard": true}, {"defKey": "owner_id", "defName": "所属对象ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "9DF88AF5-9144-42F5-9556-51DC56CDF4CC", "id": "B7AAD023-4F4A-4F8F-80F3-ED2B877C3A9D"}, {"defKey": "owner_type", "defName": "所属对象类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "75636DE0-BD32-416A-B5F5-524C63C35B09", "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "2C8F04B3-EB45-4877-A2AA-0ABE9FD3B3A4"}, {"rowNo": 2, "defKey": "grantee_id", "defName": "被授予者ID", "comment": "", "domain": "814A799A-30CD-47E6-9A94-D5F42A611E33", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "typeFullName": "VARCHAR(36)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "9BA66C32-FCF6-48DA-8488-C5ED577E5622", "isStandard": true}, {"defKey": "grantee_type", "defName": "被授予者类型", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "09BF9BC6-D6AB-48F1-AAD4-A2EDA02208CF", "extProps": {}, "domain": "8E09B4F4-25D5-4231-A964-52727B0DB5FD", "id": "29D6C84C-7F6E-4C2C-86B8-E61375858454"}, {"defKey": "icon", "defName": "图标", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "0F18255B-5DEC-4B78-962E-73CDB287230F", "id": "7617550C-469C-4D0D-ADBB-AC2F228CB901"}, {"rowNo": 5, "defKey": "system_", "defName": "是否内置群组", "comment": "内置群组有：所有人，企业用户组，外部用户组，非受限用户组，受限用户组，每个租户都会有默认的内置群组", "domain": "7BF07192-4609-4441-8D68-CE0CE52843B9", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "0", "hideInGraph": false, "typeFullName": "BIT(1)", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "34F420EA-CD1A-4580-92FC-76F1051F0C9B", "id": "D1817B72-9DBE-4DB0-94DA-611A1E6FCFE3"}, {"defKey": "icon_", "defName": "图标", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "extProps": {}, "domain": "0F18255B-5DEC-4B78-962E-73CDB287230F", "id": "6747BE9E-2588-4BC9-8CB3-43B2EC1F9AC8", "isStandard": true}], "id": "808EF0E9-4EE0-41B5-89B9-0055A942D808"}, {"defKey": "operation", "defName": "操作字段", "fields": [{"rowNo": 10, "defKey": "created_at", "defName": "创建时间", "comment": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "typeFullName": "DATETIME", "primaryKeyName": "", "notNullName": "√", "autoIncrementName": "", "refDict": "", "id": "EFC7F948-AE85-4BBE-8BBB-3190BC3C00B0"}, {"rowNo": 11, "defKey": "created_by", "defName": "创建人ID", "comment": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "typeFullName": "VARCHAR(36)", "primaryKeyName": "", "notNullName": "", "autoIncrementName": "", "refDict": "", "id": "F12FC3E1-D27D-46DC-A87C-3764E8E3707E"}, {"defKey": "updated_at", "defName": "更新时间", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "3358375A-0048-4AC3-8AD5-FE0A3542DFAE", "id": "C713612A-8305-4A2A-9A11-C76020B887F7"}, {"rowNo": 9, "defKey": "updated_by", "defName": "更新人ID", "comment": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "typeFullName": "VARCHAR(36)", "primaryKeyName": "", "notNullName": "", "autoIncrementName": "", "refDict": "", "id": "D5B36A9B-B294-46BE-B1EC-BF43D8972212"}], "id": "E71C1F42-4EF2-4FFC-935D-D552D78C4224"}, {"defKey": "reference", "defName": "引用字段", "fields": [{"defKey": "tenant_id", "defName": "所属租户ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": true, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "1D0CB9A2-3A0A-4103-9A78-41012E21F9E6", "id": "72B927F2-DD86-418B-9301-A574C9CE6E64"}, {"defKey": "parent_id", "defName": "上级ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "E5B3A45F-8465-4F6A-9EC6-2B7CF4CDC53F"}, {"defKey": "app_id", "defName": "所属应用ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": false, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "9949B86D-56FD-4549-81BB-879A1E3C1079"}, {"defKey": "org_id", "defName": "所属组织ID", "comment": "", "type": "", "len": "", "scale": "", "primaryKey": false, "notNull": false, "autoIncrement": false, "defaultValue": "", "hideInGraph": true, "refDict": "", "domain": "F8B0E91C-E1F3-42E6-89BD-F562E5464168", "id": "74E68683-7CB8-4ACB-9F4D-C8979D75D69D"}], "id": "FA42C9B9-61C0-441E-B8F0-BF309F648B50"}], "dbConn": [{"defKey": "2D288ADA-5083-45B3-ACA9-8B57E6AA3726", "defName": "localhost", "type": "52CA5BFD-BF53-47B2-8C63-4127688BC626", "properties": {"driver_class_name": "com.mysql.cj.jdbc.Driver", "url": "***************************/", "password": "1", "username": "root", "customer_driver": ""}}]}