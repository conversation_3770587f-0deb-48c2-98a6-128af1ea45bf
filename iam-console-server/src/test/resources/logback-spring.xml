<configuration scan="true" scanPeriod="30 seconds">
    <springProperty scope="context" name="LOG_LEVEL_METRICS" source="LOG_LEVEL_METRICS" defaultValue="INFO"/>
    <springProperty scope="context" name="LOG_LEVEL_SYNC" source="LOG_LEVEL_SYNC" defaultValue="INFO"/>
    <springProperty scope="context" name="LOG_HOME" source="LOG_HOME" defaultValue="./logs"/>
    <springProperty scope="context" name="LOG_FILE_NAME" source="LOG_FILE_NAME" defaultValue="sys-info"/>
    <!-- 日志输出格式 -->
    <springProperty scope="context" name="LOG_PATTERN" source="LOG_PATTERN" defaultValue="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %X{traceId} %-5level %logger{20} - [%method,%line] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 滚动日志输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/sys-info.log</file>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异常日志输出 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/sys-error.log</file>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/sys-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 定时任务的任务分发 -->
    <appender name="SIFT" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>jobId</key>
            <defaultValue>unknown</defaultValue>
        </discriminator>
        <sift>
            <appender name="FILE-${jobId}" class="ch.qos.logback.core.FileAppender">
                <file>${LOG_HOME}/${jobId}.log</file>
                <encoder>
                    <pattern>${LOG_PATTERN}</pattern>
                </encoder>
            </appender>
        </sift>
    </appender>

    <logger name="net.bingosoft.fuse.iam.sync" level="${LOG_LEVEL_SYNC}" additivity="false">
        <appender-ref ref="SIFT"/>
    </logger>
    <logger name="net.bingosoft.fuse.iam.metrics" level="${LOG_LEVEL_METRICS}" additivity="false">
        <appender-ref ref="SIFT"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR"/>
    </root>
</configuration>
