FRONT_CHANNEL_LOGOUT_URI: http://iamdev.gf.com.cn/iamsso/logout
oauth2:
  client-id: iam_admin_client
  client-secret: iam_admin_client
  indirect-server-url: http://iamdev.gf.com.cn/iamsso
  server-url: http://iamdev.gf.com.cn/iamsso

app:
  file:
    dir: ${APP_FILE_DIR:/root/server/iam/iam-console/app/}

spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************
    username: root
    password: user@2021
  redis:
    host: *************
    port: 6379
    timeout: 3000
    password: iamdev
    database: 6
    lettuce:
      cluster:
        refresh:
          adaptive: true
          period: 20s
      shutdown-timeout: 10000ms  # 关闭超时时间
      pool:
        max-wait: 10
        max-active: 50
        max-idle: 30
        min-idle: 5
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: IAMConsoleClusteredScheduler   # 集群名，区分同一系统的不同实例，若使用集群功能，则每一个实例都要使用相同的名字
            instanceId: AUTO
          jobStore:
            isClustered: false     # 集群模式开关

nacos:
  discovery:
    enabled: false
    auto-register: true
    server-addr: dfuse-dev.bingosoft.net:31048
    username: cse-app
    namespace: cse-app
    password: cse-app


#ACM系统接口域名地址，实现增加、删除、修改、查询用户和组
acm:
  host:
    #url: http://***********:8001/api
    url: http://acmdev.gf.com.cn/api
  ##公司部门Appkey，appSecret
  appKey: d49220b0-7efe-4e64-ab50-101e1747abaf
  appSecret: AAAABBBCCCDDD01234567890
geninfo:
  server-url: http://geninfotest.gf.com.cn
  appId: portal
ldap:
  baseDn: DC=gfdev,DC=com
job:
  url: ${JOB_URL:http://iamdev.gf.com.cn/iamjob}
  auth: ${JOB_AUTH:aWFtOjc4Lip0eQ==}
#OA重置密码接口地址
oa:
  resetpwd:
    apiUrl: http://oadev.gf.com.cn/coa/resetpass.nsf/reset?openagent
    encrypttime: 3
#AD 重置密码接口地址
ad:
  resetpwd:
    apiUrl: http://***********:8001/GFADOperateService.ashx
    secretKey: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
gfhr:
  host: ${GFHR_HOST:http://gfhruat.gf.com.cn}
  employeeBasicUrl: ${gfhr.host}/api/system/openApi/getSimpleInfoByCode
  appId: ${GFHR_APPID:IAM}
  appSecret: ${GFHR_APPSECRET:snz6YZo9gMAQqoPweyQZONSRTOz8HoiW}
