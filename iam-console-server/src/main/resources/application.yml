LOG_LEVEL: INFO
server:
  port: 8080
  servlet:
    context-path: /iamconsole
  max-http-header-size: 150000
spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: ${PROFILE:dev}
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: IAMConsoleClusteredScheduler   # 集群名，区分同一系统的不同实例，若使用集群功能，则每一个实例都要使用相同的名字
            instanceId: AUTO
          jobStore:
            isClustered: false     # 集群模式开关
api:
  multiTenant:
    enabled: false

fly:
  migration:
    enabled: false
    ddlCaching: false
  security:
    basic-auth:
      enabled: true
    ignored-urls:
      - /acm/dept
      - /acm/user
      - /acm/geninfo/user
      - /acm/geninfo/dept
      - /oa/dept
      - /oa/user
      - /actuator/health/liveness
      - /actuator/health/readiness
      - /$files/download/**
iam:
  sync:
    logRetentionDays: 7    # 同步日志保留天数，默认 7 天
    dml-log:
      tables: 'iam_group_user, iam_role_group, iam_role_org, iam_role_user, iam_user_data_item, iam_user_authz, iam_role_ref, iam_role, iam_authority_pack, iam_org, iam_user, iam_group, iam_group_user, iam_user_org, iam_tenant, iam_position, iam_user_position, iam_duty, iam_app, iam_project, comm_managing_member'
      cdc:
        enabled: false                           # 数据库 cdc(change data capture) 变化数据捕获开关
      messaging:
        type: redis                               # 消息传递中间件类型，目前仅支持redis(stream)
        producer:
          enabled: true                          # 消息传递生产者开关
        consumer:
          enabled: true                          # 消息传递消费者开关
springdoc:
  api-docs:
    enabled: false
  packages-to-scan:
    - net.bingosoft.fuse.iam.console