FRONT_CHANNEL_LOGOUT_URI: ${FRONT_CHANNEL_LOGOUT_URI:http://iam.gf.com.cn/iamsso/logout}
oauth2:
  client-id: ${OAUTH2_CLIENTID:iam_admin_client}
  client-secret: ${OAUTH2_CLIENTSECRET:iam_admin_client}
  indirect-server-url: ${OAUTH2_SERVERURL:http://iam.gf.com.cn/iamsso}
  server-url: ${OAUTH2_SERVERURL:http://iam.gf.com.cn/iamsso}

app:
  file:
    dir: ${APP_FILE_DIR:/iam-console/app/}

spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${SPRING_DATASOURCE_URL:*********************************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:portal}
    password: ${SPRING_DATASOURCE_PASSWORD:Tdsql_test_2023!}
  # redis 配置
  redis:
    cluster:
      # 集群信息
      nodes: ${SPRING_REDIS_NODES:10.128.3.63:31369,10.128.4.158:31372,10.128.3.65:31375,10.128.4.164:31378,10.128.4.163:31381,10.128.3.46:31497}
    # 数据库索引
    database: 0
    # 密码
    password: ${SPRING_REDIS_PASSWORD:3d659f71fac2208e}
    # 连接超时时间
    timeout: 10s
    lettuce:
      cluster:
        refresh:
          adaptive: true
          period: 20s
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: IAMConsoleClusteredScheduler   # 集群名，区分同一系统的不同实例，若使用集群功能，则每一个实例都要使用相同的名字
            instanceId: AUTO
          jobStore:
            isClustered: false     # 集群模式开关

nacos:
  discovery:
    enabled: false
    auto-register: true
    server-addr: dfuse-dev.bingosoft.net:31048
    username: cse-app
    namespace: cse-app
    password: cse-app

#ACM系统接口域名地址，实现增加、删除、修改、查询用户和组
acm:
  host:
    #url: http://***********:8001/api
    url: ${ACM_HOST_URL:http://acm.gf.com.cn/api}
  ##公司部门Appkey，appSecret
  appKey: ${ACM_APP_KEY:40067abc-fd73-4216-9318-6922da30521b}
  appSecret: ${ACM_APP_SECRET:AAAABBBCCCDDD01234567890}
geninfo:
  server-url: ${GENINFO_SERVER_URL:http://geninfo.gf.com.cn}
  appId: ${GENINFO_APPID:portal}
ldap:
  baseDn: DC=gfsecurities,DC=com
job:
  url: ${JOB_URL:http://iam.gf.com.cn/iamjob}
  auth: ${JOB_AUTH:aWFtOjc4Lip0eQ==}
oa:
  resetpwd:
    apiUrl: ${OaResetPwdApiUrl:http://oadev.gf.com.cn/coa/resetpass.nsf/reset?openagent}
    encrypttime: ${OaResetPwdEncrypttime:3}
#AD 重置密码接口地址
ad:
  resetpwd:
    apiUrl: ${AdResetPwdApiUrl:http://***********:8001/GFADOperateService.ashx}
    secretKey: ${AdResetPwdSecretKey:xxxxxxxxxxxxxxxxxxxxxxxxxxxx}
gfhr:
  host: ${GFHR_HOST:http://gfhr.gf.com.cn}
  employeeBasicUrl: ${gfhr.host}/api/system/openApi/getSimpleInfoByCode
  appId: ${GFHR_APPID:IAM}
  appSecret: ${GFHR_APPSECRET:snz6YZo9gMAQqoPweyQZONSRTOz8HoiW}