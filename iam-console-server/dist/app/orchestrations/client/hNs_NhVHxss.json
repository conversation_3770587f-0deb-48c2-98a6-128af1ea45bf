{"content": {"cells": [{"position": {"x": 190, "y": 70}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.Start", "nodeType": "Action.Start", "label": "开始", "data": {}, "component": "action-start", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "f05f3bda-b574-4987-9996-9c5b52e11e1c", "zIndex": 1, "_validateError": false}, {"position": {"x": 108, "y": 230}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.OpenInDialog", "nodeType": "Action.OpenInDialog", "label": "对话框打开页面", "data": {"uri": "tenant.user_admin.gf_add_user", "requestParams": [{"_uid": "64", "name": "type", "value": "${'edit'}"}, {"_uid": "106", "name": "requestId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1200", "continueOnClose": "true"}}, "component": "action-open-in-dialog", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "acb5f017-e67b-421b-b0c1-a18493bfe9c7", "zIndex": 2, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "71c5e18b-31b8-4c09-9a0a-6070bfee4a6b", "zIndex": 3, "source": {"cell": "f05f3bda-b574-4987-9996-9c5b52e11e1c", "port": "bottom"}, "target": {"cell": "3b6f8de9-5d83-41c4-ba2b-a7fd2e4796f9", "port": "top"}}, {"position": {"x": 243, "y": 150}, "size": {"width": 51, "height": 51}, "view": "vue-shape-view", "shape": "Action.Switch", "nodeType": "Action.Switch", "label": "分支", "data": {}, "component": "action-switch", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}, {"group": "bottom", "id": "port-auto58", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}]}, "id": "3b6f8de9-5d83-41c4-ba2b-a7fd2e4796f9", "zIndex": 4, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "674befbb-4f79-4fdc-9e3b-025f226c20c8", "zIndex": 5, "data": {"condition": "${tableCell.row.operationType == 'add'}"}, "source": {"cell": "3b6f8de9-5d83-41c4-ba2b-a7fd2e4796f9", "port": "left"}, "target": {"cell": "acb5f017-e67b-421b-b0c1-a18493bfe9c7", "port": "top"}}, {"position": {"x": 90, "y": 360}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.DataSourceDataReload", "nodeType": "Action.DataSourceDataReload", "label": "单数据源刷新", "data": {"dataSource": "${checkData}"}, "component": "action-data-source-data-reload", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "be4f9a56-47d3-4eb3-a93e-6c8e465af679", "zIndex": 6, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "19374eba-22d2-4f77-9677-71d080017c9a", "zIndex": 7, "source": {"cell": "acb5f017-e67b-421b-b0c1-a18493bfe9c7", "port": "bottom"}, "target": {"cell": "be4f9a56-47d3-4eb3-a93e-6c8e465af679", "port": "top"}}, {"position": {"x": 200, "y": 490}, "size": {"width": 65, "height": 25}, "view": "vue-shape-view", "shape": "Action.End", "nodeType": "Action.End", "label": "结束", "data": {}, "component": "action-end", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "1b71ac25-0678-4478-8cd5-f706047dbd11", "zIndex": 8, "_validateError": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "cab9e990-dff9-46a7-9e03-91f8bef0ca79", "zIndex": 9, "source": {"cell": "be4f9a56-47d3-4eb3-a93e-6c8e465af679", "port": "bottom"}, "target": {"cell": "1b71ac25-0678-4478-8cd5-f706047dbd11", "port": "top"}}, {"position": {"x": 255, "y": 256}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.OpenInDialog", "nodeType": "Action.OpenInDialog", "label": "对话框打开页面", "data": {"uri": "tenant.user_admin.gf_edit_user", "requestParams": [{"_uid": "64", "name": "type", "value": "${'edit'}"}, {"_uid": "141", "name": "requestId", "value": "${tableCell.row.id}"}, {"_uid": "68", "name": "userId", "value": "${tableCell.row.objectId}"}], "dialogSettings": {"width": "1200", "continueOnClose": "${true}"}}, "component": "action-open-in-dialog", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "9132da09-9d16-4fc6-b608-76f47cc262ef", "zIndex": 10, "_validateError": false, "_selected": true}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "5ff1daea-9121-4933-9b7f-753234cbcda9", "zIndex": 11, "data": {"condition": "${tableCell.row.operationType == 'update'}"}, "source": {"cell": "3b6f8de9-5d83-41c4-ba2b-a7fd2e4796f9", "port": "bottom"}, "target": {"cell": "9132da09-9d16-4fc6-b608-76f47cc262ef", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "66ce8c86-2252-4021-a95f-e0fd67c3ab39", "zIndex": 12, "source": {"cell": "9132da09-9d16-4fc6-b608-76f47cc262ef", "port": "bottom"}, "target": {"cell": "be4f9a56-47d3-4eb3-a93e-6c8e465af679", "port": "right"}}, {"position": {"x": 500, "y": 290}, "size": {"width": 135, "height": 60}, "view": "vue-shape-view", "shape": "Action.OpenInDialog", "nodeType": "Action.OpenInDialog", "label": "对话框打开页面", "data": {"uri": "tenant.user_admin.gf_batch_move_user", "requestParams": [{"_uid": "70", "name": "userNames", "value": null}, {"_uid": "71", "name": "userIds", "value": null}, {"_uid": "72", "name": "type", "value": "${'edit'}"}, {"_uid": "73", "name": "requestId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}"}}, "component": "action-open-in-dialog", "ports": {"groups": {"top": {"position": "top", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "right": {"position": "right", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "bottom": {"position": "bottom", "label": {"position": "bottom"}, "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}, "left": {"position": "left", "attrs": {"circle": {"r": 5, "magnet": true, "stroke": "#5F95FF", "strokeWidth": 1, "fill": "#fff", "style": {"visibility": "hidden"}}}}}, "items": [{"group": "top", "id": "top"}, {"group": "right", "id": "right"}, {"group": "bottom", "id": "bottom"}, {"group": "left", "id": "left"}]}, "id": "81a243dc-302d-4a9c-90b4-07fad99d1c81", "zIndex": 13, "_validateError": false, "_selected": false}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "ce8003be-dff7-4e2f-846e-b8073476ff73", "zIndex": 14, "data": {"condition": "${tableCell.row.objectType == 'users' && tableCell.row.operationType == 'move'}"}, "source": {"cell": "3b6f8de9-5d83-41c4-ba2b-a7fd2e4796f9", "port": "right"}, "target": {"cell": "81a243dc-302d-4a9c-90b4-07fad99d1c81", "port": "top"}}, {"shape": "edge", "attrs": {"line": {"stroke": "#dadada", "targetMarker": {"name": "block", "width": 12, "height": 8}}}, "id": "2e63e24b-0019-4d2f-8ea6-a19ff0e5a426", "zIndex": 15, "source": {"cell": "81a243dc-302d-4a9c-90b4-07fad99d1c81", "port": "bottom"}, "target": {"cell": "be4f9a56-47d3-4eb3-a93e-6c8e465af679", "port": "right"}}]}, "meta": {"title": ""}}