{"type": "AdaptivePage", "version": "2.0", "dataSources": [], "variables": {"form": {"type": "object", "default": {"itType": "0", "itUrl": null, "expireAt": null}, "title": "延期表单", "orderNo": 0}, "isLongTime": {"type": "boolean", "default": false, "title": "是否为长期", "orderNo": 1}}, "orchestrations": {"nlNjMcD1mGN": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "9b54a725-6d33-4ad8-b30d-c59a2f064903"}, "action-111": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "112", "name": "form.expireAt", "value": "${'2099-12-30'}"}]}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "9b54a725-6d33-4ad8-b30d-c59a2f064903": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${isLongTime}", "description": "${isLongTime}"}, {"condition": "${!isLongTime}", "description": "${!isLongTime}"}]}, "next": ["action-111", "6b1fc841-63fb-4895-9072-591d05c5e779"]}, "6b1fc841-63fb-4895-9072-591d05c5e779": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "120", "name": "form.expireAt", "value": "${null}"}]}, "next": "end"}}}}, "events": {"on-rendered": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "87", "name": "form.userIds", "value": "${request.params.userIds}"}], "description": null}, "description": null, "id": "action-86", "activeOn": null}]}, "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "IvForm", "props": {"model": "${form}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormRadio", "designer": {"movein": false, "combo": false}, "props": {"labelName": "是否发送ACM", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "data": [{"label": "是", "value": "${1}"}, {"label": "否", "value": "${0}"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "value": "${form.acmFlag}", "defaultValue": "${1}", "requiredName": "ivu-form-item-required", "prop": "acmFlag", "labelWidth": 140}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "IT需求URL", "placeholder": "请选择IT需求", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${form.itType}", "dataDictionary": "IT_TYPE", "dataDictionaryDisabled": [], "requiredName": "ivu-form-item-required", "prop": "itType", "defaultValue": "${'0'}", "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入IT需求URL", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.itUrl}", "labelWidth": 0}}, {"type": "IvFormDatePicker", "designer": {"movein": false}, "props": {"labelName": "延期至", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "placeholder": "请选择日期", "type": "datetime", "options": {"shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": true, "transfer": true, "disabled": "${isLongTime}", "value": "${form.expireAt}", "requiredName": "ivu-form-item-required", "prop": "expireAt", "format": "yyyy-MM-dd", "multiple": false, "labelWidth": 140}}, {"type": "IvCheckboxSingle", "designer": {"moveChild": false, "movein": false}, "props": {"size": "default", "value": "${isLongTime}", "trueValue": "${true}", "falseValue": "${false}"}, "style": "margin:8px;", "events": {"on-change": "nlNjMcD1mGN"}}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "备注", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "labelWidth": 140}, "style": "", "slots": {"default": {"children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.description}"}, "style": "margin:0px;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "style": "width:100%;", "class": "", "id": "form1", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "返回", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-94"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "保存", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "description": null, "id": "action-303"}, {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchDelayUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'delay'}", "status": "${0}"}, "data": "${form}"}, "description": null, "id": "action-108"}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已保存到提交列表", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-126"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-138"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "description": null, "id": "action-300"}, {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/batchDelayUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'delay'}", "status": "${1}"}, "data": "${form}"}, "description": null, "id": "action-129"}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已提交到复核列表", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-141"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-144"}]}, "click.stop": {"actions": []}}}], "style": "text-align:right;padding-top:12px;"}], "meta": {"title": "批量延期", "platform": "pc"}}