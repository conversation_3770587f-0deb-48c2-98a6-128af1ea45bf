{"type": "AdaptivePage", "version": "2.0", "variables": {"currentMenu": {"type": "string", "default": "menu1", "title": "", "orderNo": 0}}, "dataSources": [{"id": "show", "title": "获取记录", "multiple": false, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "username": {"type": "string", "title": "登录账号"}, "type": {"type": "string", "title": "用户类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "租户ID"}, "tenantCode": {"type": "string", "title": "租户编码"}, "updatedAt": {"type": "string", "title": "更新时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/{id}", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${request.params.userId}", "select": null}}], "events": {"on-render": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "52", "name": "currentMenu", "value": "${request.params.id}"}], "description": null}, "id": "action-51"}]}, "on-rendered": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "margin:16px;", "props": {"padding": 0, "shadow": false, "bordered": true, "disHover": false, "replace": false, "target": "_self", "append": false}, "slots": {"default": {"children": [{"type": "IvMenu", "props": {"mode": "horizontal", "theme": "light", "accordion": false, "width": "100%", "data": [{"title": "基本信息", "name": "tenant.user_admin.user_info", "icon": "", "to": "${'/tenant/lowcode/tenant.user_admin.user_info?userId=' + request.params.userId}", "replace": true, "_uniqueId": "id_195", "nodeKey": 0, "selected": false}, {"title": "所属用户组", "name": "tenant.user_admin.user_group", "icon": "", "to": "${'/tenant/lowcode/tenant.user_admin.user_group?userId=' + request.params.userId}", "replace": true, "_uniqueId": "id_196", "nodeKey": 1, "selected": false, "customIcon": "md-menu", "isBadge": false, "text": "", "dot": true, "type": "warning"}, {"title": "任职信息", "icon": "", "expand": true, "_uniqueId": "id_197", "name": "tenant.user_admin.position.user_position", "value": "value_35", "selected": false, "nodeKey": 2, "customIcon": "md-menu", "replace": true, "append": false, "isBadge": false, "text": "", "dot": true, "type": "warning", "to": "${'/tenant/lowcode/tenant.user_admin.position.user_position?userId=' + request.params.userId}"}, {"title": "组织角色", "icon": "", "expand": true, "_uniqueId": "id_198", "name": "tenant.user_admin.zuzhijiaose.zuzhijiaose", "value": "value_46", "selected": false, "nodeKey": 3, "customIcon": "md-menu", "isBadge": false, "text": "", "dot": true, "type": "warning", "to": "${'/tenant/lowcode/tenant.user_admin.zuzhijiaose.zuzhijiaose?userId=' + request.params.userId}"}, {"title": "用户设备", "icon": "", "expand": true, "_uniqueId": "id_199", "name": "tenant.user_admin.shebei.yonghushebei", "value": "value_72", "selected": false, "customIcon": "md-menu", "to": "${'/tenant/lowcode/tenant.user_admin.shebei.yonghushebei?userId=' + request.params.userId}", "isBadge": false, "text": "", "dot": true, "type": "warning", "nodeKey": 4, "hidden": "${!utils.FlyVueCore.PermissionService.hasPerm('user_device')}"}], "keyNames": {}, "activeName": "${currentMenu}"}, "class": "iam-menu"}, {"type": "SlotContainer"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "${`用户详情 — ${show.data.name}`}", "content": "管理当前用户的详细信息", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": true, "wide": false}, "style": "margin-bottom:16px;", "events": {"on-tab-change": {"actions": []}, "on-back": {"actions": [{"type": "Action.Back", "inputs": {"description": null}, "id": "action-59"}]}}}], "meta": {"title": "详情页面母版", "name": "user_detal_layout", "packageName": "tenant.user_admin"}}