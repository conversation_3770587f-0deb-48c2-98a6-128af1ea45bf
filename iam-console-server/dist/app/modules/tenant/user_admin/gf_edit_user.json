{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "userInfo", "title": "获取记录", "multiple": false, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "组织ID"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "email": {"type": "string", "title": "邮箱"}, "alias": {"type": "string", "title": "别名"}, "enabled": {"type": "boolean", "title": "是否可用"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "组织拼音"}, "py": {"type": "string", "title": "组织首字母拼音"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "dataLevel": {"type": "integer", "title": "数据级别"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "员工工号"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工类型"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "alias": {"type": "string", "title": "别名"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "idNum": {"type": "string", "title": "身份证号"}, "secretLevel": {"type": "integer", "title": "用户密级"}, "extended": {"type": "object", "title": "自定义扩展字段"}, "description": {"type": "string", "title": "描述"}, "positionSortOrder": {"type": "integer", "title": "任职中级别最高的排序号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "orgId": {"type": "string", "title": "关联组织ID"}, "system": {"type": "boolean", "title": "是否内置"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "org": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}, "childTenantId": {"type": "string", "title": "所属组织子租户值"}}, "lazy": true, "autoReload": false, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/user/extend/{id}", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"select": null, "id": "${request.params.userId}"}}], "events": {"on-rendered": "Wg2IHNqHTDC", "on-render": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "191", "name": "operateType", "value": "${type=='enaDel'?'enaDel':'update'}"}]}, "description": null, "id": "action-190"}]}, "on-destroy": {"actions": []}}, "variables": {"form": {"type": "object", "default": {"orgId": null, "restricted": false, "mobile": null, "sortOrder": 100, "operationType": "add", "staffType": "OA,AD,EMAIL", "type": "N"}, "title": "form", "orderNo": 0}, "sendMsg": {"type": "boolean", "default": false, "title": "sendMsg", "orderNo": 1}, "notifyType": {"type": "string", "default": "sms", "title": "notifyType", "orderNo": 2}, "staffTypeChoose": {"type": "number", "default": 0, "title": "选择的用户类型", "orderNo": 3}, "enableCapacity": {"type": "boolean", "default": false, "title": "enableCapacity", "orderNo": 4}, "enableDisableOA": {"type": "boolean", "default": false, "title": "禁止访问OA是否可填写", "orderNo": 5}, "enableCompany": {"type": "boolean", "default": false, "title": "公司字段是否可填写", "orderNo": 6}, "lastStaffTypeStatus": {"type": "array", "title": "上一次的用户类型", "orderNo": 7}, "type": {"type": "string", "default": "${request.params.type}", "source": "request", "optionSet": null, "entity": null, "title": "更新 | 删除后启用的更新 | 编辑草稿", "orderNo": 8}, "longTime": {"type": "boolean", "default": false, "title": "是否永久", "orderNo": 9}, "requestId": {"type": "string", "default": "${request.params.requestId}", "source": "request", "optionSet": null, "entity": null, "title": "草稿编辑的复核请求id", "orderNo": 10}, "editUserInfo": {"type": "object", "title": "草稿中的用户信息", "orderNo": 11}, "orgId": {"type": "string", "default": "", "optionSet": null, "entity": null, "title": "orgId值", "orderNo": 12}, "orgReturnInfo": {"type": "object", "title": "主部门信息", "orderNo": 13}, "otherOrgInfoReturn": {"type": "object", "title": "返回的组织信息", "orderNo": 14}, "sendAcmFlag": {"type": "string", "default": "", "title": "是否发送acm（编辑草稿使用）", "orderNo": 15}, "operateType": {"type": "string", "default": "", "title": "启用注销 | 编辑", "orderNo": 16}, "isHK": {"type": "boolean", "default": false, "title": "是否香港用户", "orderNo": 17}, "erpInfo": {"type": "object", "title": "智慧人力数据", "orderNo": 18}, "otherSortInfos": {"type": "object", "title": "其余组织信息保存", "orderNo": 19}}, "functions": {"transformed": false, "script": "export function setAcmFlag() {\r\n  let acmId = renderContext.dataSources.userInfo.data.acmUserId;\r\n  if (acmId == null || acmId == '') renderContext.dataSources.userInfo.data.acmFlag = 0;\r\n  else renderContext.dataSources.userInfo.data.acmFlag = 1\r\n}\r\nexport function filter() {\r\n  if (renderContext.dataSources.userInfo.data.iamUserOtherOrg != null)\r\n    renderContext.dataSources.userInfo.data.iamUserOtherOrg = renderContext.dataSources.userInfo.data.iamUserOtherOrg.filter(v => v.orgId != '');\r\n}\r\nexport function setErpId() {\r\n\r\n  let erpId = renderContext.variables.erpInfo.userCode;\r\n  console.log(erpId, \"erpId\")\r\n  if (erpId != null || erpId != '') renderContext.dataSources.userInfo.data.erpId = erpId;\r\n}\r\nexport function setErpCode() {\r\n\r\n  let erpCode = renderContext.variables.erpInfo.userCode;\r\n  console.log(erpCode, \"erpCode\")\r\n  if (erpCode != null || erpCode != '') renderContext.dataSources.userInfo.data.code = erpCode;\r\n}\r\n\r\nexport function validatePass(rule, value, callback) {\r\n  if (value !== renderContext.variables.form.password) {\r\n    callback(new Error('两次密码输入不一致'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\nexport function setorgReturnInfo() {\r\n  renderContext.variables.orgReturnInfo = renderContext.dataSources.userInfo.data.iamUserOrg;\r\n}\r\nexport function setotherOrgsReturnInfo() {\r\n  renderContext.variables.otherSortInfos = renderContext.dataSources.userInfo.data.iamUserOtherOrg;\r\n  console.log(renderContext.variables.otherSortInfos, \"otherOrgInfo\")\r\n}\r\nexport function setOrgInfo() {\r\n  let orgInfoNew = renderContext.variables.orgReturnInfo;\r\n  renderContext.dataSources.userInfo.data.iamUserOrg.sortOptions = orgInfoNew.sortOptions\r\n  renderContext.dataSources.userInfo.data.iamUserOrg.sortUserId = orgInfoNew.sortUserId\r\n}\r\n\r\nexport function setOtherOrgInfo() {\r\n  let orgInfoNew = renderContext.variables.otherOrgInfoReturn;\r\n  console.log(orgInfoNew, \"orgInfoNew\")\r\n  let orgId = orgInfoNew.orgId;\r\n  renderContext.dataSources.userInfo.data.iamUserOtherOrg.forEach(function (item, index, arr) {\r\n    if (item.orgId == orgId) {\r\n      item.sortOptions = orgInfoNew.sortOptions\r\n      item.sortUserId = orgInfoNew.sortUserId\r\n    }\r\n  })\r\n}\r\nexport function validatePinyin(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  console.log(staffTypeChoose, \"staffTypeChoose\")\r\n  if (!value && ((staffTypeChoose == 2) || (staffTypeChoose == 3) || (staffTypeChoose == 4) || (staffTypeChoose == 5) || (staffTypeChoose == 6) || (staffTypeChoose == 7))) {\r\n    callback(new Error('请输入全拼'));\r\n  } else {\r\n    if (value) {\r\n      if (!/^[A-Za-z]+$/.test(value)) {\r\n        callback(new Error('请输入拼音'))\r\n      }\r\n      else callback();\r\n    } else callback();\r\n  }\r\n}\r\n\r\nexport function setUserInfoData() {\r\n  console.log(\"编辑草稿\")\r\n  let userInfo = renderContext.variables.editUserInfo\r\n  let acmFlag = renderContext.variables.sendAcmFlag\r\n  renderContext.dataSources.userInfo.data = userInfo\r\n  renderContext.dataSources.userInfo.data.acmFlag = acmFlag.value\r\n  console.log(renderContext.dataSources.userInfo.data, \"草稿编辑后数据\")\r\n}\r\n\r\nexport function setExpire() {\r\n  let longTime = renderContext.variables.longTime.value\r\n  if (longTime) renderContext.dataSources.userInfo.data.expiredAt = '2099-12-30 00:00:00'\r\n  else renderContext.dataSources.userInfo.data.expiredAt = null\r\n}\r\nexport function setEnable() {\r\n  if (renderContext.dataSources.userInfo.data.enableDate != null) {\r\n    renderContext.dataSources.userInfo.data.enableDate = utils.dayjs(renderContext.dataSources.userInfo.data.enableDate).format('YYYY-MM-DD')\r\n  }\r\n}\r\n\r\nexport function validateEmail(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && ((staffTypeChoose == 1) || (staffTypeChoose == 3) || (staffTypeChoose == 4) || (staffTypeChoose == 5) || (staffTypeChoose == 6) || (staffTypeChoose == 7))) {\r\n    callback(new Error('请输入主邮箱地址'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\n\r\nexport function setItType() {\r\n  renderContext.dataSources.userInfo.data.itType = '0'\r\n}\r\n\r\nexport function setLastStaffType() {\r\n  // 初始化设置上一次的员工类型\r\n  renderContext.variables.lastStaffTypeStatus.value = renderContext.dataSources.userInfo.data.staffType\r\n}\r\n\r\nexport function choose() {\r\n  let staffType = renderContext.dataSources.userInfo.data.staffType\r\n  let lastStaffType = renderContext.variables.lastStaffTypeStatus.value\r\n\r\n  let ein = staffType.indexOf(\"mailbox\"); //ein -> email index now :email 现在的下标\r\n  let ain = staffType.indexOf(\"AD\");\r\n  let eil = lastStaffType.indexOf(\"mailbox\");// eil-> email index last:email 上次的下标\r\n  let ail = lastStaffType.indexOf(\"AD\");\r\n\r\n  if (eil != -1 && ail != -1 && ein != -1 && ain == -1) {\r\n    // 取消AD，邮箱跟着取消 原状态：AD 邮箱     现状态：邮箱\r\n    renderContext.dataSources.userInfo.data.staffType.splice(ein, 1);\r\n  } else if (eil == -1 && ail == -1 && ein != -1 && ain == -1) {\r\n    // 点击邮箱，一定联动AD 原状态：都 无 现状态 邮箱\r\n    renderContext.dataSources.userInfo.data.staffType.push(\"AD\");\r\n  }\r\n\r\n  let hin = staffType.indexOf(\"hk\");\r\n  let oin = staffType.indexOf(\"OA\");\r\n  let hil = lastStaffType.indexOf(\"hk\");\r\n  let oil = lastStaffType.indexOf(\"OA\");\r\n  if (hil != -1 && oil != -1 && hin != -1 && oin == -1) {\r\n    // 取消oa，hk也会取消 原状态：OA,HK 现状态：HK\r\n    renderContext.dataSources.userInfo.data.staffType.splice(hin, 1);\r\n  } else if (hil == -1 && oil == -1 && hin != -1 && oin == -1) {\r\n    // 点击HK，一定联动OA 原状态 都无 现状态：hk\r\n    renderContext.dataSources.userInfo.data.staffType.push(\"OA\");\r\n  }\r\n\r\n  // 如果选择了香港用户，取消邮箱的时候，ad也会取消\r\n  // 前状态 oa ad email hk 现状态 oa ad hk \r\n  // 动作：移除ad\r\n  if (oil != -1 && ail != -1 && eil != -1 && hil != -1\r\n    && oin != -1 && ain != -1 && hin != -1 && ein == -1) {\r\n    renderContext.dataSources.userInfo.data.staffType.splice(ain, 1);\r\n  }\r\n  // 如果选择了香港用户，勾选ad的时候，邮箱也会添加\r\n  // 前状态 oa  hk 现状态 oa ad hk \r\n  // 动作：添加email\r\n  else if (oil != -1 && ail == -1 && eil == -1 && hil != -1\r\n    && oin != -1 && ain != -1 && hin != -1 && ein == -1) {\r\n    renderContext.dataSources.userInfo.data.staffType.push(\"mailbox\");\r\n  }\r\n  // 保存上一次状态，用于下一次判断\r\n  saveLastStatus();\r\n  // 如果为香港用户\r\n  if (staffType.indexOf(\"hk\") != -1)\r\n    renderContext.variables.isHK = true\r\n  else renderContext.variables.isHK = false\r\n  if (staffType.indexOf(\"OA\") != -1 && staffType.indexOf(\"AD\") != -1 && staffType.indexOf(\"mailbox\") != -1 && staffType.indexOf(\"hk\") != -1)\r\n    return 7;\r\n  else if (staffType.indexOf(\"OA\") != -1 && staffType.indexOf(\"AD\") != -1 && staffType.indexOf(\"mailbox\") != -1)\r\n    return 4;\r\n  else if (staffType.indexOf(\"OA\") != -1 && staffType.indexOf(\"AD\") != -1)\r\n    return 3;\r\n  else if (staffType.indexOf(\"AD\") != -1 && staffType.indexOf(\"mailbox\") != -1)\r\n    return 5;\r\n  else if (staffType.indexOf(\"OA\") != -1 && staffType.indexOf(\"hk\") != -1)\r\n    return 6;\r\n  else if (staffType.indexOf(\"OA\") != -1)\r\n    return 1;\r\n  else if (staffType.indexOf(\"AD\") != -1)\r\n    return 2;\r\n  else return 0;\r\n}\r\n\r\n\r\nfunction saveLastStatus() {\r\n  // 保存上次状态\r\n  renderContext.variables.lastStaffTypeStatus.value = renderContext.dataSources.userInfo.data.staffType\r\n}\r\n\r\nexport function staffTypetoArray() {\r\n  // 获取用户的staffType是字符串形式，需要改成数组\r\n  let staffType = renderContext.dataSources.userInfo.data.staffType;\r\n  console.log(staffType, \"staffType\")\r\n  if (!(staffType instanceof Array))\r\n    renderContext.dataSources.userInfo.data.staffType = staffType.split('|');\r\n}\r\n\r\nexport function validateInternet(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && (staffTypeChoose == 1 || staffTypeChoose == 3 || staffTypeChoose == 4 || staffTypeChoose == 5)) {\r\n    callback(new Error('请输入因特网地址'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\n\r\nexport function validateEmployeeRoleName(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && (staffTypeChoose == 1 || staffTypeChoose == 2 || staffTypeChoose == 3 || staffTypeChoose == 4 || staffTypeChoose == 5)) {\r\n    callback(new Error('请选择角色'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\n\r\nexport function validateEnableDate(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && (staffTypeChoose == 1 || staffTypeChoose == 2 || staffTypeChoose == 4 || staffTypeChoose == 5)) {\r\n    callback(new Error('请输入生效时间'));\r\n  } else if (staffTypeChoose == 5 || staffTypeChoose == 2 || staffTypeChoose == 4) {\r\n    if (value > renderContext.dataSources.userInfo.data.expiredAt) {\r\n      callback(new Error('失效时间不能早于生效时间'));\r\n    } else callback();\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\nexport function validateExpireDate(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && (staffTypeChoose == 5 || staffTypeChoose == 2 || staffTypeChoose == 4)) {\r\n    callback(new Error('请输入失效时间'));\r\n  } else if (staffTypeChoose == 5 || staffTypeChoose == 2 || staffTypeChoose == 4) {\r\n    if (value < renderContext.dataSources.userInfo.data.enableDate) {\r\n      callback(new Error('失效时间不能早于生效时间'));\r\n    } else callback();\r\n  } else callback();\r\n}\r\n\r\n\r\nexport function validateManager(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && staffTypeChoose == 1) {\r\n    callback(new Error('请输入管理者'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\n\r\nexport function validateName(rule, value, callback) {\r\n  var orgId = renderContext.dataSources.userInfo.data.iamUserOrg == null ? '' : renderContext.dataSources.userInfo.data.iamUserOrg.orgId;\r\n  var userId = renderContext.dataSources.userInfo.data.id;\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  if (!value) {\r\n    callback(new Error('请输入用户名'))\r\n    return\r\n  }\r\n  if (orgId != null && orgId != '') {\r\n    http.get('/api/tenant/user/extend/orgExist', { params: { name: `${value}`, orgId: `${orgId}`, id: `${userId}`, } })\r\n      .then(res => {\r\n        if (res.data) {\r\n          callback(new Error('组织中该用户名重复，请修改！'))\r\n        } else {\r\n          callback()\r\n        }\r\n      })\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n\r\n\r\nexport function validatemailboxCapacity(rule, value, callback) {\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value && !(staffTypeChoose == 1 || staffTypeChoose == 2 || staffTypeChoose == 3 || staffTypeChoose == 6)) {\r\n    callback(new Error('请输入邮箱容量'));\r\n  } else {\r\n    callback();\r\n  }\r\n}\r\nexport function validateOAName(rule, value, callback) {\r\n\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  let id = renderContext.dataSources.userInfo.data.id\r\n  let staffTypeChoose = renderContext.variables.staffTypeChoose.value\r\n  if (!value) {\r\n    if (staffTypeChoose == 1 || staffTypeChoose == 4 || staffTypeChoose == 6 || staffTypeChoose == 7 || staffTypeChoose == 3)\r\n      callback(new Error('请输入OA用户名'));\r\n    else callback()\r\n  } else {\r\n    if (!/.+\\/GFZQ$/.test(value)) {\r\n      callback(new Error('请以/GFZQ结尾'));\r\n    } else {\r\n      http.get('/api/tenant/user/extend/security/manage', { params: { filters: `oaName eq ${value} and id_ ne ${id}`, kindId: 'P' } })\r\n        .then(res => {\r\n          if (res.data.length) {\r\n            callback(new Error('OA名称重复，请修改！'))\r\n          } else {\r\n            callback()\r\n            return;\r\n          }\r\n        })\r\n    }\r\n  }\r\n}\r\n\r\nexport function validateUsername(rule, value, callback) {\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  let id = renderContext.dataSources.userInfo.data.id\r\n  if (!value) {\r\n    callback(new Error('请输入登录名'))\r\n    return\r\n  }\r\n  http.get(`/api/tenant/user/extend/exist`, { params: { username: `${value}`, id: `${id}` } })\r\n    .then(res => {\r\n      if (res.data) {\r\n        callback(new Error('账号重复，请修改！'))\r\n      } else {\r\n        callback()\r\n        return;\r\n      }\r\n    })\r\n}\r\n\r\nexport function validPassword(rule, value, callback) {\r\n  let http = new utils.FlyVueCore.HttpRequest();\r\n  const { name, username, password, id } = renderContext.dataSources.userInfo.data\r\n\r\n\r\n\r\n  if (!password) {\r\n    callback();\r\n    return\r\n  }\r\n  if (!name) {\r\n    callback(new Error('请先输入用户名称'))\r\n    return\r\n  }\r\n  if (!username) {\r\n    callback(new Error('请先输入登录账号'))\r\n    return\r\n  }\r\n  if (/[\\u4e00-\\u9fa5]/.test(password)) {\r\n    callback(new Error('密码不能输入中文字符'))\r\n    return\r\n  }\r\n  http.post('/api/tenant/user/extend/password/valid', { name, username, password, id })\r\n    .then(res => {\r\n      if (!res.data.success) {\r\n        callback(new Error(res.data.ruleResults.filter(v => !v.success && v.message).map(v => v.message).join('，')))\r\n      } else {\r\n        callback()\r\n      }\r\n    })\r\n}\r\n\r\nexport function validatePhone(rule, value, callback) {\r\n  if (value) {\r\n    let http = new utils.FlyVueCore.HttpRequest();\r\n    http.get('/api/tenant/users', { params: { filters: `mobile eq ${value} ` } })\r\n      .then(res => {\r\n        if (res.data.length) {\r\n          callback(new Error('用户手机号码重复，请修改！'))\r\n        } else {\r\n          callback()\r\n        }\r\n      })\r\n  } else {\r\n    if (renderContext.variables.sendMsg.value && renderContext.variables.notifyType.value === 'sms') {\r\n      callback(new Error('请填写手机号码，将用于发送初次登录地址！'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\n}\r\nexport function buildSubmitForm(form) {\r\n  //如果外部用户没有选组织 就赋值根组织\r\n  const params = utils.lodash.cloneDeep(form)\r\n  if (params.type === 'E' && !params.orgId) {\r\n    params.orgId = utils.user.tenantId\r\n  }\r\n  // params.admin = params.adminId\r\n  delete params.expiredType\r\n  delete params.deleted\r\n  delete params.tenantCode\r\n  delete params.locked\r\n  delete params.lockedExpiredAt\r\n  delete params.pwdUpdatedAt\r\n  delete params.fullOrgName\r\n  const trimAllStringValue = Object.fromEntries(\r\n    Object.entries(form).map(([k, v]) => [k, typeof v === 'string' ? v.trim() : v])\r\n  );\r\n  return trimAllStringValue\r\n}\r\n\r\nexport function setEmail() {\r\n  let username = utils.lodash.trim(renderContext.dataSources.userInfo.data.username)\r\n  if (renderContext.variables.isHK) {\r\n    renderContext.dataSources.userInfo.data.email = username + \"@gfgroup.com.hk\";\r\n  } else {\r\n    renderContext.dataSources.userInfo.data.email = username + \"@gf.com.cn\";\r\n  }\r\n  renderContext.dataSources.userInfo.data.emailInternet = username + \"@oa.gf.com.cn\";\r\n}\r\n\r\nexport function setAdmin(item) {\r\n  // 检查item是否为对象且具有name属性\r\n  if (typeof item === 'object' && item !== null && item.name) {\r\n    renderContext.dataSources.userInfo.data.admin = item.name;\r\n  } else if (typeof item === 'string') {\r\n    // 如果item是字符串，直接赋值\r\n    renderContext.dataSources.userInfo.data.admin = item;\r\n  }\r\n}\r\n\r\n/**\r\n * 生成角色为合作方员工、AD用户，具备ERP编号、用户名为wx开头命名，邮箱格式：账号<EMAIL>.cn邮箱\r\n*/\r\nexport function generateEmail() {\r\n  // 获取当前表单数据\r\n  const form = renderContext.dataSources.userInfo.data;\r\n\r\n  //如果账号还为空直接返回\r\n  if (!form.username) {\r\n    return;\r\n  }\r\n\r\n  // 判断：角色为“合作方员工”，包含 AD，已有 erpId，且用户名以 wx 开头\r\n  const isPartnerAD =\r\n    form.employeeRoleName === '合作方员工' &&\r\n    Array.isArray(form.staffType) &&\r\n    form.staffType.includes('AD') &&\r\n    typeof form.erpId === 'string' &&\r\n    form.erpId.trim() !== '' &&\r\n    typeof form.username === 'string' &&\r\n    form.username.startsWith('wx');\r\n\r\n  if (isPartnerAD) {\r\n    // 生成新邮箱\r\n    const newEmail = utils.lodash.trim(form.username) + \"@gfpartner.com.cn\";\r\n    renderContext.dataSources.userInfo.data.email = newEmail\r\n  } else {\r\n    const newEmail = renderContext.variables.isHK ? (utils.lodash.trim(form.username) + \"@gfgroup.com.hk\") : (utils.lodash.trim(form.username) + \"@gf.com.cn\")\r\n    renderContext.dataSources.userInfo.data.email = newEmail\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * 检查是否 员工类型为驻场、公司字段自动勾上，并清空默认字段\r\n * @param {string} param1 参数\r\n*/\r\nexport function checkEmployeeRoleNameIsZhuChang() {\r\n  // 获取当前表单数据\r\n  const form = renderContext.dataSources.userInfo.data;\r\n  if (form.employeeRoleName === '供应商驻场') {\r\n    console.log('renderContext', renderContext)\r\n    renderContext.variables.enableCompany.value = true\r\n    renderContext.dataSources.userInfo.data.company = ''\r\n  } else {\r\n    renderContext.variables.enableCompany.value = false\r\n    renderContext.dataSources.userInfo.data.company = '广发证券股份有限公司'\r\n  }\r\n}"}, "orchestrations": {"gXjhWv3a-": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-26"}, "action-26": {"type": "Action.SetPageVariable", "inputs": {"variables": [], "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "iw3dpT22xc": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "8c8436ac-25ef-47aa-8ea3-5c1ff760cc1c"}, "action-70": {"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "next": "action-116"}, "action-116": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "staffTypetoArray", "params": []}, "next": "6008700f-7f22-4195-b467-45d60cc8009b"}, "action-75": {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/operationUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${operateType}", "status": "1", "objectId": "${userInfo.data.id}", "requestId": "${requestId}"}, "data": "${buildSubmitForm(userInfo.data)}", "description": null}, "next": "action-87"}, "action-87": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "更新用户请求已提交到复核列表", "duration": 1.5, "closable": false, "background": false, "description": null}, "next": "action-92"}, "action-92": {"type": "Action.CloseDialog", "inputs": {}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "0f1daba6-235c-4be7-851c-a596df937599": {"type": "Action.Http", "inputs": {"resultName": "count", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/exist", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${userInfo.data.id}", "type": "${'user'}"}}, "next": "4251b62c-f372-4ec6-b2fd-f283b2cbedfb"}, "4251b62c-f372-4ec6-b2fd-f283b2cbedfb": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${count == 0}", "description": "${count == 0}"}, {"condition": "${count > 0}", "description": "${count > 0}"}]}, "next": ["action-75", "5ea7553c-36fa-4661-b968-5d26892db495"]}, "5ea7553c-36fa-4661-b968-5d26892db495": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "该用户存在未复核的请求！", "duration": 1.5, "closable": false, "background": false}, "next": null}, "8c8436ac-25ef-47aa-8ea3-5c1ff760cc1c": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${userInfo.data.iamUserOrg!=null&&userInfo.data.iamUserOrg.orgId != null && userInfo.data.iamUserOrg.orgId != ''}", "description": "${userInfo.data.iamUserOrg!=null&&userInfo.data.iamUserOrg.orgId != null && userInfo.data.iamUserOrg.orgId != ''}"}, {"condition": "${!(userInfo.data.iamUserOrg!=null&&userInfo.data.iamUserOrg.orgId != null && userInfo.data.iamUserOrg.orgId != '')}", "description": "${!(userInfo.data.iamUserOrg!=null&&userInfo.data.iamUserOrg.orgId != null && userInfo.data.iamUserOrg.orgId != '')}"}]}, "next": ["action-70", "61ef4334-56cd-4910-93fd-419cf6e3305a"]}, "61ef4334-56cd-4910-93fd-419cf6e3305a": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "请输入主部门信息", "duration": 1.5, "closable": false, "background": false}, "next": null}, "6008700f-7f22-4195-b467-45d60cc8009b": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "filter"}, "next": "0f1daba6-235c-4be7-851c-a596df937599"}}}, "Wg2IHNqHTDC": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "ef7a1a45-d82a-43fe-95e4-5008c6e93302"}, "action-101": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userInfo}"}, "next": "9be570d6-0436-4b6c-b6c4-3ab4a586a5db"}, "action-137": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "staffTypetoArray", "params": []}, "next": "action-71"}, "action-71": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setLastStaffType", "params": []}, "next": "action-56"}, "action-56": {"type": "Action<PERSON>", "inputs": {"resultName": "data", "funcType": "page", "script": "choose", "params": [], "description": null}, "next": "action-65"}, "action-65": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "66", "name": "staffTypeChoose", "value": "${data}"}]}, "next": "action-55"}, "action-55": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setItType", "params": []}, "next": "a9e16d8a-4b8d-497d-9faa-bedd1d74933a"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "ef7a1a45-d82a-43fe-95e4-5008c6e93302": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${type != 'edit'}", "description": "${type != 'edit'}"}, {"condition": "${type == 'edit'}", "description": "${type == 'edit'}"}]}, "next": ["action-101", "7c2b603d-5cab-458f-9967-4aefd99a8515"]}, "5e0d9c79-bc36-44e6-bcef-fad7ed764e19": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "91", "name": "editUserInfo", "value": "${response.newIamUser}"}]}, "next": "3647c08d-05b6-4526-b40c-4b2822cf4fd4"}, "7c2b603d-5cab-458f-9967-4aefd99a8515": {"type": "Action.Http", "inputs": {"schema": {"properties": {"type": "object"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectStr": {"type": "string", "title": "操作对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "newIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "oldIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序，默认100"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织名称"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间 (AD|AD+EMAIL)时必填"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型 OA , AD , EMAIL , HK"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户 默认是不禁用 禁用请输入http://oa.gf.com.cn"}, "employeeRoleName": {"type": "string", "title": "角色 合作方员工partner|公共账号publicUsers|正式员工staff|营销员工sales|劳务派遣dispatch|供应商驻场supplier|实习生study"}, "phone": {"type": "string", "title": "座机"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "type": {"type": "string", "title": "类型 企业用户 N | 外部用户 E | 用户账号 A"}, "otherOrgIds": {"type": "array", "title": "其他群组ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "sortOptions": {"type": "integer", "title": "排序选项 0：排在最上  1：排在最下  2：排在谁之后"}, "sortUserId": {"type": "string", "title": "排序选择2的时候，参照的用户"}, "externalId": {"type": "string", "title": "AD-ID"}, "oaUserId": {"type": "string", "title": "OA-ID"}, "acmUserId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}, "iamUserOrg": {}, "iamUserOtherOrg": {"type": "array", "title": "其余所属组织信息"}}}, "newIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "oldIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID 创建公共群组和AD群组不需要传此参数"}, "parentOrgName": {"type": "string", "title": "上级组织名称"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号 创建AD群组不需要传此参数"}, "type": {"type": "string", "title": "组织类型 S 企业组织 V 虚拟组织"}, "kindId": {"type": "string", "title": "组织分类ID 默认组织类型 P，默认组织类型只能有一个根组织"}, "externalId": {"type": "string", "title": "外部ID AD_ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名 大写字母"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼 创建AD群组不需要传此参数"}, "businessCode": {"type": "string", "title": "业务代码 创建公共群组和AD群组不需要传此参数"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码 创建公共群组和AD群组不需要传此参数"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型 营业部：1，分公司：2,公司总部子部门：3,公司总部：4,子公司：5 创建公共群组和AD群组不需要传此参数"}, "groupType": {"type": "string", "title": "群组类型 1.部门 2，群组 创建公共群组和AD群组不需要传此参数"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型  部门群组：depGroup  公共群组：publicGroup AD群组：adGroup  "}, "member": {"type": "array", "title": "用户成员 id"}, "groupMember": {"type": "array", "title": "组织成员 id"}, "otherOrg": {"type": "array", "title": "其他群组 组织id"}, "tenantId": {"type": "string", "title": "父租户id"}, "acmOrgId": {"type": "string", "title": "是否同步ACM 0：否 1：是"}}}, "map": {"type": "object"}, "newStr": {"type": "array"}, "oldStr": {"type": "array"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getDetail", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${requestId}"}, "resultName": "response"}, "next": "5e0d9c79-bc36-44e6-bcef-fad7ed764e19"}, "bbe346fb-4597-4191-aff6-33bbafec406a": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setUserInfoData"}, "next": "action-137"}, "9be570d6-0436-4b6c-b6c4-3ab4a586a5db": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setAcmFlag"}, "next": "action-137"}, "49814898-7b73-47a2-9845-77aa199dfb8e": {"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "next": null}, "3647c08d-05b6-4526-b40c-4b2822cf4fd4": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "74", "name": "sendAcmFlag", "value": "${response.acmFlag}"}]}, "next": "bbe346fb-4597-4191-aff6-33bbafec406a"}, "a9e16d8a-4b8d-497d-9faa-bedd1d74933a": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setEnable"}, "next": "end"}}}, "LE4HspcmUc4": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "05dcc6ac-8f39-44fd-8b3d-b6e9d75960a5"}, "action-53": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setEmail", "params": [], "description": null}, "next": "0b8d8d2f-f668-4b4f-849d-1871fd5d1217"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "05dcc6ac-8f39-44fd-8b3d-b6e9d75960a5": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${staffTypeChoose!=2}", "description": "${staffTypeChoose!=2}"}, {"condition": "${staffTypeChoose==2}", "description": "${staffTypeChoose==2}"}]}, "next": ["action-53", "8864cfe7-47c9-4138-b863-5f0cbf8ff708"]}, "8864cfe7-47c9-4138-b863-5f0cbf8ff708": {"type": "Action.End", "inputs": {}, "next": null}, "0b8d8d2f-f668-4b4f-849d-1871fd5d1217": {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "generateEmail"}, "next": "end"}}}}, "style": "", "body": [{"type": "IvText", "props": {"maxLine": 0, "text": "IT服务台："}, "style": "width:100%;padding-top:12px;padding-bottom:12px;padding-left:8px;font-size:16px;"}, {"type": "IvForm", "props": {"model": "${userInfo.data}", "gutter": "0", "labelPosition": "right", "labelWidth": "100", "showMessage": true, "hideRequiredMark": false, "labelColon": false, "disabled": false}, "designer": {"lock": false}, "children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvFormCheckbox", "designer": {"movein": false}, "props": {"span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "必填", "required": true}]}, "width": "50%", "show-message": true, "size": "default", "data": "${utils.optionSet.find('staffType').items}", "keyNames": {"label": "title"}, "value": "${userInfo.data.staffType}", "dataDictionary": "staffType", "dataDictionaryDisabled": [], "requiredName": "ivu-form-item-required", "prop": "staffType"}, "style": "width:100%;", "events": {"on-change": {"actions": [{"type": "Action<PERSON>", "inputs": {"resultName": "data", "funcType": "page", "script": "choose", "params": []}, "description": null, "id": "action-210"}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "225", "name": "staffTypeChoose", "value": "${data}"}]}, "description": null, "id": "action-224"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "generateEmail", "params": []}, "description": "设置邮箱", "id": "action-288"}]}}}]}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvFormRadio", "designer": {"movein": false}, "props": {"labelName": "是否发送ACM", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "data": [{"label": "是", "value": "${1}"}, {"label": "否", "value": "${0}"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "labelWidth": 140, "value": "${userInfo.data.acmFlag}", "requiredName": "ivu-form-item-required", "prop": "acmFlag"}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}]}], "props": {"customCol": "12:12", "gutter": 16, "wrap": true}}], "style": "width:100%;"}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 24}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "border": true, "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "6", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${userInfo.data.itType}", "dataDictionary": "IT_TYPE"}, "style": ""}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.itUrl}", "labelWidth": 0}, "style": "", "class": ""}], "style": "display:flex;"}], "props": {"customCol": "12:12:12:12", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "IvText", "props": {"maxLine": 0, "text": "用户信息："}, "style": "padding-top:12px;padding-bottom:12px;padding-left:8px;width:100%;font-size:16px;"}, {"type": "IvFormInput", "props": {"labelName": "姓名", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入用户姓名", "border": true, "rules": "${validateName}", "requiredName": "ivu-form-item-required", "value": "${userInfo.data.name}", "prop": "name", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormInput", "props": {"requiredName": "ivu-form-item-required", "labelName": "OA用户名", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入OA用户名", "border": true, "rules": "${validateOAName}", "value": "${userInfo.data.oaName}", "prop": "oaName", "disabled": "${staffTypeChoose==2||staffTypeChoose==5}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${!(staffTypeChoose==2||staffTypeChoose==5)}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvFormInput", "props": {"labelName": "OA用户名", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入OA用户名", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "value": "${userInfo.data.oaName}", "prop": "oaName", "disabled": "${staffTypeChoose==2||staffTypeChoose==5}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${!utils.lodash.includes(userInfo.data.staffType,'OA')}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvFormInput", "props": {"labelName": "登录名", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入登录名", "border": true, "rules": "${validateUsername}", "value": "${userInfo.data.username}", "prop": "username", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-click": {"actions": []}, "on-change": "LE4HspcmUc4", "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}, {"type": "IvFormInput", "props": {"requiredName": "ivu-form-item-required", "labelName": "全拼", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入姓名全拼", "border": true, "rules": "${validatePinyin}", "value": "${userInfo.data.pinyin}", "disabled": "${staffTypeChoose==1}", "prop": "pinyin", "readonly": false, "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}, "style": "pinyin.label:before {\r\ncontent: '* ';\r\ncolor: red;\r\n}", "class": "", "id": "pinyin", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvFormInput", "props": {"labelName": "简拼", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入简拼", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "请输入用户简拼"}, {"type": "regex", "value": "regex", "label": "正则校验", "trigger": "blur", "pattern": "^[A-Z]+$", "message": "简拼应都为大写字母"}]}, "value": "${userInfo.data.py}", "requiredName": "ivu-form-item-required", "prop": "py", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormInput", "props": {"requiredName": "ivu-form-item-required", "labelName": "主邮箱地址", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入主邮箱地址", "border": true, "rules": "${validateEmail}", "value": "${userInfo.data.email}", "prop": "email", "disabled": "${staffTypeChoose==2}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormInput", "props": {"labelName": "因特网地址", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入因特网地址", "border": true, "rules": "${validateInternet}", "value": "${userInfo.data.emailInternet}", "disabled": "${staffTypeChoose==2}", "prop": "emailInternet", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormInput", "props": {"labelName": "其他邮箱地址", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入其他邮箱地址", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "请输入用户名称"}]}, "value": "${userInfo.data.otherEmail}", "disabled": "${staffTypeChoose==1||staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==6||staffTypeChoose==7}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "角色", "placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('EMPLOYEE_ROLE_NAME_DICT').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${userInfo.data.employeeRoleName}", "dataDictionary": "EMPLOYEE_ROLE_NAME_DICT", "dataDictionaryDisabled": [], "prop": "employeeRoleName", "labelWidth": 140, "requiredName": "ivu-form-item-required"}, "events": {"on-change": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "generateEmail", "params": []}, "description": "设置邮箱", "id": "action-270"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "checkEmployeeRoleNameIsZhuChang", "params": []}, "description": "员工类型为驻场、公司字段自动勾上，并清空默认值", "id": "action-68"}]}, "on-clear": {"actions": []}, "on-open-change": {"actions": []}, "on-query-change": {"actions": []}, "on-create": {"actions": []}, "on-select": {"actions": []}}}, {"type": "IvFormInput", "props": {"labelName": "AD显示名", "span": "8", "width": "100%", "type": "text", "size": "default", "placeholder": "输入AD显示名", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "请输入用户名称"}]}, "value": "${userInfo.data.name+'('+userInfo.data.orgName+\")\"}", "disabled": "${staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==4||staffTypeChoose==5||staffTypeChoose==7}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==4||staffTypeChoose==5||staffTypeChoose==7}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvFormDatePicker", "designer": {"movein": false}, "props": {"labelName": "生效时间", "span": "8", "rules": "${validateEnableDate}", "width": "100%", "show-message": true, "placeholder": "请选择日期", "type": "date", "options": {"disabledDate": "function anonymous(\n) {\n\n}", "shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": true, "transfer": true, "value": "${userInfo.data.enableDate}", "prop": "enableDate", "labelWidth": 140, "format": "yyyy-MM-dd"}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {"width": "33.3%"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvFormDatePicker", "designer": {"movein": false}, "props": {"labelName": "失效时间", "rules": "${validateExpireDate}", "width": "100%", "show-message": true, "placeholder": "请选择日期", "type": "datetime", "options": {"disabledDate": "function anonymous(\n) {\n\n}", "shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": false, "transfer": true, "value": "${userInfo.data.expiredAt}", "disabled": "${(staffTypeChoose==1||staffTypeChoose==6)||longTime}", "prop": "expiredAt", "labelWidth": 140, "multiple": false, "format": "yyyy-MM-dd"}, "style": "width:100%;display:block;"}], "style": "width:90%;"}, {"type": "IvCheckboxSingle", "designer": {"moveChild": false, "movein": false}, "props": {"size": "default", "value": "${longTime}", "trueValue": "${true}", "falseValue": "${false}"}, "events": {"on-change": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setExpire", "params": []}, "description": null, "id": "action-210"}, {"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "description": null, "id": "action-74"}]}}, "style": "margin-top:8px;margin-left:8px;"}], "style": "display:flex;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvFormInput", "props": {"labelName": "公司", "width": "100%", "type": "text", "size": "default", "placeholder": "请输入公司名称", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": true, "message": "请输入公司名称", "trigger": "change"}]}, "value": "${userInfo.data.company}", "disabled": "${(!enableCompany)&&(staffTypeChoose==1||staffTypeChoose==2||staffTypeChoose==3||staffTypeChoose==4||staffTypeChoose==5||staffTypeChoose==6||staffTypeChoose==7)}", "labelWidth": 140, "requiredName": "ivu-form-item-required", "prop": "company"}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}], "style": "width:90%;"}, {"type": "IvCheckboxSingle", "designer": {"moveChild": false, "movein": false}, "props": {"size": "small", "value": "${enableCompany}", "trueValue": "${true}", "falseValue": "${false}"}, "style": "margin-top:8px;margin-right:0px;margin-bottom:0px;margin-left:8px;"}], "style": "display:flex;width:33.33%;"}, {"type": "IvFormInput", "props": {"labelName": "密码", "span": "8", "width": "100%", "size": "default", "placeholder": "输入密码", "border": true, "rules": "${validPassword}", "value": "${userInfo.data.password}", "prop": "password", "type": "text", "labelWidth": 140, "password": false}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "邮箱用户服务级别", "placeholder": "请选择邮箱用户服务级别", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('EMAIL_USER_LEVEL').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${userInfo.data.emailUserServiceLevel}", "dataDictionary": "EMAIL_USER_LEVEL", "dataDictionaryDisabled": [], "labelWidth": 140}}, {"type": "SelectDown", "props": {"apiName": "/api/tenant/user/extend/all", "apiMethod": "get", "search": "key", "fieldVal": "username", "fieldLabel": "name", "value": "${userInfo.data.adminId}", "title": "管理者", "labelWidth": "140px", "col": "8", "edit": true, "labelValue": "${userInfo.data.admin}"}, "events": {"on-change": {"actions": [{"type": "Action<PERSON>", "inputs": {"resultName": "", "funcType": "page", "script": "set<PERSON>d<PERSON>", "params": [{"name": "item", "value": "${$event}"}], "description": null}, "description": null, "id": "action-75", "activeOn": null}]}}}, {"type": "IvFormInput", "props": {"labelName": "备注", "span": "8", "width": "100%", "type": "text", "size": "default", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "请输入用户名称"}]}, "value": "${userInfo.data.description}", "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}}}, {"type": "IvFormRadio", "designer": {"movein": false}, "props": {"labelName": " 门户通讯录是否隐藏", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "data": [{"label": "是", "value": "${true}"}, {"label": "否", "value": "${false}"}], "keyNames": {}, "size": "default", "buttonStyle": "default", "labelWidth": 160, "value": "${userInfo.data.oaHide}", "defaultValue": "${false}"}}, {"type": "UserSort", "props": {"col": "12", "value": "${userInfo.data.iamUserOrg}", "title": "主部门", "labelWidth": "140px", "sortStatus": "dialog"}, "events": {"on-change": {"actions": []}, "on-sort-status": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setorgReturnInfo", "params": []}, "description": null, "id": "action-71"}, {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_update_move", "requestParams": [{"_uid": "96", "name": "orgId", "value": "${$event.orgId}"}, {"_uid": "97", "name": "userId", "value": "${request.params.userId}"}, {"_uid": "148", "name": "type", "value": "${'update'}"}, {"_uid": "129", "name": "sortInfo", "value": "${orgReturnInfo}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}"}, "dialogResultName": "orgInfo", "description": null}, "description": null, "id": "action-95", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "153", "name": "orgReturnInfo", "value": "${orgInfo}"}]}, "description": null, "id": "action-152"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setOrgInfo", "params": []}, "description": null, "id": "action-161"}]}}}, {"type": "UserSort", "props": {"col": "12", "value": "${userInfo.data.iamUserOtherOrg}", "title": "其余所属组织", "labelWidth": "140px", "isMultiple": true, "sortStatus": "dialog"}, "events": {"on-change": {"actions": []}, "on-sort-status": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setotherOrgsReturnInfo", "params": [], "description": null}, "description": null, "id": "action-56", "activeOn": null}, {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.gf_user_update_move", "requestParams": [{"_uid": "58", "name": "orgId", "value": "${$event.orgId}"}, {"_uid": "59", "name": "userId", "value": "${request.params.userId}"}, {"_uid": "70", "name": "sortInfos", "value": "${otherSortInfos}"}, {"_uid": "68", "name": "type", "value": "${'update'}"}], "dialogSettings": {"width": "1000", "continueOnClose": "${true}"}, "dialogResultName": "otherOrgInfo", "description": null}, "description": null, "id": "action-57", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "74", "name": "otherOrgInfoReturn", "value": "${otherOrgInfo}"}], "description": null}, "description": null, "id": "action-73", "activeOn": null}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setOtherOrgInfo", "params": [], "description": null}, "description": null, "id": "action-101", "activeOn": null}]}}}, {"type": "UserSort", "props": {"width": "8", "col": 0}}, {"type": "IvText", "props": {"maxLine": 0, "text": "ERP属性："}, "style": "padding-top:12px;padding-bottom:12px;padding-left:8px;width:100%;font-size:16px;"}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "智慧人力绑定", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${erpInfo.chineseName}", "disabled": true, "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-click": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}, "slots": {"append": {"children": [{"type": "IvButton", "props": {"text": "选择", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.hr_user_select", "requestParams": [{"_uid": "207", "name": "searchText", "value": "${userInfo.data.name}"}], "dialogSettings": {"width": "1000", "title": "选择用户", "continueOnClose": null}, "dialogResultName": "res", "description": null}, "description": null, "id": "action-206", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "250", "name": "erpInfo", "value": "${res[0]}"}]}, "description": null, "id": "action-249"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setErpCode", "params": [], "description": null}, "description": null, "id": "action-260", "activeOn": "${erpInfo!=null&&erpInfo.userCode!=null&&(!erpInfo.userCode=='')}"}]}, "click.stop": {"actions": []}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "员工编号", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.code}", "readonly": false, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "ERP号", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.erpId}", "readonly": false, "disabled": false, "labelWidth": 140}, "events": {"on-enter": {"actions": []}, "on-click": {"actions": []}, "on-change": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "generateEmail", "params": []}, "description": "生成邮箱", "id": "action-281"}]}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "移动电话", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.mobile}", "readonly": false, "disabled": false, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "岗位", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.position}", "readonly": true, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "办公电话", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.phoneNumber}", "readonly": true, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "工作地址", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.workPlace}", "readonly": true, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "上级经理", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.manager}", "readonly": true, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "邮政编码", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.postalCode}", "readonly": true, "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "分机号", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${userInfo.data.directPhone}", "readonly": true, "disabled": true, "labelWidth": 140}}], "style": "", "class": "", "id": "form1", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"onValidate": {"actions": []}, "on-model-change": {"actions": []}}}], "header": [{"type": "IvText", "props": {"text": "修改用户信息", "showTitle": false, "maxline": 0}, "style": "font-size:16px;width:100%;padding-bottom:12px;"}], "footer": [{"type": "IvFlex", "designer": {"movein": false, "moveout": false}, "style": "", "children": [{"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "props": {"flex": "auto"}, "style": "text-align:left;"}, {"type": "IvFlexItem", "designer": {"move": false, "moveSibling": true}, "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}, "style": ""}, {"type": "IvButton", "props": {"text": "保存", "type": "primary", "size": "default", "long": false}, "events": {"click": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1"}, "description": null, "id": "action-52"}, {"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "staffTypetoArray", "params": []}, "description": null, "id": "action-106"}, {"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/operationUser", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${operateType}", "status": "0", "objectId": "${userInfo.data.id}", "requestId": "${requestId}"}, "data": "${buildSubmitForm(userInfo.data)}", "description": null}, "description": null, "id": "action-57", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "保存成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-67"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-72"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "long": false}, "events": {"click": "iw3dpT22xc", "click.stop": {"actions": []}}}]}], "props": {"mode": "horizontal", "gutter": 16}}], "meta": {"title": "编辑用户", "name": "create_user", "packageName": "tenant.user_admin"}}