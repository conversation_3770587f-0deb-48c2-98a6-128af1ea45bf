{"type": "AdaptivePage", "version": "2.0", "style": "border-style:solid;border-color:#17233d;", "variables": {"userFilters": {"type": "string", "default": "deleted ne true", "title": "用户过滤条件", "orderNo": 0}, "cellValue": {"type": "string", "default": "0", "title": "所选择的cell", "orderNo": 1}, "selectedUsers": {"type": "array", "default": [], "title": "所选择的用户列表", "orderNo": 2}, "keyword": {"type": "string", "default": "", "title": "用户查询关键字", "orderNo": 3}, "selectOrgId": {"type": "string", "default": "", "title": "所选择的组织id", "orderNo": 4}, "modal1": {"type": "boolean", "default": false, "title": "对话框1", "orderNo": 5}}, "dataSources": [{"id": "userList", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "排序"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${userFilters}", "search": "${keyword}", "expand": "${'org(name,id)'}", "joins": null, "orderby": "sortOrder,updatedAt desc", "total": "${true}", "inOrgIds": null, "orgType": null, "roleFilters": null, "expandPosition": "${true}", "kindId": ""}}, {"id": "activityUserTotal", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "排序"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${'deleted ne true'}", "search": null, "expand": null, "joins": null, "orderby": null, "total": "${true}", "inOrgIds": null, "orgType": null, "roleFilters": null, "expandPosition": null, "kindId": "${'P'}"}}, {"id": "userDeletedTotal", "title": "查询数据权限的用户", "multiple": true, "schema": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {"type": "object", "properties": {"id": {"type": "string", "title": "文件ID"}, "name": {"type": "string", "title": "文件名称"}, "size": {"type": "integer", "title": "文件大小"}, "path": {"type": "string", "title": "文件存储相对路径"}, "url": {"type": "string", "title": "获取图片的请求地址"}}}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "code": {"type": "string", "title": "组织编码"}, "sortOrder": {"type": "integer", "title": "排序"}, "name": {"type": "string", "title": "组织名称"}, "parentId": {"type": "string", "title": "上级组织ID"}, "path": {"type": "string", "title": "组织索引全路径"}, "deleted": {"type": "boolean", "title": "是否删除"}, "description": {"type": "string", "title": "描述"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部Id"}, "childTenantId": {"type": "string", "title": "子租户ID"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "parent": {}, "childTenant": {}, "properties": {"type": "object"}, "parentPathName": {"type": "string", "title": "上层组织路径名"}}}, "ref": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "avatar": {}, "username": {"type": "string", "title": "登录账号"}, "passwordStatus": {"type": "string", "title": "密码状态"}, "type": {"type": "string", "title": "用户类型"}, "userKind": {"type": "string", "title": "员工状态"}, "orgId": {"type": "string", "title": "所属组织ID"}, "refId": {"type": "string", "title": "引用用户ID"}, "restricted": {"type": "boolean", "title": "是否受限用户"}, "jobTitle": {"type": "string", "title": "员工职称"}, "deleted": {"type": "boolean", "title": "可用状态"}, "gender": {"type": "string", "title": "性别"}, "phoneNumber": {"type": "string", "title": "工作电话"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "inboundOrgId": {"type": "string", "title": "外部企业ID"}, "enabled": {"type": "boolean", "title": "是否启用账号"}, "externalId": {"type": "string", "title": "外部Id"}, "expiredAt": {"type": "string", "title": "过期时间"}, "privileged": {"type": "boolean", "title": "是否特权账号"}, "tenantId": {"type": "string", "title": "父租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "org": {}, "ref": {}, "inboundOrg": {}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}}, "inboundOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "唯一标识"}, "name": {"type": "string", "title": "名称"}, "type": {"type": "string", "title": "类型"}, "refTenantId": {"type": "string", "title": "外部租户ID"}, "description": {"type": "string", "title": "描述"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "refTenant": {}, "properties": {"type": "object"}}}, "properties": {"type": "object"}, "locked": {"type": "boolean", "title": "是否锁定"}, "lockedExpiredAt": {"type": "string", "title": "锁定过期时间"}, "pwdUpdatedAt": {"type": "string", "title": "密码修改时间"}, "fullOrgName": {"type": "string", "title": "所属组织全路径"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/security/manage", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": "${'deleted eq true'}", "search": null, "expand": null, "joins": null, "orderby": null, "total": "${true}", "inOrgIds": null, "orgType": null, "roleFilters": null, "expandPosition": null, "kindId": "P"}}], "orchestrations": {"Am1qrsnpTmd": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "065eadee-f067-43d1-8126-fbd74cdf2635"}, "065eadee-f067-43d1-8126-fbd74cdf2635": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.enabled}", "description": "${tableCell.row.enabled}"}, {"condition": "${!tableCell.row.enabled}", "description": "${!tableCell.row.enabled}"}]}, "next": ["66be9171-0bc7-451c-bee2-e2c0384a63ba", "49540e91-2f87-4d3a-8446-ca574559d6cd"]}, "66be9171-0bc7-451c-bee2-e2c0384a63ba": {"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${'确定禁用用户'+tableCell.row.name+'吗？'}", "okText": "确定", "icon": ""}, "next": "edc64515-d086-490f-8645-a43147782e0b"}, "49540e91-2f87-4d3a-8446-ca574559d6cd": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "确定提交吗？", "okText": "确定", "icon": ""}, "next": "525fe086-e832-422c-8dad-f7c2608aa065"}, "edc64515-d086-490f-8645-a43147782e0b": {"type": "Action.Http", "inputs": {"method": "PATCH", "params": {"id": "${tableCell.row.id}", "enabled": "${false}"}, "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/{id}/enabled", "processor": "ServicePathProcessor"}}, "next": "3fbe2822-d508-4c86-8423-b82c27a46908"}, "525fe086-e832-422c-8dad-f7c2608aa065": {"type": "Action.Http", "inputs": {"method": "PATCH", "params": {"id": "${tableCell.row.id}", "enabled": "${true}"}, "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/{id}/enabled", "processor": "ServicePathProcessor"}}, "next": "3fbe2822-d508-4c86-8423-b82c27a46908"}, "14a4722d-a8d9-449c-a732-6fd928585d73": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "88d6682b-1b5c-4e23-9bf2-2b583e9cab2a"}, "88d6682b-1b5c-4e23-9bf2-2b583e9cab2a": {"type": "Action.End", "inputs": {}, "next": null}, "3fbe2822-d508-4c86-8423-b82c27a46908": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "next": "14a4722d-a8d9-449c-a732-6fd928585d73"}}}, "wE1O9J069ma": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "24e1ecec-cc74-4528-a5e4-f771796d0d36"}, "24e1ecec-cc74-4528-a5e4-f771796d0d36": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.type==='N'}", "description": "${tableCell.row.type==='N'}"}, {"condition": "${tableCell.row.type==='E'}", "description": "${tableCell.row.type==='E'}"}]}, "next": ["425bc949-ef6e-4a7e-81ca-ed632ffbe222", "2aac5c94-cf34-4d6c-baef-51d2025382e2"]}, "425bc949-ef6e-4a7e-81ca-ed632ffbe222": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user"}, "next": "6d4944ee-c028-45f3-8e33-ae4178078a47"}, "2aac5c94-cf34-4d6c-baef-51d2025382e2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user_e"}, "next": "6d4944ee-c028-45f3-8e33-ae4178078a47"}, "6d4944ee-c028-45f3-8e33-ae4178078a47": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "980eeb77-2f16-48d5-8d4d-647eca1b91f3"}, "980eeb77-2f16-48d5-8d4d-647eca1b91f3": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "ac9c50bc-afd2-45e5-a1ae-a710af82e80d"}, "ac9c50bc-afd2-45e5-a1ae-a710af82e80d": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "next": "a5836082-bd70-4050-8660-76c53ba9aeae"}, "a5836082-bd70-4050-8660-76c53ba9aeae": {"type": "Action.End", "inputs": {}, "next": null}}}, "YiBUnM8cjiR": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "e6775e10-004a-42f8-8aa5-c1f801ef88c1"}, "e6775e10-004a-42f8-8aa5-c1f801ef88c1": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${$event == '0'}", "description": "${$event == '0'}"}, {"condition": "${$event == '1'}", "description": "${$event == '1'}"}, {"condition": "${$event == '3'}", "description": "${$event == '3'}"}, {"condition": "${$event == '2'}", "description": "${$event == '2'}"}, {"condition": "${$event=='4'}", "description": "${$event=='4'}"}]}, "next": ["f8fdc318-7fda-4fef-a381-671dbfc8118f", "b52ec4cb-4b3d-4e55-9d8c-1740e103cdfe", "b3bd1acb-225c-4e54-aad2-bb379e46b3e7", "c33d2ab6-b3f1-4336-ada7-2a3528dd76f1", "2f63e342-98f6-426c-872e-633b99a0491b"]}, "f8fdc318-7fda-4fef-a381-671dbfc8118f": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "314", "name": "modal1", "value": "${true}"}]}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "e5101446-5959-4385-a879-a196ba87ef6b": {"type": "Action.End", "inputs": {}, "next": null}, "b52ec4cb-4b3d-4e55-9d8c-1740e103cdfe": {"type": "Action.Confirm", "inputs": {"type": "warning", "title": "提示", "content": "${`是否确定禁用用户：${selectedUsers.filter(v=> v.id !== utils.user.userId && v.enabled).map(v=> v.name).join(',')}？禁用后，该账号将无法登录，请谨慎操作！`}", "okText": "确定", "icon": ""}, "next": "5f565fde-ad6f-4bcc-bafc-c0c7eb1b1cb3"}, "2f63e342-98f6-426c-872e-633b99a0491b": {"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${`是否确认删除用户：${selectedUsers.filter(v=> v.id !== utils.user.userId).map(v=> v.name).join(',')}？`}", "okText": "确定", "icon": ""}, "next": "8f30c6f1-df46-4ec0-a4f4-be96a5408244"}, "b3bd1acb-225c-4e54-aad2-bb379e46b3e7": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.xiaoxitongzhi", "dialogSettings": {"width": "780"}, "requestParams": [{"_uid": "70", "name": "selectedUsers", "value": "${selectedUsers.filter(v=> v.enabled)}"}]}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "5f565fde-ad6f-4bcc-bafc-c0c7eb1b1cb3": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/enabled/batch", "processor": "ServicePathProcessor"}, "method": "PATCH", "params": {"enabled": "${false}"}, "data": "${selectedUsers.filter(v=> v.id !== utils.user.userId && v.enabled).map(v=> v.id)}"}, "next": "a9d542da-0e53-4fdc-9b90-7f3e6c1a944f"}, "a9d542da-0e53-4fdc-9b90-7f3e6c1a944f": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "next": "375e651e-6ace-41e6-8019-4e22c336c44f"}, "375e651e-6ace-41e6-8019-4e22c336c44f": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "8f30c6f1-df46-4ec0-a4f4-be96a5408244": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/batch/delete", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"soft": "${cellValue == '0' ? true : false}"}, "data": "${selectedUsers.filter(v=> v.id !== utils.user.userId).map(v=> v.id)}"}, "next": "c5ea0092-ad68-480c-97a6-1d1efd8e7e5b"}, "c5ea0092-ad68-480c-97a6-1d1efd8e7e5b": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "删除成功", "duration": 1.5, "closable": false, "background": false}, "next": "1d3ff1e0-889e-418d-acf0-4fbb445a604e"}, "5e8af6e6-da9b-4c6b-a345-6d269ee837a6": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}, "5ebae9ff-7557-45f8-b2c9-1a567497a303": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "5e8af6e6-da9b-4c6b-a345-6d269ee837a6"}, "1d3ff1e0-889e-418d-acf0-4fbb445a604e": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "5ebae9ff-7557-45f8-b2c9-1a567497a303"}, "c33d2ab6-b3f1-4336-ada7-2a3528dd76f1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.copy-user", "dialogSettings": {"width": "700"}, "requestParams": [{"_uid": "100", "name": "userId", "value": "${selectedUsers[0].id}"}]}, "next": "15fb442b-9c55-4aab-a419-04a134392285"}, "15fb442b-9c55-4aab-a419-04a134392285": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "4c432d64-aa7f-4dff-b0b4-4e6b9d04b175"}, "4c432d64-aa7f-4dff-b0b4-4e6b9d04b175": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "e5101446-5959-4385-a879-a196ba87ef6b"}}}, "i_751vDdm": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "a102b029-62cd-445a-b5ca-c203225da8c0"}, "action-47": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user", "requestParams": [{"_uid": "48", "name": "userId", "value": "${tableCell.row.id}"}], "description": null}, "next": "action-96"}, "action-96": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "next": "action-100"}, "action-100": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}", "description": null}, "next": "action-104"}, "action-104": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "a102b029-62cd-445a-b5ca-c203225da8c0": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.type === 'N'}", "description": "${tableCell.row.type === 'N'}"}, {"condition": "${tableCell.row.type === 'E'}", "description": "${tableCell.row.type === 'E'}"}]}, "next": ["action-47", "a979a22e-b44c-4cde-ab77-6f0f4d52278f"]}, "a979a22e-b44c-4cde-ab77-6f0f4d52278f": {"type": "Action.OpenInDialog", "inputs": {"requestParams": [{"_uid": "73", "name": "userId", "value": "${tableCell.row.id}"}], "uri": "tenant.user_admin.enabled_user_e"}, "next": "action-96"}}}, "uNgevv3KX3g": {"actions": {"f05f3bda-b574-4987-9996-9c5b52e11e1c": {"type": "Action.Start", "inputs": {}, "next": "794ca4a6-4735-408c-bc6d-33492b9ff6d6"}, "f41119c4-c375-4c05-9935-bc8156fad90d": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user", "requestParams": [{"_uid": "74", "name": "userId", "value": "${tableCell.row.id}"}]}, "next": "322dc092-3f60-4fc5-b4e0-c2480ef750e0"}, "cb5a9b3f-2317-4c6c-a5b3-9758993ccc7c": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.enabled_user_e", "requestParams": [{"_uid": "75", "name": "userId", "value": "${tableCell.row.id}"}]}, "next": "322dc092-3f60-4fc5-b4e0-c2480ef750e0"}, "322dc092-3f60-4fc5-b4e0-c2480ef750e0": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "next": "138496e3-7994-4a4d-ae64-78141308dc76"}, "138496e3-7994-4a4d-ae64-78141308dc76": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "next": "765f45b9-e0ec-4ad2-9686-2d15802b1003"}, "765f45b9-e0ec-4ad2-9686-2d15802b1003": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "next": "6b872b07-3b96-4740-b4fb-dfe7618648a7"}, "6b872b07-3b96-4740-b4fb-dfe7618648a7": {"type": "Action.End", "inputs": {}, "next": null}, "794ca4a6-4735-408c-bc6d-33492b9ff6d6": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.type==='N'}", "description": "${tableCell.row.type==='N'}"}, {"condition": "${tableCell.row.type==='E'}", "description": "${tableCell.row.type==='E'}"}]}, "next": ["f41119c4-c375-4c05-9935-bc8156fad90d", "cb5a9b3f-2317-4c6c-a5b3-9758993ccc7c"]}}}}, "body": [{"type": "IvModal", "designer": {"movein": false, "visible": false}, "props": {"value": "${modal1}", "title": "导出用户数据", "width": "550", "closable": true, "mask": true, "maskClosable": false, "loading": false, "scrollable": false, "lockScroll": false, "draggable": false, "sticky": false, "stickyDistance": 10, "okText": "确定", "cancelText": "取消", "className": "le-vertical-center-modal", "zIndex": 1000, "transfer": true, "footerHide": true}, "style": "", "class": "", "id": "", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "IamDataExporter", "props": {"pageSize": 100, "maxExportRecords": 10000, "dataSource": "${userList}", "candidateFields": {"type": "ExpKeepValue", "value": [{"fieldName": "id", "fieldDisplayName": "用户ID", "fieldValueExp": null, "_uid": "2023021417431838"}, {"fieldName": "name", "fieldDisplayName": "用户名称", "fieldValueExp": null, "_uid": "2023021417431839"}, {"fieldName": "username", "fieldDisplayName": "登录账号", "fieldValueExp": null, "_uid": "2023021417431840"}, {"fieldName": "type", "fieldDisplayName": "用户类型", "fieldValueExp": "${utils.optionSet.getTitleByValue('UserType',$row.type)}", "_uid": "2023021417431841"}, {"fieldName": "orgId", "fieldDisplayName": "所属组织", "fieldValueExp": "${$row.org?.name || $row.orgId}", "_uid": "2023021417431842"}, {"fieldName": "restricted", "fieldDisplayName": "是否受限用户", "fieldValueExp": "${$row.restricted?'是':'否'}", "_uid": "2023021417431844"}, {"fieldName": "inboundOrgId", "fieldDisplayName": "所属外部企业", "fieldValueExp": null, "_uid": "2023021417431850"}, {"fieldName": "email", "fieldDisplayName": "邮箱", "fieldValueExp": null, "_uid": "2023031016255644"}, {"fieldName": "mobile", "fieldDisplayName": "手机号码", "fieldValueExp": "", "_uid": "2023031016260547"}, {"fieldName": "enabled", "fieldDisplayName": "是否启用", "fieldValueExp": "${$row.enabled?'是':'否'}", "_uid": "2023031016265550"}, {"fieldName": "expiredAt", "fieldDisplayName": "过期时间", "fieldValueExp": null, "_uid": "2023031016272753"}, {"fieldName": "gender", "fieldDisplayName": "性别", "fieldValueExp": "${utils.optionSet.getTitleByValue('Gender',$row.gender)}", "_uid": "2023031016274356"}, {"fieldName": "userKind", "fieldDisplayName": "员工状态", "fieldValueExp": null, "_uid": "2023031016281259"}]}, "exportFileName": "${utils.user.tenantName+'-用户列表-'+utils.dayjs().format('YYYYMMDDHHmmss')}"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "FuseDoubleColumnsView", "props": {"fixedPart": "left", "fixedPartWidth": 250, "showScroll": false, "showTrigger": true, "triggerLength": 16}, "slots": {"default": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "margin-bottom:16px", "props": {"title": "用户", "padding": 16, "shadow": false, "bordered": true, "disHover": false, "target": "_self"}, "slots": {"default": {"children": [{"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "combo": false}, "props": {"title": "所有用户", "extra": "额外内容", "target": "_self"}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "73", "name": "cellValue", "value": "${cellValue='0'}"}, {"_uid": "119", "name": "userFilters", "value": "${'deleted ne true'}"}], "description": null}, "description": null, "id": "action-72", "activeOn": null}]}}, "slots": {"icon": {"children": [{"type": "Icon", "props": {"type": "bingo-organization", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false, "demo": {"props.title": "1", "props.title.type": "string"}}, "props": {"mode": "num", "title": "${activityUserTotal.total}", "overflowCount": 99, "type": "normal", "dot": true, "showZero": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "combo": false}, "props": {"title": "已删除用户", "extra": "额外内容", "target": "_self"}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "75", "name": "cellValue", "value": "${cellValue='1'}"}, {"_uid": "120", "name": "userFilters", "value": "${'deleted eq true'}"}], "description": null}, "description": null, "id": "action-74", "activeOn": null}]}}, "slots": {"icon": {"children": [{"type": "Icon", "props": {"type": "ios-trash", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false, "demo": {"props.title": "99", "props.title.type": "string"}}, "props": {"mode": "num", "title": "${userDeletedTotal.total}", "overflowCount": 99, "type": "info", "dot": true, "showZero": true}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IamGroupRule"}, {"type": "IamGroupRule"}]}, "left": {"children": [{"type": "Card", "designer": {"movein": false}, "style": "margin-bottom:16px", "props": {"title": "用户", "padding": 16, "shadow": false, "bordered": true, "disHover": false, "target": "_self"}, "slots": {"default": {"children": [{"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "combo": false}, "props": {"title": "所有用户", "extra": "额外内容", "target": "_self", "name": "0", "selected": "${cellValue==0}"}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "73", "name": "cellValue", "value": "${cellValue='0'}"}, {"_uid": "119", "name": "userFilters", "value": "${'deleted ne true'}"}], "description": null}, "description": null, "id": "action-72", "activeOn": null}]}}, "slots": {"icon": {"children": [{"type": "Icon", "props": {"type": "bingo-organization", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false, "demo": {"props.title": "1", "props.title.type": "string"}}, "props": {"mode": "num", "type": "normal", "dot": true, "showZero": false, "count": "${activityUserTotal.total}"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "<PERSON>v<PERSON>ell", "designer": {"movein": false, "combo": false}, "props": {"title": "已删除用户", "extra": "额外内容", "target": "_self", "name": "1", "selected": "${cellValue==1}"}, "events": {"on-click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "75", "name": "cellValue", "value": "${cellValue='1'}"}, {"_uid": "120", "name": "userFilters", "value": "${'deleted eq true'}"}], "description": null}, "description": null, "id": "action-74", "activeOn": null}]}}, "slots": {"icon": {"children": [{"type": "Icon", "props": {"type": "ios-trash", "size": 16}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [{"type": "IvBadge", "designer": {"moveChild": false, "movein": false, "demo": {"props.title": "99", "props.title.type": "string"}}, "props": {"mode": "num", "overflowCount": 99, "type": "normal", "dot": true, "showZero": true, "count": "${userDeletedTotal.total}"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "right": {"children": [{"type": "Card", "designer": {"movein": false, "lock": false}, "props": {"title": "${cellValue == '0' ? '所有用户':cellValue == '1' ? '已删除用户':'组织用户'}", "padding": 0, "bordered": true}, "style": "margin-top:0px;", "class": "ledesign-card-extra", "id": "", "visible": "${cellValue!=='3'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false, "demo": {"props.data": "[{\"col1\":\"admin\",\"col2\":\"admin\",\"col3\":\"企业用户\",\"col4\":\"2022-06-20 15:00:00\"}]"}}, "props": {"data": [], "tableCols": [], "enableSelectionCol": true, "enableIndexCol": true, "stripe": false, "border": false, "show-header": true, "loading": "${userList.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无用户", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "visibleHeaderOp": true, "headerSearch": {"enable": false, "filters": [{"key": "keyword", "condition": "cn"}]}, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "ds": "${userList}", "filters": {}}, "events": {"on-current-change": {"actions": []}, "on-select": {"actions": []}, "on-select-cancel": {"actions": []}, "on-select-all": {"actions": []}, "on-select-all-cancel": {"actions": []}, "on-selection-change": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "329", "name": "selectedUsers", "value": "${$event}"}]}, "description": null, "id": "action-328"}]}, "on-sort-change": {"actions": []}, "on-filter-change": {"actions": []}, "on-row-click": {"actions": []}, "on-row-dblclick": {"actions": []}, "on-expand": {"actions": []}, "on-cell-click": {"actions": []}}, "slots": {"headerExtra": {"children": [{"type": "Input", "props": {"type": "text", "size": "default", "placeholder": "根据用户名称搜索", "border": true, "search": true, "value": "${keyword}", "clearable": true, "enterButton": false}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "header": {"children": [{"type": "IvButton", "props": {"type": "default", "size": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "id": "action-68", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "91", "name": "selectedUsers", "value": "${[]}"}]}, "description": null, "id": "action-90"}]}}}, {"type": "IvButton", "props": {"text": "创建用户", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.create_user", "requestParams": [{"_uid": "73", "name": "orgId", "value": "${selectOrgId}"}], "dialogSettings": {"width": "${560}"}, "description": null}, "id": "action-26", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "id": "action-317"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}", "description": null}, "description": null, "id": "action-38", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "${cellValue !== '1'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvButton", "props": {"text": "创建外部用户", "type": "primary", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.create_out_user", "requestParams": [{"_uid": "75", "name": "orgId", "value": "${selectOrgId}"}], "dialogSettings": {"width": "${560}"}, "description": null}, "id": "action-24", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}", "description": null}, "id": "action-324"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "description": null, "id": "action-137"}]}}, "style": "", "class": "", "id": "", "visible": "${cellValue!='1'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IamUserImporter", "style": "display:inline;", "props": {"autoImportMaxRecords": 20, "entityName": "IamUser", "excelTemplateUrl": {"processor": "ImgPathProcessor", "type": "string", "value": "/raw/material/default/user-import-template.xlsx"}, "importUrl": {"type": "ServicePath", "source": "local", "value": "/api/normal/users/data-import", "processor": "ServicePathProcessor"}}, "slots": {"default": {"children": [{"type": "IvButton", "props": {"text": "导入", "type": "primary", "size": "default", "replace": false, "target": "_self", "append": false}, "events": {"click": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${cellValue === '0'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvButton", "props": {"text": "导出", "type": "default", "size": "default", "replace": false, "target": "_self", "append": false}, "events": {"click": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "343", "name": "modal1", "value": "${true}"}], "description": null}, "description": null, "id": "action-342", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "${cellValue === '0'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}, {"type": "IvDropdown", "designer": {"movein": false}, "props": {"text": "更多操作", "element": "button", "buttonType": "default", "trigger": "hover", "placement": "bottom", "data": [{"label": "导出", "value": "0", "disabled": "${selectedUsers.length === 0}", "hidden": true}, {"label": "批量禁用", "value": "1", "disabled": "${selectedUsers.filter(v=> v.id !== utils.user.userId && v.enabled).length === 0}", "hidden": "${!utils.FlyVueCore.PermissionService.hasPerm('t.userM_.enableUser')}"}, {"value": "2", "label": "复制", "disabled": "${selectedUsers.length !== 1}", "hidden": "${!utils.FlyVueCore.PermissionService.hasPerm('t.userM_.createUser')}"}, {"value": "3", "label": "消息通知", "disabled": "${selectedUsers.filter(v=> v.enabled).length === 0}"}, {"value": "4", "label": "批量删除", "disabled": "${selectedUsers.filter(v=> v.id !== utils.user.userId).length === 0}", "hidden": "${!utils.FlyVueCore.PermissionService.hasPerm('t.userM_.softDeleteUser')}"}], "keyNames": {}, "transfer": true}, "events": {"on-click": "YiBUnM8cjiR", "on-visible-change": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${cellValue === '0'}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "用户名称", "keyName": "name", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "${tableCell.row.name}", "type": "page", "linkColor": true, "disabled": "${cellValue === '1'}", "replace": false}, "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "/tenant/lowcode/tenant.user_admin.user_info", "variables": [{"_uid": "34", "name": "userId", "value": "${tableCell.row.id}"}], "cacheOpener": false, "description": null}, "id": "action-33", "activeOn": null}]}}, "auth": {"turnOn": true, "forbiddenStatus": "disabled", "requiredPermission": ["t.userM_.updateUser"]}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "登录账号", "keyName": "username", "dataTimeType": "YYYY-MM-DD", "sortable": false}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.username}", "showTitle": false, "maxline": 0}, "auth": {"turnOn": false, "forbiddenStatus": "invisible", "requiredPermission": []}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "用户类型", "keyName": "type", "dataTimeType": "YYYY-MM-DD", "options": "UserType", "dataDictionary": "UserType"}, "enableSlot": true, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "企业用户", "props.text.type": "string"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "所属组织", "keyName": "orgId", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvTooltip", "designer": {"movein": false}, "props": {"title": "${tableCell.row.type === 'E' && tableCell.row.orgId === utils.user.tenantId ? '/' : tableCell.row.org.name}", "content": "${tableCell.row.type === 'E' && tableCell.row.orgId === utils.user.tenantId ? '/' : tableCell.row.fullOrgName}", "placement": "top", "delay": 0, "theme": "dark", "transfer": true}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "员工类型", "keyName": "staffType", "dataType": "text", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "StaffType", "align": "left", "verticalAlign": "le-align-middle", "filterMultiple": true}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "公共账号"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "任职岗位", "keyName": "name_81"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 1, "text": "${tableCell.row.positions?.map(v=> v.positionName).join(',')}", "showTitle": true}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "排序号", "keyName": "sortOrder", "sortable": true}, "style": "", "class": "", "id": "", "visible": false, "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作", "keyName": "operations", "dataTimeType": "YYYY-MM-DD", "width": 180}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "编辑"}, "auth": {"turnOn": true, "forbiddenStatus": "invisible", "requiredPermission": []}, "events": {"click": {"actions": [{"type": "Action.OpenUrl", "inputs": {"url": "/lowcode/tenant.user_admin.user_info", "variables": [{"_uid": "38", "name": "userId", "value": "${tableCell.row.id}"}], "cacheOpener": true}, "description": null, "id": "action-37"}]}}, "style": "", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "linkColor": true, "text": "删除"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${'是否确认删除用户'+tableCell.row.name+'?'}", "okText": "确定", "icon": ""}, "description": null, "id": "action-59"}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/{id}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"id": "${tableCell.row.id}", "soft": "${true}"}}, "description": null, "id": "action-61"}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-82"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-90"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${activityUserTotal}"}, "description": null, "id": "action-94"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "description": null, "id": "action-98"}]}}, "style": "", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "重置密码"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.user_admin.zhong<PERSON>mima", "requestParams": [{"_uid": "40", "name": "userId", "value": "${tableCell.row.id}"}]}, "description": null, "id": "action-39"}]}}, "style": "", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "${tableCell.row.enabled?'禁用':'启用'}"}, "events": {"click": "Am1qrsnpTmd"}, "style": "${ {'color': tableCell.row.id === utils.user.userId ? '#c5c8ce' : !tableCell.row.enabled ? 'rgb(255,153,0)':'rgb(45,140,240)'}}", "class": "", "id": "", "visible": "${!tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "恢复"}, "events": {"click": "uNgevv3KX3g"}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "永久删除"}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "error", "title": "提示", "content": "${'确定永久删除用户 '+tableCell.row.name+' ?'+\"此操作不可恢复,请谨慎操作\"}", "okText": "确定", "icon": "", "description": null}, "description": null, "id": "action-67", "activeOn": null}, {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/users/{id}", "processor": "ServicePathProcessor"}, "method": "DELETE", "params": {"id": "${tableCell.row.id}", "soft": "${false}"}}, "description": null, "id": "action-69"}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "操作成功", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-77"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userList}"}, "description": null, "id": "action-79"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${userDeletedTotal}"}, "description": null, "id": "action-83"}]}}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.deleted}", "loop": {"data": "", "variableName": "loopItem1", "indexName": "loopIndex1", "key": ""}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "用户管理", "content": "支持管理当前用户信息", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}, "style": "padding-top:12px;padding-left:12px;"}], "meta": {"title": "用户管理页面"}}