{"type": "AdaptivePage", "version": "2.0", "dataSources": [], "variables": {"form": {"type": "object", "title": "提交表单", "orderNo": 0}, "id": {"type": "string", "default": "${request.params.id}", "source": "request", "optionSet": null, "entity": null, "title": "用户id", "orderNo": 1}, "name": {"type": "string", "default": "${request.params.name}", "source": "request", "optionSet": null, "entity": null, "title": "用户名", "orderNo": 2}}, "body": [{"type": "IvForm", "props": {"model": "${form}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "用户名", "placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${name}", "disabled": true, "labelWidth": 140}, "style": "margin-bottom:35px;"}, {"type": "IvFormItem", "designer": {"movein": false}, "props": {"labelName": "备注", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "labelWidth": 140}, "style": "margin:0px;", "slots": {"default": {"children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.description}"}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 10}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "IT需求URL", "placeholder": "请选择IT需求", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('ItType').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${form.itType}", "dataDictionary": "ItType", "dataDictionaryDisabled": [], "requiredName": "ivu-form-item-required", "prop": "itType", "labelWidth": 140, "firstValue": true}, "style": "margin:0px;padding:0px;"}], "style": "padding:0px;"}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 14}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入IT需求URL", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.itUrl}", "labelWidth": 0}, "style": ""}], "style": "padding:0px;"}], "props": {"customCol": "10:14", "gutter": 16, "wrap": true}, "style": "width:100%;margin:0px;"}], "style": "padding-right:12px;padding-left:12px;", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "footer": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 2}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 4}, "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-145"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "确认", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/operationUser", "method": "POST", "params": {"operationType": "${'resetPass'}", "status": "${1}", "objectId": "${request.params.id}"}, "data": "${form}", "description": null}, "description": null, "id": "action-149", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已提交", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-185"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-188"}]}, "click.stop": {"actions": []}}}]}], "props": {"customCol": "6:6:6:2:4", "gutter": 16, "wrap": true}}], "meta": {"title": "置空密码", "platform": "pc"}}