{"type": "AdaptivePage", "version": "2.0", "variables": {"form": {"type": "object", "title": "表单内容", "orderNo": 0}, "msg": {"type": "string", "default": "", "title": "导入消息提醒", "orderNo": 1}, "success": {"type": "string", "default": "", "title": "上传成功数", "orderNo": 2}, "fail": {"type": "string", "default": "", "title": "上传失败数", "orderNo": 3}}, "orchestrations": {"TSJFYjS1lnn": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-102"}, "action-102": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "103", "name": "form.userList", "value": "${$event.response.userEntityList}"}, {"_uid": "53", "name": "success", "value": "${$event.response.success}"}, {"_uid": "54", "name": "fail", "value": "${$event.response.fail}"}], "description": null}, "next": "action-109"}, "action-109": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "110", "name": "msg", "value": "${`导入结果:导入成功${success}条，失败${fail}条`}"}]}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}}, "dataSources": [], "style": "", "body": [{"type": "IvUpload", "designer": {"movein": false}, "props": {"action": "${'/iamconsole/api/tenant/user/extend/import'}", "name": "file", "multipleUpload": false, "multiple": false, "withCredentials": false, "showUploadList": true, "headers": {"Authorization": "${'Bearer ' + utils.user.token}"}, "type": "select", "text": "上传", "dragText": "将文件拖到此处，或点击上传", "buttonType": "primary", "icon": "ios-cloud-upload", "accept": [".xlsx"], "valueToEntries": false}, "events": {"on-progress": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "104", "name": "msg", "value": "上传中"}]}, "description": null, "id": "action-103"}]}, "on-success": "TSJFYjS1lnn", "on-error": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "107", "name": "msg", "value": "上传失败"}]}, "description": null, "id": "action-106"}]}, "on-preview": {"actions": []}, "on-remove": {"actions": []}, "on-format-error": {"actions": []}, "on-exceeded-size": {"actions": []}}, "style": "width:150px;"}, {"type": "IvLink", "props": {"tag": "a", "linkColor": true, "text": "下载模板"}, "events": {"click": {"actions": [{"type": "Action.HttpDownload", "inputs": {"url": "/api/tenant/file/download/adUser", "filename": "AD用户批量新增导入模板.xlsx"}, "description": null, "id": "action-92"}]}, "click.stop": {"actions": []}}}, {"type": "IvText", "props": {"maxLine": 0, "text": "${msg}"}, "style": "margin:12px;"}, {"type": "IvForm", "props": {"model": "${{}}", "rowspace": 16, "labelPosition": "right", "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('IT_TYPE').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "border": true, "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": true}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${form.itType}", "dataDictionary": "IT_TYPE", "defaultValue": "0", "requiredName": "ivu-form-item-required", "labelWidth": 0}, "style": "width:150px;"}]}], "style": ""}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 18}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${form.itUrl}", "labelWidth": 0}, "style": "width:500px;", "class": ""}]}], "style": ""}], "props": {"customCol": "6:18", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "IvTable", "designer": {"movein": false, "moveout": false}, "children": [{"type": "IvTableColumn", "props": {"title": "姓名", "keyName": "name", "width": 100}, "designer": {"movein": false}}, {"type": "IvTableColumn", "props": {"title": "登录名", "keyName": "username", "align": "left"}, "designer": {"movein": false}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_136", "title": "全拼", "keyName": "pinyin", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_137", "title": "简拼", "keyName": "py", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_138", "title": "角色", "keyName": "employeeRoleName", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_139", "title": "是否发送ACM", "keyName": "acmFlag", "align": "left", "width": 130}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.acmFlag=='1'?'是':'否'}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_140", "title": "生效时间", "keyName": "enableDate", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_141", "title": "失效时间", "keyName": "expiredAtStr", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_142", "title": "邮箱用户级别", "keyName": "emailUserServiceLevelStr", "align": "left", "width": 100}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_143", "title": "管理者", "keyName": "admin", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_144", "title": "主部门", "keyName": "orgName", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_145", "title": "其余所属组织", "keyName": "otherOrgNames", "align": "left", "width": 130}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_146", "title": "备注", "keyName": "description", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_71", "title": "密码", "keyName": "password", "align": "left"}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_68", "title": "导入结果", "align": "left", "fixed": "right"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.isSuccessful?'成功':tableCell.row.errorMessage}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "props": {"data": "${form.userList}", "columns": [], "noDataText": "暂无数据", "sumText": "合计", "indentSize": 16, "noFilteredDataText": "暂无筛选结果", "size": "default", "showHeader": true, "tooltipTheme": "dark", "tooltipMaxWidth": 300, "fixedShadow": "show"}, "style": "width:100%;"}], "style": "width:100%;"}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "关闭", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-151"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "提交", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.Http", "inputs": {"schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/ad/batchInsert", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"operationType": "${'batchAD'}", "status": "1", "itType": "${form.itType}", "itUrl": "${form.itUrl}"}, "data": "${form}", "description": null}, "description": null, "id": "action-45", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "已提交请求到复核列表", "duration": 1.5, "closable": false, "background": false}, "description": null, "id": "action-50"}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-55"}]}, "click.stop": {"actions": []}}}], "style": "width:100%;text-align:right;padding-top:12px;padding-bottom:12px;"}], "meta": {"title": "上传文件界面", "platform": "pc"}}