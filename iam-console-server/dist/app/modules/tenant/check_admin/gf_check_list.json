{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "checkData", "title": "查询数据列表", "multiple": true, "schema": {"id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过 4-已撤回"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "deleted": {"type": "boolean", "title": "可用状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "requestObjectStr": {"type": "string", "title": "操作对象"}}, "lazy": true, "autoReload": false, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getList", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"page": null, "size": null, "limit": null, "offset": null, "select": null, "filters": null, "search": null, "expand": null, "joins": null, "orderby": null, "objectType": null, "total": "${true}"}, "data": "${operQuery}"}], "variables": {"keyword": {"type": "string", "default": "", "title": "", "orderNo": 0}, "submitBy": {"type": "string", "default": "", "title": "submitBy", "orderNo": 1}, "operQuery": {"type": "object", "default": {"submitBy": null, "name": null, "startTime": null, "endTime": null, "listType": "to<PERSON><PERSON><PERSON>"}, "title": "复核请求体", "orderNo": 2}, "cancelTheReviewResult": {"type": "object", "title": "撤回复核结果", "orderNo": 3}}, "orchestrations": {"Qx6ZUCsoLBq": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "8040c49c-ec9c-4e48-8a83-************"}, "action-48-condition": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'add'}"}, {"condition": "${tableCell.row.operationType == 'impDelay'|| tableCell.row.operationType == 'resetPass'||tableCell.row.operationType == 'updatePass'||tableCell.row.operationType == 'update'||tableCell.row.operationType == 'disable'||tableCell.row.operationType == 'enable'||tableCell.row.operationType == 'enaDel'||tableCell.row.operationType == 'delay'}", "description": "${tableCell.row.operationType == 'impDelay'|| tableCell.row.operationType == 'resetPass'||tableCell.row.operationType == 'updatePass'||tableCell.row.operationType == 'update'||tableCell.row.operationType == 'disable'||tableCell.row.operationType == 'enable'||tableCell.row.operationType == 'enaDel'||tableCell.row.operationType == 'delay'}"}, {"condition": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'org' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}", "description": "${tableCell.row.objectType == 'user' && tableCell.row.operationType == 'delete'}"}, {"condition": "${tableCell.row.operationType == 'move'}", "description": "${tableCell.row.operationType == 'move'}"}, {"condition": "${tableCell.row.operationType == 'batchAD'}", "description": "${tableCell.row.operationType == 'batchAD'}"}]}, "next": ["139e28a8-d4ed-4631-81b8-388d147189c2", "de17e820-d419-4300-b4d1-933a6d6ebea1", "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9", "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a", "17d85de0-0b53-4dc8-a5e5-7d82d56115be", "5da6ec13-411b-4349-b002-c3231c8b12ec", "2e544848-bd75-47da-9156-b19249b3cc69"]}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "139e28a8-d4ed-4631-81b8-388d147189c2": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_check_user", "requestParams": [{"_uid": "258", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "de17e820-d419-4300-b4d1-933a6d6ebea1": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_check_org", "requestParams": [{"_uid": "266", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "40d4615e-47f0-4ef3-af9e-e35ca2c3dfc9": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "77", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "595e0c70-d4a6-42ae-87f9-c7f4a8fd284a": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_org_delete_check", "requestParams": [{"_uid": "199", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1200", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "17d85de0-0b53-4dc8-a5e5-7d82d56115be": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_check_user_delete", "requestParams": [{"_uid": "200", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "5da6ec13-411b-4349-b002-c3231c8b12ec": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.edit_check", "requestParams": [{"_uid": "62", "name": "id", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1000", "continueOnClose": "true"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}, "8040c49c-ec9c-4e48-8a83-************": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${tableCell.row.approvalBy && tableCell.row.approvalBy != utils.user.userId}", "description": "${tableCell.row.approvalBy && tableCell.row.approvalBy != utils.user.userId}"}, {"condition": "${!tableCell.row.approvalBy}", "description": "${!tableCell.row.approvalBy}"}, {"condition": "${tableCell.row.approvalBy&&tableCell.row.approvalBy==utils.user.userId}", "description": "${tableCell.row.approvalBy&&tableCell.row.approvalBy==utils.user.userId}"}]}, "next": ["f5e4bdbb-2491-4e04-aa1d-71910b1b9e6f", "db0cd243-487a-4877-9d5d-37f4cd9933cc", "action-48-condition"]}, "f5e4bdbb-2491-4e04-aa1d-71910b1b9e6f": {"type": "Action.Confirm", "inputs": {"type": "info", "title": "提示", "content": "当前单子正在被审核", "okText": "确定", "icon": ""}, "next": null}, "db0cd243-487a-4877-9d5d-37f4cd9933cc": {"type": "Action.Http", "inputs": {"resultName": "count", "schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/count", "processor": "ServicePathProcessor"}, "method": "GET"}, "next": "37a74b47-63fc-4475-9958-51d0983ff7fc"}, "37a74b47-63fc-4475-9958-51d0983ff7fc": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${count >= 1}", "description": "${count >= 1}"}, {"condition": "${count == 0}", "description": "${count == 0}"}]}, "next": ["1d0a8e3d-a7a8-4fe2-aae6-5fd28e55c7d6", "768e2f40-c5c8-4576-aa45-492e5e6ded0e"]}, "1d0a8e3d-a7a8-4fe2-aae6-5fd28e55c7d6": {"type": "Action.Confirm", "inputs": {"type": "info", "title": "提示", "content": "您有未完成的复核，请先进行复核", "okText": "确定", "icon": ""}, "next": null}, "768e2f40-c5c8-4576-aa45-492e5e6ded0e": {"type": "Action.Http", "inputs": {"schema": {}, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/check", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"requestId": "${tableCell.row.id}"}}, "next": "18b52e56-3012-4a41-a31d-e21a2b185a7f"}, "18b52e56-3012-4a41-a31d-e21a2b185a7f": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "action-48-condition"}, "98187555-11a4-4df9-88d4-55d990e55474": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "end"}, "2e544848-bd75-47da-9156-b19249b3cc69": {"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.gf_batch_AD_user", "requestParams": [{"_uid": "83", "name": "requestId", "value": "${tableCell.row.id}"}], "dialogSettings": {"width": "1200", "continueOnClose": "${true}"}}, "next": "98187555-11a4-4df9-88d4-55d990e55474"}}}, "jV88zT8S4c8": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-98"}, "action-98": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "6C8ehOc7Qc3": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-37"}, "action-37": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}, "8FoRcrJoTzo": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-54"}, "action-54": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'确定要撤回该复核吗？'}", "okText": "确定", "icon": "", "description": null}, "next": "action-56"}, "action-56": {"type": "Action.Http", "inputs": {"resultName": "cancelTheReviewResult", "url": "/api/tenant/requestOperation/updateOperation", "method": "POST", "params": {"id": "${tableCell.row.id}", "status": "${1}", "oper": "${'reset'}"}, "description": null}, "next": "b6182e9e-e8a6-47cb-9b44-94eace6eaa9e"}, "action-61": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "撤回复核成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "next": "action-222"}, "action-222": {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "b6182e9e-e8a6-47cb-9b44-94eace6eaa9e": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${cancelTheReviewResult.code == 0}", "description": "${cancelTheReviewResult.code == 0}"}, {"condition": "${cancelTheReviewResult.code == 1}", "description": "${cancelTheReviewResult.code == 1}"}]}, "next": ["action-61", "0df1e869-7669-4244-9571-28743d4fcdba"]}, "0df1e869-7669-4244-9571-28743d4fcdba": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${cancelTheReviewResult.message}", "duration": 1.5, "closable": false, "background": false}, "next": "action-222"}}}}, "events": {"on-rendered": {"actions": []}, "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "Card", "designer": {"movein": false}, "style": "", "props": {"icon": "md-card", "padding": 0, "bordered": true, "disHover": false, "shadow": false, "replace": false, "append": false}, "slots": {"default": {"children": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false, "demo": {"props.data": "[{\"col1\":\"部门管理员\",\"col2\":\"组织管理者\",\"col3\":\"2022-06-17 10:42:00\"},{\"col1\":\"采购负责人\",\"col2\":\"采购经理\",\"col3\":\"2022-06-17 10:42:00\"}]"}, "lock": false}, "props": {"data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "stripe": false, "border": false, "show-header": true, "loading": "${checkData.loading}", "disabled-hover": false, "highlight-row": false, "no-data-text": "暂无复核数据", "no-filtered-data-text": "暂无筛选结果", "draggable": false, "visibleHeaderOp": true, "headerSearch": {"enable": false, "filters": [{"key": "keyword", "condition": "cn"}]}, "visibleMore": false, "visiblePage": true, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "current": 1}, "ds": "${checkData}", "loadOnMounted": true}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"default": {"children": [{"designer": {"movein": false, "combo": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "需求来源", "keyName": "itType", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "IT_TYPE"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "IT需求"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "需求url", "keyName": "itUrl"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.itUrl}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作人", "keyName": "submitBy"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"maxLine": 0, "text": "${tableCell.row.submitBy}"}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作对象", "keyName": "requestObjectStr", "dataTimeType": "YYYY-MM-DD", "children": []}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.requestObjectStr}", "maxLine": 3, "showTitle": true}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作类型", "keyName": "operationType", "dataTimeType": "YYYY-MM-DD", "dataDictionary": "operationType", "children": []}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "新增"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "提交时间", "keyName": "submitAtStr", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.submitAtStr}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作描述", "keyName": "description", "dataTimeType": "YYYY-MM-DD", "children": []}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.description}", "maxLine": 3, "showTitle": true}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "承接人", "keyName": "approvalByName", "dataTimeType": "YYYY-MM-DD"}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${tableCell.row.approvalByName}", "maxLine": 0}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"designer": {"movein": false}, "type": "IvTableGridColumn", "props": {"show": true, "title": "操作", "keyName": "operations", "dataTimeType": "YYYY-MM-DD", "align": "left", "children": []}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "text": "复核", "type": "page", "linkColor": true, "disabled": false, "replace": false}, "events": {"click": "Qx6ZUCsoLBq"}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.createdBy != utils.user.userId}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "text": "撤回", "type": "page", "linkColor": true, "replace": false}, "events": {"click": {"actions": [{"type": "Action.Confirm", "inputs": {"type": "primary", "title": "提示", "content": "${'确定要执行此操作吗？'}", "okText": "确定", "icon": "", "description": null}, "id": "action-54", "activeOn": null}, {"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/updateOperation", "method": "POST", "params": {"id": "${tableCell.row.id}", "status": "${4}"}, "description": null}, "id": "action-56", "activeOn": null}, {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "撤回成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "id": "action-61", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "id": "action-222", "activeOn": null}]}}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.createdBy === utils.user.userId}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvLink", "props": {"tag": "a", "text": "撤回复核", "type": "page", "linkColor": true, "replace": false}, "events": {"click": "8FoRcrJoTzo", "click.stop": {"actions": []}}, "style": "", "class": "", "id": "", "visible": "${tableCell.row.approvalBy === utils.user.userId}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}, "header": {"children": [{"type": "IvButton", "props": {"type": "default", "size": "default", "icon": "md-refresh"}, "events": {"click": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}", "description": null}, "id": "action-393", "activeOn": null}]}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "headerExtra": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "search": {"children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "提交时间："}, "style": "width:80px;text-align:right;"}, {"type": "DatePicker", "props": {"placeholder": "开始时间", "type": "datetime", "options": {"shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": true, "transfer": true, "value": "${operQuery.startTime}", "format": "yyyy-MM-dd HH:mm:ss", "multiple": false}, "events": {"on-change": {"actions": []}, "on-open-change": {"actions": []}, "on-ok": "jV88zT8S4c8", "on-clear": {"actions": []}, "on-clickoutside": {"actions": []}}, "style": "width:150px;"}, {"type": "DatePicker", "props": {"placeholder": "结束时间", "type": "datetime", "options": {"shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": true, "transfer": true, "value": "${operQuery.endTime}", "multiple": false}, "events": {"on-change": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "description": null, "id": "action-102"}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "description": null, "id": "action-102"}]}, "on-open-change": {"actions": []}, "on-ok": {"actions": []}, "on-clear": {"actions": []}, "on-clickoutside": {"actions": []}}, "style": "width:150px;"}]}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "被操作人："}, "style": "width:100px;text-align:right;"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "输入被操作用户名查询", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "value": "${operQuery.name}"}, "events": {"on-enter": "6C8ehOc7Qc3", "on-click": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}, "style": "width:150px;"}], "style": "display:flex;align-items:center;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "操作人："}, "style": "width:100px;text-align:right;"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "输入操作人用户名查询", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "value": "${operQuery.submitBy}"}, "events": {"on-enter": {"actions": [{"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${checkData}"}, "description": null, "id": "action-41"}]}, "on-click": {"actions": []}, "on-change": {"actions": []}, "on-search": {"actions": []}, "on-clear": {"actions": []}, "on-focus": {"actions": []}, "on-blur": {"actions": []}, "on-keyup": {"actions": []}, "on-keydown": {"actions": []}, "on-keypress": {"actions": []}}, "style": "width:150px;"}], "style": "display:flex;align-items:center"}], "style": "display:flex;"}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "extra": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false, "visible": false}}}}], "header": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "designer": {"movein": false}, "props": {"title": "复核管理", "breadcrumbList": [{"title": "首页", "to": ""}, {"title": "菜单1", "to": ""}], "hidden-breadcrumb": true, "tabList": [], "back": false, "wide": false}, "style": "font-size:16px;padding-top:12px;"}], "auth": {"turnOn": true, "requiredPermission": [], "license": null}, "meta": {"title": "复核列表", "name": "role_list", "packageName": "tenant.public_role"}}