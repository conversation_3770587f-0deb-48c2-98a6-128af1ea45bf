{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "orgData", "title": "获取详情", "multiple": false, "schema": {"properties": {"type": "object"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "id": {"type": "string", "title": "ID"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "objectType": {"type": "string", "title": "操作对象类型;user-用户，org-组织"}, "operationType": {"type": "string", "title": "操作类型;add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回 3-已通过"}, "submitBy": {"type": "string", "title": "提交用户ID"}, "submitAt": {"type": "string", "title": "提交时间"}, "submitAtStr": {"type": "string", "title": "提交时间"}, "approvalBy": {"type": "string", "title": "审核用户ID"}, "approvalAt": {"type": "string", "title": "审核时间"}, "approvalOpinion": {"type": "string", "title": "审核意见"}, "approvalByName": {"type": "string", "title": "复核人名称"}, "description": {"type": "string", "title": "描述"}, "requestObject": {"type": "string", "title": "请求对象"}, "requestObjectStr": {"type": "string", "title": "操作对象"}, "requestObjectOld": {"type": "string", "title": "请求对象（旧）"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "newIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织ID"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户"}, "employeeRoleName": {"type": "string", "title": "角色"}, "phone": {"type": "string", "title": "座机"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "otherOrgId": {"type": "array", "title": "其他群组ID"}}}, "oldIamUser": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "sortOrder": {"type": "integer", "title": "排序，默认从小到大排序"}, "username": {"type": "string", "title": "登录账号"}, "password": {"type": "string", "title": "登录密码"}, "orgId": {"type": "string", "title": "所属组织ID"}, "orgName": {"type": "string", "title": "所属组织ID"}, "mobile": {"type": "string", "title": "手机号码"}, "email": {"type": "string", "title": "邮箱"}, "expiredAt": {"type": "string", "title": "过期时间"}, "pinyin": {"type": "string", "title": "用户名拼音"}, "py": {"type": "string", "title": "用户名首字母拼音"}, "staffType": {"type": "array", "title": "员工类型"}, "oaName": {"type": "string", "title": "OA用户名"}, "mailboxCapacity": {"type": "string", "title": "邮箱容量"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "adShowName": {"type": "string", "title": "AD显示名"}, "enableDate": {"type": "string", "title": "生效时间"}, "company": {"type": "string", "title": "公司"}, "interfaceBy": {"type": "string", "title": "接口人"}, "emailUserServiceLevel": {"type": "string", "title": "邮箱用户服务级别"}, "disableAccessToOa": {"type": "string", "title": "禁止访问OA门户"}, "employeeRoleName": {"type": "string", "title": "角色"}, "phone": {"type": "string", "title": "座机"}, "description": {"type": "string", "title": "备注"}, "erpId": {"type": "string", "title": "erp号"}, "position": {"type": "string", "title": "岗位"}, "workPlace": {"type": "string", "title": "工作地址"}, "manager": {"type": "string", "title": "上级经理"}, "postalCode": {"type": "string", "title": "邮政编码"}, "directPhone": {"type": "string", "title": "分机号"}, "otherOrgId": {"type": "array", "title": "其他群组ID"}}}, "newIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼"}, "businessCode": {"type": "string", "title": "业务代码"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型"}, "groupType": {"type": "string", "title": "群组类型"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型"}, "member": {"type": "array", "title": "成员"}, "groupMember": {"type": "array", "title": "组织成员"}, "otherOrg": {"type": "array", "title": "其他群组"}}}, "oldIamOrg": {"type": "object", "properties": {"id": {"type": "string", "title": "用户ID"}, "name": {"type": "string", "title": "用户名称"}, "code": {"type": "string", "title": "用户编码"}, "operationType": {"type": "string", "title": "操作类型 add-新增，update-修改，delete-删除"}, "status": {"type": "integer", "title": "请求状态;0-草稿，1-审核中，2-已驳回"}, "objectId": {"type": "string", "title": "操作对象的ID"}, "itType": {"type": "string", "title": "IT需求类型"}, "itUrl": {"type": "string", "title": "IT需求地址"}, "acmFlag": {"type": "integer", "title": "是否同步ACM 0：否 1：是"}, "parentId": {"type": "string", "title": "上级组织ID"}, "description": {"type": "string", "title": "组织描述"}, "sortOrder": {"type": "integer", "title": "组织排序号"}, "type": {"type": "string", "title": "组织类型"}, "kindId": {"type": "string", "title": "组织分类ID"}, "externalId": {"type": "string", "title": "外部ID"}, "hidden": {"type": "boolean", "title": "是否隐藏"}, "email": {"type": "string", "title": "邮箱"}, "simpleSpell": {"type": "string", "title": "组账户名"}, "oaSimpleSpell": {"type": "string", "title": "OA组简拼"}, "businessCode": {"type": "string", "title": "业务代码"}, "selfBusinessCode": {"type": "string", "title": "上级业务代码"}, "emailInternet": {"type": "string", "title": "因特网地址"}, "otherEmail": {"type": "string", "title": "其他邮箱地址"}, "emailDomain": {"type": "string", "title": "邮件域"}, "deptType": {"type": "string", "title": "部门类型"}, "groupType": {"type": "string", "title": "群组类型"}, "manager": {"type": "string", "title": "组管理者"}, "sysType": {"type": "string", "title": "类型"}, "member": {"type": "array", "title": "成员"}, "groupMember": {"type": "array", "title": "组织成员"}, "otherOrg": {"type": "array", "title": "其他群组"}}}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/requestOperation/getDetail", "processor": "ServicePathProcessor"}, "method": "POST", "params": {"id": "${request.params.id}", "select": null}}], "orchestrations": {"PEKAhkIo97K": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "e82dd90c-68f3-4d9d-ad70-2a2ca2073ac8"}, "action-53": {"type": "Action.Http", "inputs": {"url": "/api/tenant/requestOperation/updateOperation", "method": "POST", "params": {"id": "${orgData.data.id}", "status": "3"}, "data": "", "description": null, "resultName": "result"}, "next": "7932044d-356b-4cbd-a883-9ed70ed24546"}, "action-65": {"type": "Action.Message", "inputs": {"notifyType": "success", "content": "操作成功", "duration": 1.5, "closable": false, "background": false, "description": null}, "next": "action-67"}, "action-67": {"type": "Action.CloseDialog", "inputs": {"description": null}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}, "e82dd90c-68f3-4d9d-ad70-2a2ca2073ac8": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "120", "name": "canOpera", "value": "${false}"}]}, "next": "action-53"}, "7932044d-356b-4cbd-a883-9ed70ed24546": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "121", "name": "canOpera", "value": "${true}"}]}, "next": "2dd492a2-9f71-4b7b-8cc2-5e5fc692de34"}, "2dd492a2-9f71-4b7b-8cc2-5e5fc692de34": {"type": "Action.Switch", "inputs": {"dataConditions": [{"condition": "${result.code == 0}", "description": "${result.code == 0}"}, {"condition": "${result.code ==1}", "description": "${result.code ==1}"}]}, "next": ["action-65", "e587b292-09f7-44f1-afc3-3f00458da882"]}, "e587b292-09f7-44f1-afc3-3f00458da882": {"type": "Action.Confirm", "inputs": {"type": "primary", "title": "发送acm提示", "content": "${result.message}", "okText": "确定", "icon": ""}, "next": "action-67"}}}}, "variables": {"canOpera": {"type": "boolean", "default": true, "title": "是否可操作", "orderNo": 0}, "type": {"type": "string", "default": "${request.params.type}", "source": "request", "optionSet": null, "entity": null, "title": "可操作 | 详情", "orderNo": 1}}, "body": [{"type": "IvForm", "props": {"gutter": "0", "labelPosition": "right", "labelWidth": 120, "showMessage": true, "model": "${orgData.data.newIamOrg}"}, "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "IT服务台："}, "style": "padding-top:12px;padding-bottom:12px;padding-left:8px;font-weight:normal;color:#000000;font-size:16px;width:100%;"}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "是否发送ACM", "placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${orgData.data.acmFlag?'是':'否'}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormCheckbox", "designer": {"movein": false}, "props": {"span": "12", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "必填", "required": true}]}, "width": "100%", "show-message": true, "size": "default", "data": "${utils.optionSet.find('OrgSysType').items}", "keyNames": {"label": "title"}, "value": "${orgData.data.newIamOrg.sysType}", "dataDictionary": "OrgSysType", "dataDictionaryDisabled": [], "disabled": true, "requiredName": "ivu-form-item-required", "prop": "sysType"}}, {"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 9}, "children": [{"type": "IvFormSelect", "designer": {"movein": false}, "props": {"placeholder": "请选择", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('ItType').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "border": true, "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${orgData.data.newIamOrg.itType}", "disabled": true, "dataDictionary": "ItType", "labelWidth": 140}}]}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 15}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${orgData.data.newIamOrg.itUrl}", "disabled": true, "labelWidth": 0}}]}], "props": {"customCol": "9:15", "gutter": 16, "wrap": true}, "style": "width:100%;"}, {"type": "IvText", "props": {"maxLine": 0, "text": "组织信息："}, "style": "width:100%;padding-top:12px;padding-bottom:12px;padding-left:8px;font-weight:normal;color:#000000;font-size:16px;"}, {"type": "IvFormInput", "props": {"labelName": "群组名称", "span": "8", "width": "100%", "type": "text", "size": "default", "border": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.name}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "props": {"labelName": "组账户名", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.simpleSpell}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "OA组简拼", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.oaSimpleSpell}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "序号", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.sortOrder2}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "业务代码", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.businessCode}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "上级业务代码", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.selfBusinessCode}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "主邮箱", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.email}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "因特网地址", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.emailInternet}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "其他邮箱地址", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.otherEmail}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "部门类型", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('deptType').items}", "keyNames": {"label": "title", "value": "value"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${orgData.data.newIamOrg.deptType}", "dataDictionary": "deptType", "dataDictionaryDisabled": [], "disabled": true, "placeholder": " ", "labelWidth": 140}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "群组类型", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('groupType').items}", "keyNames": {"label": "title", "value": "value"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "8", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${orgData.data.newIamOrg.groupType}", "dataDictionary": "groupType", "dataDictionaryDisabled": [], "disabled": true, "placeholder": " ", "labelWidth": 140}}, {"type": "IvFormInput", "props": {"labelName": "组管理者", "span": "8", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.manager}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "IvFormInput", "props": {"labelName": "备注", "span": "24", "width": "100%", "type": "text", "autosize": true, "size": "default", "border": true, "rows": 1, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.description}", "disabled": true, "labelWidth": 140}, "designer": {"combo": false}}, {"type": "UserSelect", "props": {"col": 24, "labelWidth": "140px", "labelTitleAlign": "right", "value": "${orgData.data.newIamOrg.member}", "title": "用户成员", "disabled": true}, "style": "margin-bottom:24px;"}, {"type": "OrganModel", "props": {"title": "群组成员", "col": 24, "labelWidth": "140px", "labelTitleAlign": "right", "value": "${orgData.data.newIamOrg.groupMember}", "disabled": true}, "style": "margin-bottom:24px;"}, {"type": "IamFormOrgSelect", "designer": {"movein": false, "lock": false, "collapse": false}, "props": {"labelName": "所属部门群组", "span": "12", "type": "input", "multiple": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "value": "required", "type": "required", "required": false, "message": "该项为必填项"}]}, "value": "${orgData.data.newIamOrg.parentOrg!=null?orgData.data.newIamOrg.parentOrg.orgId:null}", "showConfig": false, "disabled": true, "placeholder": " ", "labelWidth": 140, "orgUrl": "/api/tenant/org/extend/tree/orgs/extend", "initOrgId": "/api/tenant/org/extend/tree/roots/extend", "rootOrgUrl": "/api/tenant/org/extend/tree/roots/extend"}, "events": {"on-change": {"actions": []}}, "style": "", "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IamFormOrgSelect", "designer": {"movein": false}, "props": {"labelName": "所属其他群组", "span": "12", "type": "input", "multiple": true, "disabled": true, "canSelectDisabledIdChild": false, "disabledChild": false, "modalTitle": "选择组织", "modalWidth": 650, "showConfig": true, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${orgData.data.newIamOrg.otherOrg}", "labelWidth": 140, "placeholder": " ", "orgUrl": "/api/tenant/org/extend/tree/orgs/extend", "initOrgId": "/api/tenant/org/extend/tree/roots/extend", "rootOrgUrl": "/api/tenant/org/extend/tree/roots/extend"}, "slots": {"default": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "<PERSON>v<PERSON><PERSON><PERSON>", "designer": {"movein": false, "moveout": false}, "props": {"title": "驳回意见：", "tag": "h4"}, "style": "padding-top:12px;padding-bottom:12px;padding-left:8px;font-weight:normal;color:#000000;margin-top:8px;width:100%;font-size:16px;"}, {"type": "IvText", "props": {"maxLine": 0, "text": "${orgData.data.approvalOpinion}"}, "style": "margin-left:80px;"}], "style": "", "class": "", "id": "", "visible": "${orgData.data.status == 2 }", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "style": "", "class": "", "id": "form3", "visible": "", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "events": {"onValidate": {"actions": [{"type": "Action.ValidateForm", "inputs": {"formId": "form1", "description": null}, "id": "action-390"}]}}}, {"type": "IvSpin", "props": {"type": "style1", "text": "复核中...", "fix": true}, "style": "", "class": "", "id": "", "visible": "${!canOpera}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "header": [{"type": "IvText", "props": {"text": "组织复核", "showTitle": false, "maxline": 0}, "style": "font-size:16px;color:#000000;width:100%;padding-top:12px;padding-bottom:12px;"}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "关闭", "type": "default", "size": "default"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {"terminate": true, "description": null}, "id": "action-25", "activeOn": null}]}}}, {"type": "IvButton", "props": {"text": "通过", "type": "primary", "size": "default", "long": false, "disabled": "${!canOpera}"}, "events": {"click": "PEKAhkIo97K"}, "style": "", "class": "", "id": "", "visible": "${type!='detail'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}, {"type": "IvButton", "props": {"text": "驳回", "type": "primary", "size": "default", "long": false, "disabled": "${!canOpera}"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.check_admin.bohui<PERSON><PERSON>u", "requestParams": [{"_uid": "281", "name": "id", "value": "${request.params.id}"}]}, "description": null, "id": "action-280"}, {"type": "Action.CloseDialog", "inputs": {"description": null}, "id": "action-67"}]}}, "style": "text-align:right;", "class": "", "id": "", "visible": "${type!='detail'}", "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "style": "text-align:right;padding-top:12px;padding-right:16px;padding-bottom:12px;"}], "meta": {"title": "组织复核", "name": "edit_org", "packageName": "tenant.org_admin"}}