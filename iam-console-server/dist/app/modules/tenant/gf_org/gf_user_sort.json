{"type": "AdaptivePage", "version": "2.0", "dataSources": [], "variables": {"userList": {"type": "array", "default": "${request.params.userList}", "source": "request", "optionSet": null, "entity": null, "title": "用户列表", "orderNo": 0}, "userOldOANames": {"type": "string", "default": "", "title": "旧用户排序列表", "orderNo": 1}, "copyContent": {"type": "string", "default": "", "title": "复制内容", "orderNo": 2}, "userNewOANames": {"type": "string", "default": "", "title": "用户新排序字符串", "orderNo": 3}}, "functions": {"transformed": false, "script": "export function copy() {\r\n  renderContext.variables.copyContent.value = renderContext.variables.userOldOANames.value\r\n  return utils.copy(renderContext.variables.userOldOANames)\r\n}\r\nexport function prase() {\r\n  console.log(\"prase\", renderContext.variables.copyContent)\r\n  renderContext.variables.userNewOANames.value = renderContext.variables.copyContent.value\r\n}\r\nexport function setMemeberStr() {\r\n  console.log(\"userlist\", renderContext.variables.userList);\r\n  renderContext.variables.userOldOANames.value = renderContext.variables.userList.value.map(v => {\r\n    if (v.oaName != null && v.oaName != '')\r\n      return v.oaName;\r\n    else if (v.username != null && v.username != '')\r\n      return v.username;\r\n    else return v.name;\r\n  }).join(\"\\n\")\r\n  console.log(\"oldOANames\", renderContext.variables.userOldOANames)\r\n}\r\nexport function userNewSort() {\r\n  console.log(\"newUserNames\", renderContext.variables.userNewOANames);\r\n  var str = renderContext.variables.userNewOANames.value;\r\n  let newUsers = str.split(\"\\n\");\r\n  renderContext.variables.userList.value.sort((a, b) => {\r\n    var comparea = (a.oaName != null && a.oaName != '') ? a.oaName : a.username;\r\n    var compareb = (b.oaName != null && b.oaName != '') ? b.oaName : b.username;\r\n    console.log(comparea, \"comparea\")\r\n    console.log(compareb, \"compareb\")\r\n    console.log(newUsers.indexOf(comparea), newUsers.indexOf(compareb))\r\n    return newUsers.indexOf(comparea) - newUsers.indexOf(compareb)\r\n  })\r\n  renderContext.variables.userList = renderContext.variables.userList.value\r\n  console.log(\"newUserSorg\", renderContext.variables.userList)\r\n}"}, "events": {"on-rendered": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "setMemeberStr", "params": []}, "description": null, "id": "action-135"}]}, "on-render": {"actions": []}, "on-destroy": {"actions": []}}, "body": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "style": "", "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "原排序："}, "style": "width:100px;text-align:right;padding-top:5px;"}, {"type": "Input", "designer": {"movein": false}, "props": {"type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 10, "wrap": "soft", "autocomplete": "off", "autosize": false, "value": "${userOldOANames}", "readonly": true}, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}}], "style": "display:flex;"}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 12}, "children": [{"type": "IvText", "props": {"maxLine": 0, "text": "目标排序："}, "style": "width:140px;padding-top:5px;text-align:right;"}, {"type": "Input", "designer": {"movein": false}, "props": {"placeholder": "请输入", "type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 10, "wrap": "soft", "autocomplete": "off", "autosize": false, "value": "${userNewOANames}"}, "style": ""}], "style": "margin:0px;display:flex;"}], "props": {"customCol": "12:12", "gutter": 16, "wrap": true}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "复制", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "copy", "params": [], "description": null}, "description": null, "id": "action-116", "activeOn": null}]}, "click.stop": {"actions": []}}, "style": "margin-right:8px;margin-left:110px;"}, {"type": "IvButton", "props": {"text": "粘贴", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "prase", "params": []}, "description": null, "id": "action-46"}]}, "click.stop": {"actions": []}}, "style": "margin-left:8px;"}], "style": "display:flex;justify-content:center;margin-top:20px;"}], "footer": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}, "children": [{"type": "Row", "designer": {"movein": false, "moveout": false}, "children": [{"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 6}}, {"type": "Col", "designer": {"move": false, "moveSibling": true}, "props": {"span": 18}, "children": [{"type": "IvButton", "props": {"text": "取消", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "style": "margin-right:12px;", "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-83"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "确定", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action<PERSON>", "inputs": {"funcType": "page", "script": "userNewSort", "params": []}, "description": null, "id": "action-56"}, {"type": "Action.Output", "inputs": {"output": "${userList.map(v=>v.id)}", "description": null}, "description": null, "id": "action-75", "activeOn": null}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-73"}]}, "click.stop": {"actions": []}}}]}], "props": {"customCol": "6:18", "gutter": 16, "wrap": true}}]}], "props": {"customCol": "6:6:6:6", "gutter": 16, "wrap": true}}], "meta": {"title": "用户排序", "platform": "pc"}}