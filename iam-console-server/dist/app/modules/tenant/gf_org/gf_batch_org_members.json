{"type": "AdaptivePage", "version": "2.0", "variables": {"importMsg": {"type": "string", "default": "", "title": "上传状态信息", "orderNo": 0}, "res": {"type": "object", "default": null, "title": "上传返回实体", "orderNo": 1}}, "body": [{"type": "IvUpload", "designer": {"movein": false}, "props": {"action": "${'/iamconsole/api/tenant/user/extend/import/orgs'}", "name": "file", "multipleUpload": false, "multiple": false, "withCredentials": false, "showUploadList": true, "headers": {"Authorization": "${'Bearer ' + utils.user.token}"}, "type": "select", "text": "上传", "dragText": "将文件拖到此处，或点击上传", "buttonType": "primary", "icon": "ios-cloud-upload"}, "events": {"on-progress": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "50", "name": "importMsg", "value": "${'上传中'}"}]}, "description": null, "id": "action-49"}]}, "on-success": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "54", "name": "res", "value": "${$event.response}"}], "description": null}, "description": null, "id": "action-53", "activeOn": null}, {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "46", "name": "importMsg", "value": "${`上传成功，导入成功：${res.success},导入失败：${res.fail}`}"}]}, "description": null, "id": "action-45"}]}, "on-error": {"actions": [{"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "52", "name": "importMsg", "value": "${'上传失败'}"}]}, "description": null, "id": "action-51"}]}, "on-preview": {"actions": []}, "on-remove": {"actions": []}, "on-format-error": {"actions": []}, "on-exceeded-size": {"actions": []}}}, {"type": "IvLink", "props": {"tag": "a", "type": "download", "linkColor": true, "text": "批量导入组织模版"}, "events": {"click": {"actions": [{"type": "Action.HttpDownload", "inputs": {"url": "/api/tenant/file/download/org", "filename": "批量导入组织模版.xlsx"}, "description": null, "id": "action-87"}]}, "click.stop": {"actions": []}}}, {"type": "IvText", "props": {"maxLine": 0, "text": "${importMsg}"}}, {"type": "IvTable", "designer": {"movein": false, "moveout": false}, "children": [{"type": "IvTableColumn", "props": {"title": "匹配結果", "keyName": "errorMessage"}, "designer": {"movein": false}}, {"type": "IvTableColumn", "props": {"title": "组织名", "keyName": "name", "width": 200}, "designer": {"movein": false}}, {"designer": {"movein": false}, "type": "IvTableColumn", "props": {"value": "newValue_44", "title": "组织账户名", "keyName": "simpleSpell", "width": 200, "align": "left"}}], "props": {"data": "${res.orgEntityList}", "columns": [], "noDataText": "暂无数据", "sumText": "合计", "indentSize": 16, "noFilteredDataText": "暂无筛选结果", "size": "default", "showHeader": true, "tooltipTheme": "dark", "tooltipMaxWidth": 300, "fixedShadow": "show"}}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "返回", "type": "default", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-71"}]}, "click.stop": {"actions": []}}}, {"type": "IvButton", "props": {"text": "导入", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": {"actions": [{"type": "Action.Output", "inputs": {"output": "${res.orgEntityList}", "description": null}, "description": null, "id": "action-65", "activeOn": null}, {"type": "Action.CloseDialog", "inputs": {}, "description": null, "id": "action-69"}]}, "click.stop": {"actions": []}}}], "style": "width:100%;text-align:right;padding-top:12px;padding-bottom:12px;"}], "meta": {"title": "批量导入组织成员", "platform": "pc"}}