{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "list", "multiple": true, "lazy": false, "autoReload": true, "url": "/extend/config/list", "method": "POST"}], "body": [{"type": "IvTableGrid", "designer": {"movein": false, "moveChild": false}, "props": {"loadOnMounted": false, "data": [], "tableCols": [], "enableSelectionCol": false, "enableIndexCol": true, "visibleHeaderOp": false, "visibleMore": false, "padding": 16, "size": "default", "stripe": false, "border": false, "show-header": true, "loading": false, "disabled-hover": false, "highlight-row": false, "select-row-single": false, "draggable": false, "no-data-text": "暂无数据", "no-filtered-data-text": "暂无筛选结果", "indentSize": 16, "visiblePage": false, "pagerProps": {"pageSize": 10, "pageSizeOpts": ["10", "20", "30", "40", "50"], "showTotal": true, "showElevator": true, "showSizer": true, "simple": false, "current": 1}, "autoReload": true, "ds": "${list}"}, "mockComp": false, "style": "", "class": "", "id": "", "visible": true, "loop": {"data": "", "variableName": "loopItem", "indexName": "loopIndex", "key": ""}, "slots": {"header": {"children": [], "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}, "default": {"children": [{"type": "IvTableGridColumn", "props": {"title": "配置", "keyName": "name", "width": 300, "show": true, "children": [], "dataDictionary": "SysConfig"}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvText", "props": {"text": "${utils.optionSet.getTitleByValue(tableCell.column.dataDictionary, tableCell.row[tableCell.column.key])}", "maxLine": 0}, "designer": {"demo": {"props.text": "限制普通用户登录"}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}, {"type": "IvTableGridColumn", "props": {"title": "值", "keyName": "value", "show": true, "children": []}, "designer": {"movein": false}}, {"type": "IvTableGridColumn", "props": {"title": "操作", "width": 140, "keyName": "action", "show": true, "align": "center", "fixed": "right"}, "designer": {"movein": false}, "slots": {"content": {"children": [{"type": "IvLink", "props": {"tag": "a", "type": "page", "linkColor": true, "text": "修改"}, "events": {"click": {"actions": [{"type": "Action.OpenInDialog", "inputs": {"uri": "tenant.setting.zuhuanquancelve.gf_edit_config", "requestParams": [{"_uid": "78", "name": "name", "value": "${tableCell.row.name}"}, {"_uid": "93", "name": "value", "value": "${tableCell.row.value}"}], "dialogSettings": {"width": "800", "title": "修改配置", "continueOnClose": "${true}"}, "description": null}, "description": null, "id": "action-77", "activeOn": null}, {"type": "Action.DataSourceDataReload", "inputs": {"dataSource": "${list}"}, "description": null, "id": "action-70"}]}, "click.stop": {"actions": []}}}], "context": {"name": "tableCell"}, "designer": {"move": false, "copy": false, "moveSibling": false, "select": true, "del": false, "dynamic": false}}}}]}}}], "master": "tenant.setting.zuhuanquancelve.zuhuyingyonganquanmoban", "meta": {"title": "系统配置", "platform": "pc", "type": "view"}}