{"type": "AdaptivePage", "version": "2.0", "dataSources": [], "variables": {"p1": {"type": "string", "default": "2323", "title": "2323", "orderNo": 0}, "p1_1654744670456": {"type": "string", "default": "2323", "title": "2323", "orderNo": 1}, "showPanel": {"type": "boolean", "default": true, "title": "是否显示区域", "orderNo": 2}, "showPanel_325576": {"type": "boolean", "default": true, "title": "是否显示区域", "orderNo": 3}, "showPanel_495479": {"type": "boolean", "default": true, "title": "是否显示区域", "orderNo": 4}, "pppppp": {"type": "string", "default": "", "title": "", "orderNo": 5}, "formData": {"type": "object", "default": {"name": "", "gender": "", "birthday": "", "hobby": "", "city": "", "rules": []}, "title": "表单对象", "orderNo": 6}, "p2": {"type": "string", "default": "", "title": "", "orderNo": 7}, "p2_1654744670462": {"type": "string", "default": "", "title": "", "orderNo": 8}, "selectOrgId": {"type": "string", "default": "pU3Lbn9brixcR9gLgK4jfg", "title": "selectOrgId", "orderNo": 9}}, "functions": {"transformed": false, "script": "export function cc() {\n\n  console.log(111)\n}\n"}, "orchestrations": {"MWSxOPkS8": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-156"}, "action-156": {"type": "Action.SetPageVariable", "inputs": {"variables": [{"_uid": "157", "name": "selectOrgId", "value": "${''}"}], "description": null}, "next": "action-135"}, "action-135": {"type": "Action.Message", "inputs": {"notifyType": "info", "content": "${utils.JSON.stringify(formData.rules)}", "duration": 1.5, "closable": false, "background": false}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}}, "body": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "designer": {"moveChild": false, "movein": false}, "children": [{"type": "IvStepPane", "props": {"title": "步骤1"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "按钮", "type": "default", "size": "default"}}]}]}, {"type": "IvStepPane", "props": {"title": "步骤2"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "上一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "previous"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}]}]}, {"type": "IvStepPane", "props": {"title": "步骤3"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "上一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "previous"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}, {"type": "Icon", "props": {"type": "md-search", "size": 16}}]}]}, {"type": "IvStepPane", "props": {"title": "步骤4"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "上一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "previous"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}]}]}, {"type": "IvStepPane", "props": {"title": "步骤5"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "上一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "previous"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}]}]}, {"type": "IvStepPane", "props": {"title": "步骤6"}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "上一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "previous"}, "id": "action-1"}]}}}, {"type": "IvButton", "props": {"text": "下一步"}, "events": {"click": {"actions": [{"type": "Action.WizardChangeStep", "inputs": {"stepDirection": "next"}, "id": "action-1"}]}}}]}]}], "props": {"size": "default", "headerNav": false}}, {"type": "IvImage", "props": {"width": "160", "height": "90", "fit": "cover", "round": "0"}}, {"type": "OrganModel", "props": {"col": "24", "labelWidth": "80px", "labelTitleAlign": "right", "title": "测试"}}, {"type": "OrganModel", "props": {"col": 24, "labelWidth": "80px", "labelTitleAlign": "right"}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px;"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "style": "margin-bottom:16px", "children": [{"type": "OrganModel", "props": {"col": 24, "labelWidth": "80px", "labelTitleAlign": "right"}}], "designer": {"collapse": true}}, {"type": "IamMemberSelect", "props": {"text": "选择成员", "type": "default", "icon": "md-add", "multiple": true, "disabled": false, "showSelect": false, "placeholder": "根据用户名搜索", "modalTitle": "选择用户", "modalWidth": 650}}, {"type": "IamOrgSelect", "props": {"text": "选择组织", "type": "default", "icon": "md-add", "multiple": true, "disabled": false, "disabledChild": false, "placeholder": "根据组织搜索", "modalTitle": "选择组织", "modalWidth": 650}}, {"type": "IamOrg<PERSON>ree", "props": {"placeholder": "根据组织搜索", "selectedId": "${selectOrgId}"}}, {"type": "IvForm", "props": {"model": "${{}}", "labelPosition": "right", "labelWidth": 100, "labelColon": false, "showMessage": true, "hideRequiredMark": false, "disabled": false, "prevent": true}, "children": [{"type": "IvFormCheckbox", "designer": {"movein": false}, "props": {"labelName": "表单复选框", "span": "24", "width": "100%", "value": "${formData.rules}", "size": "default", "vertical": false, "data": [{"label": "多选框0", "value": "createmock_4"}, {"label": "多选框1", "value": "createmock_5"}], "keyNames": {}, "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"type": "required", "value": "required", "label": "必填", "message": "此项为必填项"}]}}}]}, {"type": "IvButton", "props": {"text": "按钮", "type": "default", "size": "default"}, "events": {"click": "MWSxOPkS8"}}], "meta": {"title": "222444", "name": "222", "packageName": "tenant"}}