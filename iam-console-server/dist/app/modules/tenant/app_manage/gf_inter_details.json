{"type": "AdaptivePage", "version": "2.0", "dataSources": [{"id": "details", "title": "查询日志详情接口", "multiple": false, "schema": {"id": {"type": "string", "title": "id"}, "clientName": {"type": "string", "title": "客户端name"}, "clientId": {"type": "string", "title": "客户端id"}, "code": {"type": "string", "title": "接口编码"}, "name": {"type": "string", "title": "接口名称"}, "transferReport": {"type": "string", "title": "传输报文"}, "receiveReport": {"type": "string", "title": "接收报文"}, "status": {"type": "integer", "title": "状态"}, "tenantId": {"type": "string", "title": "所属租户ID"}, "createdAt": {"type": "string", "title": "创建时间"}, "createdBy": {"type": "string", "title": "创建人"}, "updatedAt": {"type": "string", "title": "更新时间"}, "updatedBy": {"type": "string", "title": "更新人"}, "properties": {"type": "object"}}, "lazy": false, "autoReload": true, "url": {"type": "ServicePath", "source": "local", "value": "/api/tenant/app/clientDataLog/details", "processor": "ServicePathProcessor"}, "method": "GET", "params": {"id": "${id}"}}], "variables": {"id": {"type": "string", "default": "${request.params.id}", "source": "request", "optionSet": null, "entity": null, "title": "日志id", "orderNo": 0}}, "orchestrations": {"LT22aAjpq2Y": {"actions": {"start": {"type": "Action.Start", "inputs": {}, "next": "action-54"}, "action-54": {"type": "Action.CloseDialog", "inputs": {}, "next": "end"}, "end": {"type": "Action.End", "inputs": {}, "next": null}}}}, "body": [{"type": "IvForm", "props": {"model": "${{}}", "rowspace": 16, "labelPosition": "right", "labelWidth": 100, "labelColon": true, "showMessage": true, "prevent": true}, "children": [{"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "调用方名称", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${details.data.clientName}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormDatePicker", "designer": {"movein": false}, "props": {"labelName": "日志日期", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "type": "date", "options": {"disabledDate": "function anonymous(\n) {\n\n}", "shortcuts": []}, "size": "default", "placement": "bottom-start", "clearable": true, "editable": true, "capture": true, "transfer": true, "value": "${details.data.createdAt}", "format": "yyyy-MM-dd", "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "调用接口", "type": "text", "size": "default", "border": true, "clearable": true, "rows": 4, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "value": "${details.data.name}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormSelect", "designer": {"movein": false}, "props": {"labelName": "请求状态", "notFoundText": "无匹配数据", "data": "${utils.optionSet.find('InterfaceStatus').items}", "keyNames": {"label": "title"}, "size": "default", "placement": "bottom-start", "clearable": true, "maxTagCount": 3, "loadingText": "加载中", "labelInValue": true, "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "transfer": true, "value": "${details.data.status}", "disabled": true, "dataDictionary": "InterfaceStatus", "dataDictionaryDisabled": [], "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "响应内容", "type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 10, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "autosize": false, "value": "${details.data.transferReport}", "disabled": true, "labelWidth": 140}}, {"type": "IvFormInput", "designer": {"movein": false}, "props": {"labelName": "请求信息", "type": "textarea", "size": "default", "border": true, "clearable": true, "rows": 9, "wrap": "soft", "autocomplete": "off", "span": "24", "rules": {"type": "ValidateRules", "processor": "iview_processor", "value": [{"label": "必填", "type": "required", "value": "required", "message": "此项为必填项", "required": false}]}, "width": "100%", "show-message": true, "autosize": false, "value": "${details.data.receiveReport}", "disabled": true, "labelWidth": 140}}], "style": "padding:12px;"}], "footer": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"type": "IvButton", "props": {"text": "关闭", "type": "primary", "size": "default", "shape": "default", "target": "_self"}, "events": {"click": "LT22aAjpq2Y", "click.stop": {"actions": []}}}], "style": "text-align:right;padding:12px;"}], "meta": {"title": "接口日志详情页面"}}